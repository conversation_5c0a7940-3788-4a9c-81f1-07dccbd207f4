import { UserService } from '@/api/usersApi'
import type {
  CrudTableConfig,
  BatchAction,
  ExportConfig,
  PermissionConfig,
  SecurityConfig,
  PerformanceConfig,
  ResponsiveConfig
} from '@/components/core/tables/ArtCrudTable.vue'

// 过滤空值参数的工具函数
const filterEmptyParams = (params: Record<string, any>) => {
  const filtered: Record<string, any> = {}
  Object.keys(params).forEach((key) => {
    const value = params[key]
    // 只保留非空值：非空字符串、非null、非undefined、数字0、布尔值false
    if (value !== '' && value !== null && value !== undefined) {
      filtered[key] = value
    }
  })
  return filtered
}

// 包装API服务，添加参数过滤
const createNoticeApiService = () => ({
  getList: (params: any) => {
    // 过滤空值参数，解决URL中出现空值的问题
    const filteredParams = filterEmptyParams(params)
    console.log('Notice API - 过滤后的请求参数:', filteredParams)
    return UserService.getUserNoticeList(filteredParams)
  },
  delete: (id: any) => UserService.deleteNotice(id),
  batchDelete: (ids: any[]) => UserService.batchDeleteNotices(ids),
  update: (id: any, data: any) => UserService.updateNotice(id, data),
  create: (data: any) => UserService.createNotice(data)
})

// 批量操作配置
const batchActions: BatchAction[] = [
  {
    label: '批量标记已读',
    key: 'batchMarkRead',
    type: 'success',
    icon: 'Check',
    confirm: '确认标记选中的 {count} 个通知为已读吗？',
    handler: async (selectedRows) => {
      console.log('批量标记已读:', selectedRows)
      await Promise.all(selectedRows.map((row) => UserService.markNoticeAsRead(row.id as number)))
    }
  },
  {
    label: '批量设为置顶',
    key: 'batchPin',
    type: 'warning',
    icon: 'Top',
    confirm: '确认将选中的 {count} 个通知设为置顶吗？',
    handler: async (selectedRows) => {
      console.log('批量设为置顶:', selectedRows)
      await Promise.all(
        selectedRows.map((row) => UserService.updateNotice(row.id as number, { status: 2 }))
      )
    }
  },
  {
    label: '批量隐藏',
    key: 'batchHide',
    type: 'danger',
    icon: 'Hide',
    confirm: '确认隐藏选中的 {count} 个通知吗？',
    handler: async (selectedRows) => {
      console.log('批量隐藏通知:', selectedRows)
      await Promise.all(
        selectedRows.map((row) => UserService.updateNotice(row.id as number, { status: 3 }))
      )
    }
  },
  {
    label: '批量导出',
    key: 'batchExport',
    type: 'primary',
    icon: 'Download',
    handler: async (selectedRows) => {
      console.log('批量导出通知:', selectedRows)
      const exportData = selectedRows.map((row) => ({
        id: row.id as number,
        sender_id: row.sender_id as number,
        receiver_id: row.receiver_id as number,
        title: row.title as string,
        summary: row.summary as string,
        type: getTypeText(row.type as number),
        priority: getPriorityText(row.priority as number),
        status: getStatusText(row.status as number),
        is_read: row.is_read ? '已读' : '未读',
        read_at: row.read_at as string,
        created_at: row.created_at as string
      }))
      console.log('导出数据:', exportData)
    }
  }
]

// 通知类型文本转换
export const getTypeText = (type: number) => {
  const typeMap: Record<number, string> = {
    1: '系统通知',
    2: '订单通知',
    3: '账户通知',
    4: '活动通知',
    5: '消息通知',
    6: '安全通知'
  }
  return typeMap[type] || '系统通知'
}

// 通知状态文本转换
export const getStatusText = (status: number) => {
  const statusMap: Record<number, string> = {
    1: '正常',
    2: '置顶',
    3: '隐藏',
    4: '失效'
  }
  return statusMap[status] || '正常'
}

// 优先级文本转换
export const getPriorityText = (priority: number) => {
  const priorityMap: Record<number, string> = {
    1: '低优先级',
    2: '普通优先级',
    3: '高优先级',
    4: '紧急优先级'
  }
  return priorityMap[priority] || '普通优先级'
}

// 发送者文本转换
export const getSenderText = (senderId: number) => {
  return senderId === 0 ? '系统' : `用户${senderId}`
}

// 导出配置
const exportConfig: ExportConfig = {
  enabled: true,
  filename: '用户通知数据导出',
  columns: [
    'id',
    'sender_id',
    'receiver_id',
    'title',
    'summary',
    'type',
    'priority',
    'status',
    'is_read',
    'read_at',
    'created_at'
  ],
  formats: ['excel', 'csv'],
  maxRows: 1000
}

// 权限配置
const permissionConfig: PermissionConfig = {
  create: () => {
    return true
  },
  update: (row) => {
    // 失效的通知不允许编辑
    return (row.status as number) !== 4
  },
  delete: (row) => {
    // 系统通知不允许删除
    return (row.sender_id as number) !== 0
  },
  export: () => true,
  batchDelete: () => true
}

// 响应式配置
const responsiveConfig: ResponsiveConfig = {
  mobile: {
    hideColumns: ['summary', 'route', 'read_at', 'sender_id'],
    cardView: false,
    breakpoint: 768
  }
}

// 安全配置
const securityConfig: SecurityConfig = {
  enableXssProtection: true,
  allowedTags: ['p', 'br', 'strong', 'em', 'a'],
  allowedAttributes: {
    div: ['class'],
    span: ['class'],
    a: ['href', 'target']
  }
}

// 性能配置
const performanceConfig: PerformanceConfig = {
  enableCache: true,
  cacheTime: 5 * 60 * 1000, // 5分钟
  debounceTime: 500,
  maxCacheSize: 50,
  lazyLoad: true,
  virtualScroll: {
    enabled: true,
    itemHeight: 60,
    buffer: 10
  }
}

// 用户通知管理配置
export const noticeTableConfig: CrudTableConfig = {
  title: '用户通知',
  addButtonText: '新增通知',
  deleteButtonText: '删除通知',
  deleteConfirmText: '确定要删除该通知吗？',
  batchDeleteConfirmText: '确定要删除选中的 {count} 条通知吗？',
  emptyHeight: '500px',

  initialSearchState: {
    sender_id: '',
    receiver_id: '',
    title: '',
    type: '',
    priority: '',
    status: '',
    is_read: '',
    startTime: '',
    endTime: ''
  },

  searchFormItems: [
    {
      key: 'sender_id',
      label: '发送者ID',
      type: 'input',
      placeholder: '请输入发送者ID (0为系统)'
    },
    {
      key: 'receiver_id',
      label: '接收者ID',
      type: 'input',
      placeholder: '请输入接收者ID'
    },
    {
      key: 'title',
      label: '通知标题',
      type: 'input',
      placeholder: '请输入通知标题'
    },
    {
      key: 'type',
      label: '通知类型',
      type: 'select',
      placeholder: '请选择通知类型',
      options: [
        { label: '系统通知', value: '1' },
        { label: '订单通知', value: '2' },
        { label: '账户通知', value: '3' },
        { label: '活动通知', value: '4' },
        { label: '消息通知', value: '5' },
        { label: '安全通知', value: '6' }
      ]
    },
    {
      key: 'priority',
      label: '优先级',
      type: 'select',
      placeholder: '请选择优先级',
      options: [
        { label: '低优先级', value: '1' },
        { label: '普通优先级', value: '2' },
        { label: '高优先级', value: '3' },
        { label: '紧急优先级', value: '4' }
      ]
    },
    {
      key: 'status',
      label: '状态',
      type: 'select',
      placeholder: '请选择状态',
      options: [
        { label: '正常', value: '1' },
        { label: '置顶', value: '2' },
        { label: '隐藏', value: '3' },
        { label: '失效', value: '4' }
      ]
    },
    {
      key: 'is_read',
      label: '阅读状态',
      type: 'select',
      placeholder: '请选择阅读状态',
      options: [
        { label: '未读', value: 'false' },
        { label: '已读', value: 'true' }
      ]
    },
    {
      key: 'startTime',
      label: '开始时间',
      type: 'date',
      placeholder: '请选择开始时间'
    },
    {
      key: 'endTime',
      label: '结束时间',
      type: 'date',
      placeholder: '请选择结束时间'
    }
  ],

  tableColumns: [
    { type: 'selection', width: 55 },
    { label: 'ID', prop: 'id', width: 80, sortable: true },
    {
      label: '发送者',
      prop: 'sender_id',
      width: 100,
      useSlot: true,
      slotName: 'sender'
    },
    {
      label: '接收者',
      prop: 'receiver',
      minWidth: 120,
      useSlot: true,
      slotName: 'receiver'
    },
    {
      label: '通知标题',
      prop: 'title',
      minWidth: 200,
      showOverflowTooltip: true,
      columnType: 'copy'
    },
    {
      label: '摘要',
      prop: 'summary',
      minWidth: 250,
      showOverflowTooltip: true
    },
    {
      label: '通知类型',
      prop: 'type',
      width: 100,
      useSlot: true,
      slotName: 'type'
    },
    {
      label: '优先级',
      prop: 'priority',
      width: 100,
      useSlot: true,
      slotName: 'priority'
    },
    {
      label: '状态',
      prop: 'status',
      width: 80,
      useSlot: true,
      slotName: 'status'
    },
    {
      label: '阅读状态',
      prop: 'is_read',
      width: 100,
      useSlot: true,
      slotName: 'readStatus'
    },
    {
      label: '阅读时间',
      prop: 'read_at',
      minWidth: 160,
      sortable: true,
      columnType: 'date'
    },
    {
      label: '路由',
      prop: 'route',
      minWidth: 150,
      showOverflowTooltip: true
    },
    {
      label: '创建时间',
      prop: 'created_at',
      minWidth: 160,
      sortable: true,
      columnType: 'date'
    },
    {
      label: '操作',
      width: 250,
      fixed: 'right',
      useSlot: true,
      slotName: 'actions'
    }
  ],

  apiService: createNoticeApiService(),

  // 分页参数映射
  paginationKey: {
    current: 'page',
    size: 'pageSize'
  },

  dialog: {
    title: {
      add: '添加通知',
      edit: '编辑通知'
    },
    width: '900px',
    formItems: [
      {
        label: '发送者ID',
        prop: 'sender_id',
        type: 'input',
        required: true,
        config: {
          placeholder: '请输入发送者ID，0表示系统'
        },
        rules: [{ required: true, message: '请输入发送者ID', trigger: 'blur' }],
        span: 12
      },
      {
        label: '接收者ID',
        prop: 'receiver_id',
        type: 'input',
        required: true,
        config: {
          placeholder: '请输入接收者ID'
        },
        rules: [{ required: true, message: '请输入接收者ID', trigger: 'blur' }],
        span: 12
      },
      {
        label: '通知类型',
        prop: 'type',
        type: 'select',
        required: true,
        options: [
          { label: '系统通知', value: 1 },
          { label: '订单通知', value: 2 },
          { label: '账户通知', value: 3 },
          { label: '活动通知', value: 4 },
          { label: '消息通知', value: 5 },
          { label: '安全通知', value: 6 }
        ],
        span: 12
      },
      {
        label: '优先级',
        prop: 'priority',
        type: 'select',
        required: true,
        options: [
          { label: '低优先级', value: 1 },
          { label: '普通优先级', value: 2 },
          { label: '高优先级', value: 3 },
          { label: '紧急优先级', value: 4 }
        ],
        span: 12
      },
      {
        label: '通知标题',
        prop: 'title',
        type: 'input',
        required: true,
        config: {
          placeholder: '请输入通知标题',
          maxlength: 255
        },
        rules: [
          { required: true, message: '请输入通知标题', trigger: 'blur' },
          { max: 255, message: '通知标题长度不能超过 255 个字符', trigger: 'blur' }
        ],
        span: 24
      },
      {
        label: '通知摘要',
        prop: 'summary',
        type: 'input',
        config: {
          placeholder: '请输入通知摘要',
          maxlength: 500
        },
        rules: [{ max: 500, message: '通知摘要长度不能超过 500 个字符', trigger: 'blur' }],
        span: 24
      },
      {
        label: '通知内容',
        prop: 'content',
        type: 'textarea',
        required: true,
        config: {
          placeholder: '请输入通知内容',
          rows: 6
        },
        rules: [{ required: true, message: '请输入通知内容', trigger: 'blur' }],
        span: 24
      },
      {
        label: '路由地址',
        prop: 'route',
        type: 'input',
        config: {
          placeholder: '请输入路由地址，如：/user/profile',
          maxlength: 255
        },
        rules: [{ max: 255, message: '路由地址长度不能超过 255 个字符', trigger: 'blur' }],
        span: 24
      },
      {
        label: '状态',
        prop: 'status',
        type: 'select',
        required: true,
        options: [
          { label: '正常', value: 1 },
          { label: '置顶', value: 2 },
          { label: '隐藏', value: 3 },
          { label: '失效', value: 4 }
        ],
        span: 12
      }
    ],
    initialFormData: {
      sender_id: 0,
      receiver_id: '',
      type: 1,
      priority: 2,
      title: '',
      summary: '',
      content: '',
      route: '',
      status: 1
    }
  },

  // 功能配置
  batchActions,
  export: exportConfig,
  permissions: permissionConfig,
  responsive: responsiveConfig,
  security: securityConfig,
  performance: performanceConfig,

  // 状态切换配置
  hasStatusSwitch: false, // 通知状态不适合简单的开关切换

  // 操作权限
  allowBatchDelete: true
}
