import request from '@/utils/http'

export class UserBillService {
  private static baseUrl = '/wallet/bill'

  // 账单类型常量 - 收入类型（正数）
  static readonly BILL_TYPE_DEPOSIT = 1 // 充值
  static readonly BILL_TYPE_SYSTEM_REWARD = 2 // 系统奖励
  static readonly BILL_TYPE_REGISTER_REWARD = 3 // 注册奖励
  static readonly BILL_TYPE_INVITE_REWARD = 4 // 邀请奖励
  static readonly BILL_TYPE_DISTRIBUTION_REWARD = 5 // 分销奖励
  static readonly BILL_TYPE_BALANCE_UNFREEZE = 6 // 余额解冻
  static readonly BILL_TYPE_SYSTEM_ADDITION = 7 // 系统加款
  static readonly BILL_TYPE_WITHDRAWAL_REJECT = 8 // 提现拒绝
  static readonly BILL_TYPE_PRODUCT_REFUND = 9 // 产品退款
  static readonly BILL_TYPE_PRODUCT_EARNINGS = 10 // 产品收益
  static readonly BILL_TYPE_TRANSFER_RECEIVE = 11 // 转账接收
  static readonly BILL_TYPE_SWAPS_RECEIVE = 12 // 闪兑接收

  // 账单类型常量 - 支出类型（负数）
  static readonly BILL_TYPE_WITHDRAWAL = -1 // 提现
  static readonly BILL_TYPE_SYSTEM_DEDUCTION = -2 // 系统扣款
  static readonly BILL_TYPE_PRODUCT_PURCHASE = -3 // 购买产品
  static readonly BILL_TYPE_MEMBERSHIP_PURCHASE = -4 // 购买会员
  static readonly BILL_TYPE_BALANCE_FREEZE = -5 // 余额冻结
  static readonly BILL_TYPE_TRANSFER_SEND = -6 // 转账发送
  static readonly BILL_TYPE_SWAPS_SEND = -7 // 闪兑发送

  // ==================== 基础 CRUD ====================

  // 获取用户账单列表
  static getUserBillList(params?: any) {
    return request.get<any>({
      url: this.baseUrl,
      params
    })
  }

  // 获取用户账单详情
  static getUserBillDetail(id: number) {
    return request.get<any>({
      url: `${this.baseUrl}/${id}`
    })
  }

  // 创建用户账单
  static createUserBill(data: any) {
    return request.post<any>({
      url: this.baseUrl,
      data
    })
  }

  // 更新用户账单
  static updateUserBill(id: number, data: any) {
    return request.put<any>({
      url: `${this.baseUrl}/${id}`,
      data
    })
  }

  // 删除用户账单
  static deleteUserBill(id: number) {
    return request.del<any>({
      url: `${this.baseUrl}/${id}`
    })
  }

  // 批量删除用户账单
  static batchDeleteUserBills(ids: number[]) {
    return request.del<any>({
      url: `${this.baseUrl}/batch`,
      data: { ids }
    })
  }

  // 批量验证账单
  static batchVerifyBills(ids: number[]) {
    return request.post<any>({
      url: `${this.baseUrl}/batch/verify`,
      data: { ids }
    })
  }

  // 批量重新计算余额
  static batchRecalculateBalance(ids: number[]) {
    return request.put<any>({
      url: `${this.baseUrl}/batch/recalculate`,
      data: { ids }
    })
  }
}
