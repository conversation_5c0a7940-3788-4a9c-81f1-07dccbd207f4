import request from '@/utils/http'

/**
 * 支付管理服务
 * 类型：1 银行卡 2 加密货币 3 三方支付
 * 模式：1 充值 2 提现
 * 状态：1 启用 0 禁用
 */
export class SystemPaymentService {
  private static baseUrl = '/system/payments'

  static readonly TYPE_BANK = 1
  static readonly TYPE_CRYPTO = 2
  static readonly TYPE_THIRD = 3

  static readonly MODE_DEPOSIT = 1
  static readonly MODE_WITHDRAW = 2

  static readonly STATUS_ENABLED = 1
  static readonly STATUS_DISABLED = 0

  // ============ 基础 CRUD ============
  static getList(params?: any) {
    return request.get<any>({ url: this.baseUrl, params })
  }

  static getDetail(id: number) {
    return request.get<any>({ url: `${this.baseUrl}/${id}` })
  }

  static create(data: any) {
    return request.post<any>({ url: this.baseUrl, data })
  }

  static update(id: number, data: any) {
    return request.put<any>({ url: `${this.baseUrl}/${id}`, data })
  }

  static remove(id: number) {
    return request.del<any>({ url: `${this.baseUrl}/${id}` })
  }

  static batchRemove(ids: number[]) {
    return request.del<any>({ url: `${this.baseUrl}/batch`, data: { ids } })
  }

  // ============ 状态 ============
  static enable(id: number) {
    return request.put<any>({ url: `${this.baseUrl}/${id}/enable` })
  }

  static disable(id: number) {
    return request.put<any>({ url: `${this.baseUrl}/${id}/disable` })
  }

  // ============ 导出/搜索 ============
  static exportList(params?: any) {
    return request.get<any>({ url: `${this.baseUrl}/export`, params, responseType: 'blob' })
  }

  static search(params: any) {
    return request.get<any>({ url: `${this.baseUrl}/search`, params })
  }
}
