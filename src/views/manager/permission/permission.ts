import ManagerPermissionService from '../../../api/managerPermission'
import type {
  CrudTableConfig,
  BatchAction,
  ExportConfig,
  PermissionConfig,
  ResponsiveConfig,
  SecurityConfig,
  PerformanceConfig
} from '@/components/core/tables/ArtCrudTable.vue'

// 搜索初始状态
export const initialSearchState = {
  name: '',
  code: '',
  resource: '',
  action: '',
  method: '',
  api: '',
  status: undefined as number | undefined
}

export const actionOptions = [
  { label: '创建', value: 'create' },
  { label: '读取', value: 'read' },
  { label: '更新', value: 'update' },
  { label: '删除', value: 'delete' },
  { label: '查看', value: 'view' }
]

export const methodOptions = [
  { label: 'GET', value: 'GET' },
  { label: 'POST', value: 'POST' },
  { label: 'PUT', value: 'PUT' },
  { label: 'DELETE', value: 'DELETE' },
  { label: 'PATCH', value: 'PATCH' }
]

export const statusOptions = [
  { label: '启用', value: 1 },
  { label: '禁用', value: 0 }
]

export const yesNoOptions = [
  { label: '是', value: true },
  { label: '否', value: false }
]

export const searchFormItems = [
  { key: 'name', label: '权限名称', type: 'input', placeholder: '输入名称' },
  { key: 'code', label: '权限编码', type: 'input', placeholder: '输入编码' },
  { key: 'resource', label: '资源标识', type: 'input' },
  { key: 'action', label: '操作类型', type: 'select', options: actionOptions },
  { key: 'method', label: 'HTTP 方法', type: 'select', options: methodOptions },
  { key: 'api', label: 'API 路径', type: 'input' },
  { key: 'status', label: '状态', type: 'select', options: statusOptions }
]

export const tableColumns = [
  {
    prop: 'id',
    label: 'ID',
    width: 80,
    fixed: 'left' as const,
    sortable: true,
    align: 'center'
  },
  { prop: 'name', label: '名称', minWidth: 160 },
  { prop: 'code', label: '编码', minWidth: 140 },
  { prop: 'resource', label: '资源', minWidth: 160 },
  { prop: 'action', label: '操作', minWidth: 120 },
  { prop: 'method', label: '方法', minWidth: 100 },
  { prop: 'api', label: 'API', minWidth: 220 },
  {
    prop: 'isSystem',
    label: '系统内置',
    minWidth: 100,
    slot: 'isSystem',
    align: 'center'
  },
  {
    prop: 'status',
    label: '状态',
    width: 100,
    slot: 'status',
    align: 'center'
  },
  { prop: 'created_at', label: '创建时间', minWidth: 180, align: 'center' },
  {
    prop: 'actions',
    label: '操作',
    width: 160,
    fixed: 'right' as const,
    slot: 'actions',
    align: 'center'
  }
]

export const dialog = {
  width: '720px',
  formItems: [
    {
      prop: 'name',
      label: '权限名称',
      type: 'input' as const,
      rules: [{ required: true, message: '必填', trigger: 'blur' }],
      span: 12
    },
    {
      prop: 'code',
      label: '权限编码',
      type: 'input' as const,
      rules: [{ required: true, message: '必填', trigger: 'blur' }],
      span: 12
    },
    { prop: 'resource', label: '资源标识', type: 'input' as const, span: 12 },
    {
      prop: 'action',
      label: '操作类型',
      type: 'select' as const,
      options: actionOptions,
      span: 12
    },
    {
      prop: 'method',
      label: 'HTTP 方法',
      type: 'select' as const,
      options: methodOptions,
      span: 12
    },
    { prop: 'api', label: 'API 路径', type: 'input' as const, span: 12 },
    {
      prop: 'isSystem',
      label: '系统内置',
      type: 'select' as const,
      options: yesNoOptions,
      span: 12
    },
    { prop: 'status', label: '状态', type: 'select' as const, options: statusOptions, span: 12 },
    { prop: 'remark', label: '备注', type: 'textarea' as const, span: 24, config: { rows: 3 } }
  ]
}

export const getStatusText = (status: number) =>
  status === 1 ? '启用' : status === 0 ? '禁用' : '未知'
export const getStatusClass = (status: number) =>
  status === 1 ? 'status-enabled' : status === 0 ? 'status-disabled' : 'status-unknown'

// 封装 apiService，保持与 ArtCrudTable 期望一致
const createApiService = () => ({
  getList: (params: any) => ManagerPermissionService.list(params),
  delete: (id: string | number) => ManagerPermissionService.delete(id),
  batchDelete: (ids: Array<string | number>) => ManagerPermissionService.batchDelete(ids),
  update: (id: string | number, data: any) => ManagerPermissionService.update(id, data),
  create: (data: any) => ManagerPermissionService.create(data)
})

// 批量操作/导出/权限/响应式/安全/性能
const batchActions: BatchAction[] = [
  {
    key: 'batchEnable',
    label: '批量启用',
    type: 'success',
    icon: 'Open',
    handler: (rows: any[]) => ManagerPermissionService.batchEnable(rows.map((r) => r.id))
  },
  {
    key: 'batchDisable',
    label: '批量禁用',
    type: 'warning',
    icon: 'Close',
    handler: (rows: any[]) => ManagerPermissionService.batchDisable(rows.map((r) => r.id))
  },
  {
    key: 'batchDelete',
    label: '批量删除',
    type: 'danger',
    icon: 'Delete',
    confirm: '确认删除选中的 {count} 条权限吗？',
    handler: (rows: any[]) => ManagerPermissionService.batchDelete(rows.map((r) => r.id))
  }
]

const exportConfig: ExportConfig = {
  enabled: true,
  filename: '权限数据导出',
  columns: [
    'id',
    'name',
    'code',
    'resource',
    'action',
    'method',
    'api',
    'isSystem',
    'status',
    'created_at'
  ],
  formats: ['excel', 'csv']
}

const permissions: PermissionConfig = { create: true, update: true, delete: true, export: true }
const responsive: ResponsiveConfig = {
  mobile: { hideColumns: ['resource', 'api', 'created_at'], breakpoint: 768, cardView: false }
}
const security: SecurityConfig = {
  enableXssProtection: true,
  allowedTags: ['span', 'div'],
  allowedAttributes: { div: ['class'], span: ['class'] }
}
const performance: PerformanceConfig = {
  enableCache: true,
  cacheTime: 3 * 60 * 1000,
  debounceTime: 300,
  maxCacheSize: 100,
  lazyLoad: true,
  virtualScroll: { enabled: false, itemHeight: 60, buffer: 10 }
}

export const permissionTableConfig: CrudTableConfig = {
  title: '权限管理',
  addButtonText: '新增权限',
  deleteButtonText: '删除权限',
  deleteConfirmText: '确定要删除该权限吗？',
  batchDeleteConfirmText: '确定要删除选中的 {count} 条权限吗？',

  initialSearchState,
  searchFormItems,
  tableColumns,

  apiService: createApiService(),
  paginationKey: { current: 'page', size: 'page_size' },

  dialog,
  batchActions,
  export: exportConfig,
  permissions,
  responsive,
  security,
  performance,

  hasStatusSwitch: true,
  statusField: 'status',
  statusActiveValue: 1,
  statusInactiveValue: 0,
  allowBatchDelete: true
}

export default permissionTableConfig
