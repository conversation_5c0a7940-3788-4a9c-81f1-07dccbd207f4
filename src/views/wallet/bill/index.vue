<script setup lang="ts">
  import { ref } from 'vue'
  import ArtCrudTable from '@/components/core/tables/ArtCrudTable.vue'
  import ArtButtonTable from '@/components/core/forms/art-button-table/index.vue'
  import {
    billTableConfig,
    getBillTypeText,
    isIncomeType,
    isExpenseType,
    getTypeTagClass,
    formatAmount,
    getAmountClass,
    calculateNetProfit
  } from './bill'

  defineOptions({ name: 'UserBill' })

  const crudTableRef = ref()
  const componentKey = ref(Date.now())

  // 用户信息渲染
  const renderUser = (row: any) => {
    if (row.user) {
      return {
        id: row.user.id,
        username: row.user.username,
        nickname: row.user.nickname,
        avatar: row.user.avatar
      }
    }
    return { id: row.user_id, username: '未知用户', nickname: '', avatar: '' }
  }

  // 资产信息渲染
  const renderAsset = (row: any) => {
    if (row.asset) {
      return {
        id: row.asset.id,
        name: row.asset.name,
        symbol: row.asset.symbol,
        icon: row.asset.icon,
        precision: row.asset.precision || 4
      }
    }
    return { id: row.asset_id, name: '未知资产', symbol: 'UNKNOWN', icon: '', precision: 4 }
  }

  // 账单类型渲染
  const renderBillType = (row: any) => {
    const typeText = getBillTypeText(row.type)
    const typeClass = getTypeTagClass(row.type)
    let icon = ''

    if (isIncomeType(row.type)) {
      icon = 'ArrowUp'
    } else if (isExpenseType(row.type)) {
      icon = 'ArrowDown'
    }

    return {
      text: typeText,
      class: typeClass,
      icon,
      isIncome: isIncomeType(row.type),
      isExpense: isExpenseType(row.type)
    }
  }

  // 金额渲染
  const renderAmount = (row: any) => {
    const amount = formatAmount(row.amount, renderAsset(row).precision, true)
    const amountClass = getAmountClass(row.type, row.amount)
    return {
      amount,
      class: amountClass,
      symbol: renderAsset(row).symbol,
      isPositive: row.amount > 0,
      isNegative: row.amount < 0
    }
  }

  // 手续费渲染
  const renderFee = (row: any) => {
    const fee = formatAmount(row.fee, renderAsset(row).precision)
    return {
      fee,
      symbol: renderAsset(row).symbol,
      hasFee: parseFloat(row.fee) > 0
    }
  }

  // 余额渲染
  const renderBalance = (row: any) => {
    const balance = formatAmount(row.balance, renderAsset(row).precision)
    const balanceClass =
      row.balance > 0 ? 'balance-positive' : row.balance < 0 ? 'balance-negative' : 'balance-zero'
    return {
      balance,
      class: balanceClass,
      symbol: renderAsset(row).symbol
    }
  }

  // 净收益渲染
  const renderNetProfit = (row: any) => {
    const netProfit = calculateNetProfit(row.amount, row.fee)
    const netProfitFormatted = formatAmount(netProfit, renderAsset(row).precision, true)
    const netProfitClass =
      netProfit > 0 ? 'profit-positive' : netProfit < 0 ? 'profit-negative' : 'profit-zero'
    return {
      netProfit: netProfitFormatted,
      class: netProfitClass,
      symbol: renderAsset(row).symbol
    }
  }

  // 查看账单详情
  const viewBillDetail = (row: any) => {
    console.log('查看账单详情:', row)
    // 这里可以实现跳转到账单详情页面的逻辑
  }

  // 验证账单
  const verifyBill = (row: any) => {
    console.log('验证账单:', row.id)
    // 调用API验证账单
    crudTableRef.value?.refreshData()
  }

  // 重新计算账单余额
  const recalculateBill = (row: any) => {
    console.log('重新计算账单余额:', row.id)
    // 调用API重新计算账单余额
    crudTableRef.value?.refreshData()
  }

  // 审核账单
  const auditBill = (row: any, status: string) => {
    console.log('审核账单:', row.id, status)
    // 调用API审核账单
    crudTableRef.value?.refreshData()
  }

  // 查看关联记录
  const viewRelatedRecords = (row: any) => {
    console.log('查看关联记录:', row.source_id)
    // 这里可以实现跳转到关联记录页面的逻辑
  }

  // 复制账单信息
  const copyBillInfo = (row: any) => {
    const billInfo = `账单ID: ${row.id}\n用户ID: ${row.user_id}\n资产: ${renderAsset(row).symbol}\n类型: ${getBillTypeText(row.type)}\n金额: ${renderAmount(row).amount}\n时间: ${row.created_at}`
    navigator.clipboard.writeText(billInfo).then(() => {
      console.log('账单信息已复制到剪贴板')
    })
  }

  // 处理下拉菜单命令
  const handleDropdownCommand = (command: string, row: any) => {
    switch (command) {
      case 'verify':
        verifyBill(row)
        break
      case 'recalculate':
        recalculateBill(row)
        break
      case 'audit-approve':
        auditBill(row, 'approved')
        break
      case 'audit-reject':
        auditBill(row, 'rejected')
        break
      case 'view-related':
        viewRelatedRecords(row)
        break
      case 'copy':
        copyBillInfo(row)
        break
      default:
        console.log('未知命令:', command)
    }
  }
</script>

<template>
  <ArtCrudTable ref="crudTableRef" :key="componentKey" :config="billTableConfig">
    <!-- 用户信息列 -->
    <template #user="{ row }">
      <div class="user-info">
        <div class="user-avatar" v-if="renderUser(row).avatar">
          <img :src="renderUser(row).avatar" :alt="renderUser(row).username" class="avatar-img" />
        </div>
        <div class="user-details">
          <div class="user-id">ID: {{ renderUser(row).id }}</div>
          <div class="user-name">{{ renderUser(row).username }}</div>
          <div v-if="renderUser(row).nickname" class="user-nickname">
            ({{ renderUser(row).nickname }})
          </div>
        </div>
      </div>
    </template>

    <!-- 资产信息列 -->
    <template #asset="{ row }">
      <div class="asset-info">
        <div class="asset-icon" v-if="renderAsset(row).icon">
          <img :src="renderAsset(row).icon" :alt="renderAsset(row).symbol" class="asset-icon-img" />
        </div>
        <div class="asset-details">
          <div class="asset-symbol">{{ renderAsset(row).symbol }}</div>
          <div class="asset-name">{{ renderAsset(row).name }}</div>
        </div>
      </div>
    </template>

    <!-- 账单类型列 -->
    <template #billType="{ row }">
      <div class="bill-type-container">
        <span :class="['bill-type-tag', renderBillType(row).class]">
          <el-icon v-if="renderBillType(row).icon" class="type-icon">
            <component :is="renderBillType(row).icon" />
          </el-icon>
          {{ renderBillType(row).text }}
        </span>
        <div class="type-category">
          <span v-if="renderBillType(row).isIncome" class="category-income">收入</span>
          <span v-if="renderBillType(row).isExpense" class="category-expense">支出</span>
        </div>
      </div>
    </template>

    <!-- 金额列 -->
    <template #amount="{ row }">
      <div class="amount-container">
        <span :class="['amount-value', renderAmount(row).class]">
          {{ renderAmount(row).amount }}
        </span>
        <span class="amount-symbol">{{ renderAmount(row).symbol }}</span>
        <div class="amount-indicator">
          <el-icon v-if="renderAmount(row).isPositive" class="indicator-positive">
            <TrendCharts />
          </el-icon>
          <el-icon v-if="renderAmount(row).isNegative" class="indicator-negative">
            <TrendCharts />
          </el-icon>
        </div>
      </div>
    </template>

    <!-- 手续费列 -->
    <template #fee="{ row }">
      <div class="fee-container">
        <span class="fee-value">{{ renderFee(row).fee }}</span>
        <span class="fee-symbol">{{ renderFee(row).symbol }}</span>
        <div v-if="!renderFee(row).hasFee" class="no-fee-tag">免费</div>
      </div>
    </template>

    <!-- 余额列 -->
    <template #balance="{ row }">
      <div class="balance-container">
        <span :class="['balance-value', renderBalance(row).class]">
          {{ renderBalance(row).balance }}
        </span>
        <span class="balance-symbol">{{ renderBalance(row).symbol }}</span>
      </div>
    </template>

    <!-- 净收益列 -->
    <template #netProfit="{ row }">
      <div class="net-profit-container">
        <span :class="['net-profit-value', renderNetProfit(row).class]">
          {{ renderNetProfit(row).netProfit }}
        </span>
        <span class="net-profit-symbol">{{ renderNetProfit(row).symbol }}</span>
      </div>
    </template>

    <!-- 操作列 -->
    <template #actions="{ row }">
      <div class="flex gap-2">
        <ArtButtonTable type="edit" @click="crudTableRef?.showDialog('edit', row)" />
        <ArtButtonTable type="delete" @click="crudTableRef?.deleteItem(row.id)" />
        <ArtButtonTable type="view" @click="viewBillDetail(row)" />

        <!-- 更多操作下拉菜单 -->
        <el-dropdown trigger="click" @command="(command) => handleDropdownCommand(command, row)">
          <ArtButtonTable type="more" />
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="verify">
                <el-icon><CircleCheck /></el-icon>
                验证账单
              </el-dropdown-item>
              <el-dropdown-item command="recalculate">
                <el-icon><Calculator /></el-icon>
                重新计算
              </el-dropdown-item>
              <el-dropdown-item command="audit-approve" divided>
                <el-icon><Select /></el-icon>
                审核通过
              </el-dropdown-item>
              <el-dropdown-item command="audit-reject">
                <el-icon><CloseBold /></el-icon>
                审核拒绝
              </el-dropdown-item>
              <el-dropdown-item command="view-related" divided>
                <el-icon><Connection /></el-icon>
                关联记录
              </el-dropdown-item>
              <el-dropdown-item command="copy">
                <el-icon><CopyDocument /></el-icon>
                复制信息
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </template>
  </ArtCrudTable>
</template>

<style lang="scss" scoped>
  .flex {
    display: flex;
  }

  .gap-2 {
    gap: 8px;
  }

  // 用户信息样式
  .user-info {
    display: flex;
    gap: 8px;
    align-items: center;

    .user-avatar {
      .avatar-img {
        width: 32px;
        height: 32px;
        object-fit: cover;
        border: 1px solid #e0e0e0;
        border-radius: 50%;
      }
    }

    .user-details {
      .user-id {
        margin-bottom: 2px;
        font-size: 12px;
        color: #999;
      }

      .user-name {
        font-weight: 500;
        color: #333;
      }

      .user-nickname {
        margin-top: 2px;
        font-size: 12px;
        color: #666;
      }
    }
  }

  // 资产信息样式
  .asset-info {
    display: flex;
    gap: 8px;
    align-items: center;

    .asset-icon {
      .asset-icon-img {
        width: 24px;
        height: 24px;
        object-fit: cover;
        border-radius: 50%;
      }
    }

    .asset-details {
      .asset-symbol {
        font-size: 14px;
        font-weight: 600;
        color: #333;
      }

      .asset-name {
        margin-top: 2px;
        font-size: 12px;
        color: #666;
      }
    }
  }

  // 账单类型样式
  .bill-type-container {
    display: flex;
    flex-direction: column;
    gap: 4px;

    .bill-type-tag {
      display: flex;
      gap: 4px;
      align-items: center;
      padding: 4px 8px;
      font-size: 12px;
      font-weight: 500;
      border-radius: 4px;

      .type-icon {
        font-size: 12px;
      }

      &.income-type {
        color: #52c41a;
        background-color: #f6ffed;
        border: 1px solid #b7eb8f;

        .type-icon {
          color: #52c41a;
        }
      }

      &.expense-type {
        color: #ff4d4f;
        background-color: #fff2f0;
        border: 1px solid #ffb3b3;

        .type-icon {
          color: #ff4d4f;
        }
      }

      &.unknown-type {
        color: #8c8c8c;
        background-color: #f5f5f5;
        border: 1px solid #d9d9d9;
      }
    }

    .type-category {
      display: flex;
      justify-content: center;

      .category-income,
      .category-expense {
        padding: 1px 4px;
        font-size: 10px;
        border-radius: 2px;
      }

      .category-income {
        color: #389e0d;
        background-color: #f6ffed;
      }

      .category-expense {
        color: #cf1322;
        background-color: #fff1f0;
      }
    }
  }

  // 金额容器样式
  .amount-container,
  .fee-container,
  .balance-container,
  .net-profit-container {
    display: flex;
    flex-direction: column;
    align-items: flex-end;

    .amount-value,
    .fee-value,
    .balance-value,
    .net-profit-value {
      font-family: Monaco, Menlo, monospace;
      font-weight: 500;
    }

    .amount-symbol,
    .fee-symbol,
    .balance-symbol,
    .net-profit-symbol {
      margin-top: 2px;
      font-size: 11px;
      color: #8c8c8c;
    }
  }

  // 金额样式
  .amount-container {
    .amount-value {
      &.amount-income {
        font-weight: 600;
        color: #52c41a;
      }

      &.amount-expense {
        font-weight: 600;
        color: #ff4d4f;
      }

      &.amount-positive {
        color: #52c41a;
      }

      &.amount-negative {
        color: #ff4d4f;
      }

      &.amount-zero {
        color: #8c8c8c;
      }
    }

    .amount-indicator {
      margin-top: 2px;

      .indicator-positive {
        font-size: 12px;
        color: #52c41a;
      }

      .indicator-negative {
        font-size: 12px;
        color: #ff4d4f;
        transform: rotate(180deg);
      }
    }
  }

  // 手续费样式
  .fee-container {
    .fee-value {
      color: #faad14;
    }

    .no-fee-tag {
      padding: 1px 4px;
      margin-top: 2px;
      font-size: 10px;
      color: #52c41a;
      background-color: #f6ffed;
      border-radius: 2px;
    }
  }

  // 余额样式
  .balance-container {
    .balance-value {
      &.balance-positive {
        color: #1890ff;
      }

      &.balance-negative {
        color: #ff4d4f;
      }

      &.balance-zero {
        color: #8c8c8c;
      }
    }
  }

  // 净收益样式
  .net-profit-container {
    .net-profit-value {
      &.profit-positive {
        font-weight: 600;
        color: #52c41a;
      }

      &.profit-negative {
        font-weight: 600;
        color: #ff4d4f;
      }

      &.profit-zero {
        color: #8c8c8c;
      }
    }
  }

  // 悬停效果
  .amount-container:hover,
  .fee-container:hover,
  .balance-container:hover,
  .net-profit-container:hover {
    background-color: #fafafa;
    border-radius: 4px;
    transition: all 0.3s ease;
  }

  // 特殊高亮
  .bill-type-container:hover .bill-type-tag {
    transition: all 0.2s ease;
    transform: scale(1.02);
  }

  // 操作按钮区域

  /* 已有 .flex 定义，此处不再重复定义，若需换行可在需要处单独添加样式类 */

  // 响应式设计
  @media (width <= 768px) {
    .user-info,
    .asset-info {
      flex-direction: column;
      gap: 4px;
      align-items: flex-start;
    }

    .amount-container,
    .fee-container,
    .balance-container,
    .net-profit-container {
      align-items: flex-start;
    }

    .bill-type-container {
      align-items: flex-start;

      .bill-type-tag {
        align-self: flex-start;
      }
    }
  }
</style>
