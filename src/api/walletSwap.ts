import request from '@/utils/http'

/**
 * 用户闪兑管理服务类
 * 对应的Go结构体：
 * - UserSwap: 用户闪兑主表结构
 * - 闪兑类型常量: 闪兑类型(1)
 * - 闪兑状态常量: 待处理(1)、已完成(2)、已取消(3)、已失败(4)
 */
export class UserSwapService {
  private static baseUrl = '/wallet/swap'

  // 闪兑类型常量
  static readonly SWAP_TYPE_FLASH = 1 // 闪兑

  // 闪兑状态常量
  static readonly SWAP_STATUS_PENDING = 1 // 待处理
  static readonly SWAP_STATUS_COMPLETED = 2 // 已完成
  static readonly SWAP_STATUS_CANCELED = 3 // 已取消
  static readonly SWAP_STATUS_FAILED = 4 // 已失败

  // 获取用户闪兑列表
  static getUserSwapList(params?: any) {
    return request.get<any>({
      url: this.baseUrl,
      params
    })
  }

  // 获取用户闪兑详情
  static getUserSwapDetail(id: number) {
    return request.get<any>({
      url: `${this.baseUrl}/${id}`
    })
  }

  // 创建用户闪兑
  static createUserSwap(data: any) {
    return request.post<any>({
      url: this.baseUrl,
      data
    })
  }

  // 更新用户闪兑
  static updateUserSwap(id: number, data: any) {
    return request.put<any>({
      url: `${this.baseUrl}/${id}`,
      data
    })
  }

  // 删除用户闪兑
  static deleteUserSwap(id: number) {
    return request.del<any>({
      url: `${this.baseUrl}/${id}`
    })
  }

  // 批量删除用户闪兑
  static batchDeleteUserSwaps(ids: number[]) {
    return request.del<any>({
      url: `${this.baseUrl}/batch`,
      data: { ids }
    })
  }

  // ==================== 状态操作 ====================

  // 完成闪兑
  static completeSwap(id: number, reason?: string) {
    return request.put<any>({
      url: `${this.baseUrl}/${id}/complete`,
      data: { reason }
    })
  }

  // 取消闪兑
  static cancelSwap(id: number, reason: string) {
    return request.put<any>({
      url: `${this.baseUrl}/${id}/cancel`,
      data: { reason }
    })
  }

  // 设置闪兑失败
  static failSwap(id: number, reason: string) {
    return request.put<any>({
      url: `${this.baseUrl}/${id}/fail`,
      data: { reason }
    })
  }

  // 批量更新状态
  static batchUpdateStatus(ids: number[], status: number, reason?: string) {
    return request.put<any>({
      url: `${this.baseUrl}/batch/status`,
      data: { ids, status, reason }
    })
  }
}
