// 操作日志管理配置
import { OperateLogService } from '@/api/operateLogApi'

export const operateLogTableConfig = {
  title: '操作日志',
  addButtonText: '新增日志',
  deleteButtonText: '批量删除',
  deleteConfirmText: '确定要删除该操作日志吗？',
  batchDeleteConfirmText: '确定要删除选中的 {count} 条操作日志吗？',

  initialSearchState: {
    userId: '',
    managerId: '',
    username: '',
    module: '',
    type: '',
    mode: '',
    status: '',
    startTime: '',
    endTime: ''
  },

  searchFormItems: [
    {
      label: '用户名',
      prop: 'username',
      type: 'input',
      config: {
        clearable: true,
        placeholder: '请输入用户名'
      }
    },
    {
      label: '操作类型',
      prop: 'type',
      type: 'select',
      config: {
        clearable: true,
        placeholder: '请选择操作类型'
      },
      options: [
        { label: '新增', value: '1' },
        { label: '删除', value: '2' },
        { label: '修改', value: '3' },
        { label: '查询', value: '4' },
        { label: '其他', value: '5' }
      ]
    },
    {
      label: '操作模式',
      prop: 'mode',
      type: 'select',
      config: {
        clearable: true,
        placeholder: '请选择操作模式'
      },
      options: [
        { label: '后台', value: '1' },
        { label: '前台', value: '2' }
      ]
    },
    {
      label: '操作状态',
      prop: 'status',
      type: 'select',
      config: {
        clearable: true,
        placeholder: '请选择操作状态'
      },
      options: [
        { label: '成功', value: '1' },
        { label: '失败', value: '0' }
      ]
    }
  ],

  tableColumns: [
    { type: 'selection' as const, width: 55 },
    { label: 'ID', prop: 'id', width: 80, sortable: true },
    { label: '用户名', prop: 'username', minWidth: 100 },
    { label: '操作类型', prop: 'type', width: 100, slot: 'type' },
    { label: '操作模式', prop: 'mode', width: 100, slot: 'mode' },
    { label: '请求路径', prop: 'reqPath', minWidth: 200, showOverflowTooltip: true },
    { label: '操作状态', prop: 'status', width: 100, slot: 'status' },
    { label: '操作IP', prop: 'ip', minWidth: 120 },
    { label: '操作地点', prop: 'location', minWidth: 150, showOverflowTooltip: true },
    { label: '操作时间', prop: 'created_at', minWidth: 160, sortable: true, columnType: 'date' },
    { label: 'UserAgent', prop: 'userAgent', minWidth: 200, showOverflowTooltip: true },
    { label: '操作', width: 150, fixed: 'right' as const, slot: 'actions' }
  ],

  apiService: {
    getList: (params: any) => {
      // 参数格式化处理
      const formattedParams = {
        page: params.page || 1,
        pageSize: params.pageSize || 20,
        userId: params.userId ? Number(params.userId) : undefined,
        managerId: params.managerId ? Number(params.managerId) : undefined,
        username: params.username || undefined,
        module: params.module || undefined,
        type: params.type ? Number(params.type) : undefined,
        mode: params.mode ? Number(params.mode) : undefined,
        status: params.status !== '' ? Number(params.status) : undefined,
        startTime: params.startTime || undefined,
        endTime: params.endTime || undefined
      }
      return OperateLogService.getOperateLogList(formattedParams)
    },
    delete: (id: any) => OperateLogService.deleteOperateLog(id),
    batchDelete: (ids: any[]) => OperateLogService.batchDeleteOperateLogs(ids),
    update: undefined, // 操作日志不允许更新
    create: undefined // 操作日志不允许创建
  },

  // 自定义分页字段映射，适配后端接口
  paginationKey: {
    current: 'page',
    size: 'pageSize',
    orderBy: 'orderBy',
    order: 'order'
  },

  // 按钮控制配置
  hideAddButton: true, // 隐藏新增按钮
  hideDeleteButton: false, // 显示删除按钮
  disableEdit: true, // 禁用编辑功能
  allowDelete: true, // 允许删除操作
  allowBatchDelete: true, // 允许批量删除

  // 空数据时的高度
  emptyHeight: '500px'
}

// 获取操作类型标签类型
export const getOperateTypeTagType = (type: number) => {
  switch (type) {
    case 1:
      return 'success' // 新增
    case 2:
      return 'danger' // 删除
    case 3:
      return 'warning' // 修改
    case 4:
      return 'info' // 查询
    case 5:
      return undefined // 导出
    default:
      return undefined
  }
}

// 获取操作类型文本
export const getOperateTypeText = (type: number) => {
  switch (type) {
    case 1:
      return '新增'
    case 2:
      return '删除'
    case 3:
      return '修改'
    case 4:
      return '查询'
    case 5:
      return '其他'
    default:
      return '未知'
  }
}

// 获取操作模式标签类型
export const getOperateModeTagType = (mode: number) => {
  return mode === 1 ? 'warning' : 'primary'
}

// 获取操作模式文本
export const getOperateModeText = (mode: number) => {
  return mode === 1 ? '前台' : '后台'
}

// 获取操作状态标签类型
export const getOperateStatusTagType = (status: number) => {
  return status === 1 ? 'success' : 'danger'
}

// 获取操作状态文本
export const getOperateStatusText = (status: number) => {
  return status === 1 ? '成功' : '失败'
}
