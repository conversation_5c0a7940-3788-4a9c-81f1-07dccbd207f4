<script setup lang="ts">
  import { ref } from 'vue'
  import ArtCrudTable from '@/components/core/tables/ArtCrudTable.vue'
  import ArtButtonTable from '@/components/core/forms/art-button-table/index.vue'
  import {
    noticeTableConfig,
    getTypeText,
    getStatusText,
    getPriorityText,
    getSenderText
  } from './notice'

  defineOptions({ name: 'UserNotice' })

  const crudTableRef = ref()
  const componentKey = ref(Date.now())

  // 发送者渲染
  const renderSender = (row: any) => {
    const senderId = row.sender_id as number
    const senderText = getSenderText(senderId)
    return {
      text: senderText,
      isSystem: senderId === 0,
      class: senderId === 0 ? 'sender-system' : 'sender-user'
    }
  }

  // 接收者渲染
  const renderReceiver = (row: any) => {
    if (row.receiver) {
      return {
        id: row.receiver.id,
        username: row.receiver.username,
        nickname: row.receiver.nickname
      }
    }
    return { id: row.receiver_id, username: '未知用户', nickname: '' }
  }

  // 通知类型渲染
  const renderType = (row: any) => {
    const typeText = getTypeText(row.type)
    let typeClass = ''
    switch (row.type) {
      case 1:
        typeClass = 'type-system'
        break
      case 2:
        typeClass = 'type-order'
        break
      case 3:
        typeClass = 'type-account'
        break
      case 4:
        typeClass = 'type-activity'
        break
      case 5:
        typeClass = 'type-message'
        break
      case 6:
        typeClass = 'type-security'
        break
    }
    return { text: typeText, class: typeClass }
  }

  // 优先级渲染
  const renderPriority = (row: any) => {
    const priorityText = getPriorityText(row.priority)
    let priorityClass = ''
    switch (row.priority) {
      case 1:
        priorityClass = 'priority-low'
        break
      case 2:
        priorityClass = 'priority-normal'
        break
      case 3:
        priorityClass = 'priority-high'
        break
      case 4:
        priorityClass = 'priority-urgent'
        break
    }
    return { text: priorityText, class: priorityClass }
  }

  // 状态渲染
  const renderStatus = (row: any) => {
    const statusText = getStatusText(row.status)
    let statusClass = ''
    switch (row.status) {
      case 1:
        statusClass = 'status-normal'
        break
      case 2:
        statusClass = 'status-pinned'
        break
      case 3:
        statusClass = 'status-hidden'
        break
      case 4:
        statusClass = 'status-expired'
        break
    }
    return { text: statusText, class: statusClass }
  }

  // 阅读状态渲染
  const renderReadStatus = (row: any) => {
    const isRead = row.is_read
    return {
      text: isRead ? '已读' : '未读',
      class: isRead ? 'read-status-read' : 'read-status-unread'
    }
  }

  // 查看通知详情
  const viewNoticeDetail = (row: any) => {
    console.log('查看通知详情:', row)
    // 这里可以实现跳转到通知详情页面或弹窗显示详情
  }

  // 标记为已读
  const markAsRead = (row: any) => {
    console.log('标记为已读:', row.id)
    // 调用API标记为已读
    crudTableRef.value?.refreshData()
  }

  // 设置置顶
  const setPinned = (row: any) => {
    console.log('设置置顶:', row.id)
    // 调用API设置置顶
    crudTableRef.value?.refreshData()
  }
</script>

<template>
  <ArtCrudTable ref="crudTableRef" :key="componentKey" :config="noticeTableConfig">
    <!-- 发送者列 -->
    <template #sender="{ row }">
      <span :class="['sender-tag', renderSender(row).class]">
        {{ renderSender(row).text }}
      </span>
    </template>

    <!-- 接收者列 -->
    <template #receiver="{ row }">
      <div class="receiver-info">
        <div class="receiver-id">ID: {{ renderReceiver(row).id }}</div>
        <div class="receiver-name">{{ renderReceiver(row).username }}</div>
        <div v-if="renderReceiver(row).nickname" class="receiver-nickname">
          ({{ renderReceiver(row).nickname }})
        </div>
      </div>
    </template>

    <!-- 通知类型列 -->
    <template #type="{ row }">
      <span :class="['type-tag', renderType(row).class]">
        {{ renderType(row).text }}
      </span>
    </template>

    <!-- 优先级列 -->
    <template #priority="{ row }">
      <span :class="['priority-tag', renderPriority(row).class]">
        {{ renderPriority(row).text }}
      </span>
    </template>

    <!-- 状态列 -->
    <template #status="{ row }">
      <span :class="['status-tag', renderStatus(row).class]">
        {{ renderStatus(row).text }}
      </span>
    </template>

    <!-- 阅读状态列 -->
    <template #readStatus="{ row }">
      <span :class="['read-status-tag', renderReadStatus(row).class]">
        {{ renderReadStatus(row).text }}
      </span>
    </template>

    <!-- 操作列 -->
    <template #actions="{ row }">
      <div class="flex gap-2">
        <ArtButtonTable
          type="edit"
          @click="crudTableRef?.showDialog('edit', row)"
          :disabled="row.status === 4"
        />
        <ArtButtonTable
          type="delete"
          @click="crudTableRef?.deleteItem(row.id)"
          :disabled="row.sender_id === 0"
        />
        <ArtButtonTable type="view" @click="viewNoticeDetail(row)" />

        <!-- 标记已读按钮 -->
        <ArtButtonTable v-if="!row.is_read" type="add" @click="markAsRead(row)" />

        <!-- 置顶按钮 -->
        <ArtButtonTable v-if="row.status === 1" type="more" @click="setPinned(row)" />
      </div>
    </template>
  </ArtCrudTable>
</template>

<style lang="scss" scoped>
  .flex {
    display: flex;
  }

  .gap-2 {
    gap: 8px;
  }

  // 发送者标签样式
  .sender-tag {
    padding: 4px 8px;
    font-size: 12px;
    font-weight: 500;
    border-radius: 4px;

    &.sender-system {
      color: #7b1fa2;
      background-color: #f3e5f5;
      border: 1px solid #ce93d8;
    }

    &.sender-user {
      color: #2e7d32;
      background-color: #e8f5e8;
      border: 1px solid #a5d6a7;
    }
  }

  // 接收者信息样式
  .receiver-info {
    .receiver-id {
      margin-bottom: 2px;
      font-size: 12px;
      color: #999;
    }

    .receiver-name {
      font-weight: 500;
      color: #333;
    }

    .receiver-nickname {
      margin-top: 2px;
      font-size: 12px;
      color: #666;
    }
  }

  // 通知类型标签样式
  .type-tag {
    padding: 4px 8px;
    font-size: 12px;
    font-weight: 500;
    border-radius: 4px;

    &.type-system {
      color: #1976d2;
      background-color: #e3f2fd;
      border: 1px solid #bbdefb;
    }

    &.type-order {
      color: #f57c00;
      background-color: #fff3e0;
      border: 1px solid #ffcc02;
    }

    &.type-account {
      color: #388e3c;
      background-color: #e8f5e8;
      border: 1px solid #a5d6a7;
    }

    &.type-activity {
      color: #c2185b;
      background-color: #fce4ec;
      border: 1px solid #f48fb1;
    }

    &.type-message {
      color: #7b1fa2;
      background-color: #f3e5f5;
      border: 1px solid #ce93d8;
    }

    &.type-security {
      color: #d32f2f;
      background-color: #ffebee;
      border: 1px solid #ef9a9a;
    }
  }

  // 优先级标签样式
  .priority-tag {
    padding: 4px 8px;
    font-size: 12px;
    font-weight: 500;
    border-radius: 4px;

    &.priority-low {
      color: #757575;
      background-color: #f5f5f5;
      border: 1px solid #e0e0e0;
    }

    &.priority-normal {
      color: #1976d2;
      background-color: #e3f2fd;
      border: 1px solid #bbdefb;
    }

    &.priority-high {
      color: #f57c00;
      background-color: #fff3e0;
      border: 1px solid #ffcc02;
    }

    &.priority-urgent {
      color: #d32f2f;
      background-color: #ffebee;
      border: 1px solid #ef9a9a;
    }
  }

  // 状态标签样式
  .status-tag {
    padding: 4px 8px;
    font-size: 12px;
    font-weight: 500;
    border-radius: 4px;

    &.status-normal {
      color: #2e7d32;
      background-color: #e8f5e8;
      border: 1px solid #a5d6a7;
    }

    &.status-pinned {
      color: #ef6c00;
      background-color: #fff3e0;
      border: 1px solid #ffcc02;
    }

    &.status-hidden {
      color: #757575;
      background-color: #f5f5f5;
      border: 1px solid #e0e0e0;
    }

    &.status-expired {
      color: #c62828;
      background-color: #ffebee;
      border: 1px solid #ef9a9a;
    }
  }

  // 阅读状态标签样式
  .read-status-tag {
    padding: 4px 8px;
    font-size: 12px;
    font-weight: 500;
    border-radius: 4px;

    &.read-status-unread {
      color: #d32f2f;
      background-color: #ffebee;
      border: 1px solid #ef9a9a;
    }

    &.read-status-read {
      color: #2e7d32;
      background-color: #e8f5e8;
      border: 1px solid #a5d6a7;
    }
  }
</style>
