<script setup lang="ts">
  import { ref } from 'vue'
  import ArtCrudTable from '@/components/core/tables/ArtCrudTable.vue'
  import ArtButtonTable from '@/components/core/forms/art-button-table/index.vue'
  import { articleTableConfig, articleTypeOptions } from './article'

  defineOptions({ name: 'SystemArticle' })

  const crudRef = ref()
</script>

<template>
  <ArtCrudTable ref="crudRef" :config="articleTableConfig">
    <!-- 类型 -->
    <template #type="{ row }">
      <span class="type-tag">{{
        articleTypeOptions.find((o) => o.value === row.type)?.label || '未知'
      }}</span>
    </template>

    <!-- 操作 -->
    <template #actions="{ row }">
      <div>
        <ArtButtonTable type="edit" @click="crudRef?.showDialog('edit', row)" />
        <ArtButtonTable type="delete" @click="crudRef?.deleteItem(row.id)" />
      </div>
    </template>
  </ArtCrudTable>
</template>

<style scoped lang="scss"></style>
