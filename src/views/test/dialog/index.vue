<script setup lang="ts">
  import { ref } from 'vue'
  import { ElButton, ElMessage } from 'element-plus'
  import ArtCrudDialog from '@/components/core/tables/ArtCrudDialog.vue'

  defineOptions({ name: 'DialogTest' })

  const dialogVisible = ref(false)
  const dialogType = ref<'add' | 'edit'>('add')
  const currentRow = ref<Record<string, unknown> | null>(null)
  const submitLoading = ref(false)

  // 对话框配置
  const dialogConfig = {
    title: {
      add: '添加用户',
      edit: '编辑用户'
    },
    width: '600px',
    formItems: [
      {
        label: '用户名',
        prop: 'username',
        type: 'input' as const,
        required: true,
        config: {
          placeholder: '请输入用户名'
        },
        span: 24
      },
      {
        label: '邮箱',
        prop: 'email',
        type: 'input' as const,
        required: true,
        config: {
          placeholder: '请输入邮箱地址'
        },
        span: 12
      },
      {
        label: '手机号',
        prop: 'phone',
        type: 'input' as const,
        config: {
          placeholder: '请输入手机号'
        },
        span: 12
      },
      {
        label: '年龄',
        prop: 'age',
        type: 'number' as const,
        config: {
          min: 1,
          max: 120,
          placeholder: '请输入年龄'
        },
        span: 8
      },
      {
        label: '性别',
        prop: 'gender',
        type: 'select' as const,
        options: [
          { label: '男', value: 'male' },
          { label: '女', value: 'female' },
          { label: '其他', value: 'other' }
        ],
        span: 8
      },
      {
        label: '状态',
        prop: 'status',
        type: 'select' as const,
        options: [
          { label: '激活', value: 'active' },
          { label: '禁用', value: 'inactive' },
          { label: '待审核', value: 'pending' }
        ],
        span: 8
      },
      {
        label: '个人简介',
        prop: 'bio',
        type: 'textarea' as const,
        config: {
          rows: 4,
          placeholder: '请输入个人简介'
        },
        span: 24
      },
      {
        label: '详细地址',
        prop: 'address',
        type: 'textarea' as const,
        config: {
          rows: 3,
          placeholder: '请输入详细地址'
        },
        span: 24
      }
    ],
    initialFormData: {
      username: '',
      email: '',
      phone: '',
      age: 25,
      gender: 'male',
      status: 'active',
      bio: '',
      address: ''
    }
  }

  // 模拟的用户数据
  const userData = {
    id: 1,
    username: 'john_doe',
    email: '<EMAIL>',
    phone: '13812345678',
    age: 28,
    gender: 'male',
    status: 'active',
    bio: '这是一个测试用户的个人简介，用于演示多行文本字段的显示效果。',
    address: '北京市朝阳区某某街道某某小区某某号楼某某单元某某室'
  }

  // 打开新增对话框
  const openAddDialog = () => {
    dialogType.value = 'add'
    currentRow.value = null
    dialogVisible.value = true
  }

  // 打开编辑对话框
  const openEditDialog = () => {
    dialogType.value = 'edit'
    currentRow.value = { ...userData }
    dialogVisible.value = true
  }

  // 处理对话框提交
  const handleDialogSubmit = async (data: {
    formData: Record<string, unknown>
    type: 'add' | 'edit'
  }) => {
    submitLoading.value = true

    try {
      // 模拟API调用
      await new Promise((resolve) => setTimeout(resolve, 1000))

      console.log('提交的数据:', data)
      ElMessage.success(`${data.type === 'add' ? '添加' : '编辑'}成功！`)
      dialogVisible.value = false
    } catch {
      ElMessage.error(`${data.type === 'add' ? '添加' : '编辑'}失败！`)
    } finally {
      submitLoading.value = false
    }
  }

  // 处理对话框取消
  const handleDialogCancel = () => {
    dialogVisible.value = false
  }
</script>

<template>
  <div class="dialog-test-page">
    <div class="page-header">
      <h2>ArtCrudDialog 组件测试</h2>
      <p>这是一个演示新分离的对话框组件的测试页面，支持完整的响应式设计。</p>
      <p class="responsive-tip">💡 尝试调整浏览器窗口大小或使用移动设备查看响应式效果</p>
    </div>

    <div class="demo-buttons">
      <ElButton type="primary" @click="openAddDialog"> 打开新增对话框 </ElButton>
      <ElButton type="success" @click="openEditDialog"> 打开编辑对话框 </ElButton>
    </div>

    <!-- 使用新的对话框组件 -->
    <ArtCrudDialog
      :visible="dialogVisible"
      :type="dialogType"
      :dialog-config="dialogConfig"
      :current-row="currentRow"
      :loading="submitLoading"
      entity-name="用户"
      @update:visible="(val) => (dialogVisible = val)"
      @submit="handleDialogSubmit"
      @cancel="handleDialogCancel"
    />

    <div class="demo-info">
      <h3>🎯 功能特点</h3>
      <ul>
        <li>✅ 完全独立的对话框组件</li>
        <li>✅ 支持新增和编辑模式</li>
        <li>✅ 自动表单验证和数据绑定</li>
        <li>✅ 支持多种表单字段类型</li>
        <li>🔥 <strong>完整的响应式设计</strong></li>
        <li>📱 移动端优化：字段自动全宽，按钮垂直排列</li>
        <li>📟 平板端适配：适当调整间距和尺寸</li>
        <li>🖥️ 桌面端体验：保持原有布局和交互</li>
        <li>✅ 完整的事件处理机制</li>
      </ul>

      <h3>📋 对话框配置示例</h3>
      <pre><code>{{ JSON.stringify(dialogConfig, null, 2) }}</code></pre>
    </div>
  </div>
</template>

<style lang="scss" scoped>
  .dialog-test-page {
    max-width: 1200px;
    padding: 24px;
    margin: 0 auto;

    .page-header {
      margin-bottom: 32px;
      text-align: center;

      h2 {
        margin-bottom: 8px;
        color: #303133;
      }

      p {
        font-size: 14px;
        color: #606266;
      }

      .responsive-tip {
        margin-top: 8px;
        font-size: 12px;
        color: #909399;
      }
    }

    .demo-buttons {
      display: flex;
      gap: 16px;
      justify-content: center;
      margin-bottom: 32px;
    }

    .demo-info {
      padding: 24px;
      margin-top: 32px;
      background: #f8f9fa;
      border-radius: 8px;

      h3 {
        margin-bottom: 16px;
        font-size: 16px;
        color: #409eff;
      }

      ul {
        margin-bottom: 24px;

        li {
          margin-bottom: 8px;
          color: #606266;
        }
      }

      pre {
        padding: 16px;
        overflow-x: auto;
        font-size: 12px;
        color: #303133;
        background: #fff;
        border: 1px solid #e4e7ed;
        border-radius: 4px;
      }
    }
  }
</style>
