import { UserTransferService } from '@/api/walletTransfer'
import type {
  CrudTableConfig,
  BatchAction,
  ExportConfig,
  PermissionConfig,
  SecurityConfig,
  PerformanceConfig,
  ResponsiveConfig
} from '@/components/core/tables/ArtCrudTable.vue'

// 过滤空值参数的工具函数
const filterEmptyParams = (params: Record<string, any>) => {
  const filtered: Record<string, any> = {}
  Object.keys(params).forEach((key) => {
    const value = params[key]
    // 只保留非空值：非空字符串、非null、非undefined、数字0、布尔值false
    if (value !== '' && value !== null && value !== undefined) {
      filtered[key] = value
    }
  })
  return filtered
}

// 包装API服务，添加参数过滤
const createTransferApiService = () => ({
  getList: (params: any) => {
    // 过滤空值参数，解决URL中出现空值的问题
    const filteredParams = filterEmptyParams(params)
    console.log('UserTransfer API - 过滤后的请求参数:', filteredParams)
    return UserTransferService.getUserTransferList(filteredParams)
  },
  delete: (id: any) => UserTransferService.deleteUserTransfer(id),
  batchDelete: (ids: any[]) => UserTransferService.batchDeleteUserTransfers(ids),
  update: (id: any, data: any) => UserTransferService.updateUserTransfer(id, data),
  create: (data: any) => UserTransferService.createUserTransfer(data)
})

// 转账类型选项
export const transferTypeOptions = [{ label: '内部转账', value: 1 }]

// 转账状态选项
export const transferStatusOptions = [
  { label: '待处理', value: 1 },
  { label: '已完成', value: 2 },
  { label: '已取消', value: 3 },
  { label: '已失败', value: 4 }
]

// 批量操作配置
const batchActions: BatchAction[] = [
  {
    label: '批量完成',
    key: 'batchComplete',
    type: 'success',
    icon: 'CircleCheck',
    confirm: '确认完成选中的 {count} 条转账吗？',
    handler: async (selectedRows) => {
      console.log('批量完成转账:', selectedRows)
      await UserTransferService.batchUpdateStatus(
        selectedRows.map((row) => row.id as number),
        UserTransferService.TRANSFER_STATUS_COMPLETED,
        '批量完成'
      )
    }
  },
  {
    label: '批量取消',
    key: 'batchCancel',
    type: 'warning',
    icon: 'Close',
    confirm: '确认取消选中的 {count} 条转账吗？',
    handler: async (selectedRows) => {
      console.log('批量取消转账:', selectedRows)
      await UserTransferService.batchUpdateStatus(
        selectedRows.map((row) => row.id as number),
        UserTransferService.TRANSFER_STATUS_CANCELED,
        '批量取消'
      )
    }
  },
  {
    label: '批量设为失败',
    key: 'batchFail',
    type: 'danger',
    icon: 'CloseBold',
    confirm: '确认将选中的 {count} 条转账设为失败吗？',
    handler: async (selectedRows) => {
      console.log('批量设为失败:', selectedRows)
      await UserTransferService.batchUpdateStatus(
        selectedRows.map((row) => row.id as number),
        UserTransferService.TRANSFER_STATUS_FAILED,
        '批量设为失败'
      )
    }
  },
  {
    label: '批量审核通过',
    key: 'batchApprove',
    type: 'primary',
    icon: 'Select',
    confirm: '确认审核通过选中的 {count} 条转账吗？',
    handler: async (selectedRows) => {
      console.log('批量审核通过转账:', selectedRows)
      await UserTransferService.batchReviewTransfers(
        selectedRows.map((row) => row.id as number),
        true,
        '批量审核通过'
      )
    }
  },
  {
    label: '批量导出',
    key: 'batchExport',
    type: 'info',
    icon: 'Download',
    handler: async (selectedRows) => {
      console.log('批量导出转账:', selectedRows)
      const exportData = selectedRows.map((row) => ({
        id: row.id as number,
        send_user_id: row.send_user_id as number,
        receive_user_id: row.receive_user_id as number,
        asset_id: row.asset_id as number,
        amount: row.amount as number,
        fixed_fee: row.fixed_fee as number,
        rate_fee: row.rate_fee as number,
        actual_amount: row.actual_amount as number,
        type: row.type as number,
        status: row.status as number,
        reason: row.reason as string,
        created_at: row.created_at as string
      }))
      console.log('导出数据:', exportData)
    }
  }
]

// 转账类型文本转换
export const getTransferTypeText = (type: number) => {
  const typeMap: Record<number, string> = {
    1: '内部转账'
  }
  return typeMap[type] || '未知类型'
}

// 转账状态文本转换
export const getTransferStatusText = (status: number) => {
  const statusMap: Record<number, string> = {
    1: '待处理',
    2: '已完成',
    3: '已取消',
    4: '已失败'
  }
  return statusMap[status] || '未知状态'
}

// 获取状态样式类
export const getStatusTagClass = (status: number) => {
  const classMap: Record<number, string> = {
    1: 'status-pending',
    2: 'status-completed',
    3: 'status-canceled',
    4: 'status-failed'
  }
  return classMap[status] || 'status-unknown'
}

// 金额格式化
export const formatAmount = (amount: number | string, precision: number = 4) => {
  if (!amount && amount !== 0) return '0.0000'
  const num = typeof amount === 'string' ? parseFloat(amount) : amount
  return num.toFixed(precision)
}

// 计算总手续费
export const calculateTotalFee = (amount: number, fixedFee: number, rateFee: number) => {
  return fixedFee + (amount * rateFee) / 100
}

// 计算实际到账金额
export const calculateActualAmount = (amount: number, fixedFee: number, rateFee: number) => {
  const totalFee = calculateTotalFee(amount, fixedFee, rateFee)
  return Math.max(0, amount - totalFee)
}

// 计算手续费率
export const calculateFeeRate = (amount: number, totalFee: number) => {
  if (amount === 0) return '0.000'
  return ((totalFee / amount) * 100).toFixed(3)
}

// 导出配置
const exportConfig: ExportConfig = {
  enabled: true,
  filename: '用户转账数据导出',
  columns: [
    'id',
    'send_user_id',
    'receive_user_id',
    'asset_id',
    'amount',
    'fixed_fee',
    'rate_fee',
    'actual_amount',
    'type',
    'status',
    'reason',
    'created_at'
  ],
  formats: ['excel', 'csv'],
  maxRows: 10000
}

// 权限配置
const permissionConfig: PermissionConfig = {
  create: () => {
    return true
  },
  update: () => {
    return true
  },
  delete: () => {
    // 已完成的转账通常不允许删除
    return true
  },
  export: () => true,
  batchDelete: () => true
}

// 响应式配置
const responsiveConfig: ResponsiveConfig = {
  mobile: {
    hideColumns: ['fixed_fee', 'rate_fee', 'reason', 'created_at'],
    cardView: false,
    breakpoint: 768
  }
}

// 安全配置
const securityConfig: SecurityConfig = {
  enableXssProtection: true,
  allowedTags: ['p', 'br', 'strong', 'em'],
  allowedAttributes: {
    div: ['class'],
    span: ['class']
  }
}

// 性能配置
const performanceConfig: PerformanceConfig = {
  enableCache: true,
  cacheTime: 3 * 60 * 1000, // 3分钟
  debounceTime: 500,
  maxCacheSize: 100,
  lazyLoad: true,
  virtualScroll: {
    enabled: true,
    itemHeight: 75,
    buffer: 15
  }
}

// 用户转账管理配置
export const transferTableConfig: CrudTableConfig = {
  title: '用户转账',
  addButtonText: '新增转账',
  deleteButtonText: '删除转账',
  deleteConfirmText: '确定要删除该转账记录吗？',
  batchDeleteConfirmText: '确定要删除选中的 {count} 条转账记录吗？',
  emptyHeight: '500px',

  initialSearchState: {
    send_user_id: '',
    receive_user_id: '',
    asset_id: '',
    type: '',
    status: '',
    min_amount: '',
    max_amount: '',
    min_actual_amount: '',
    max_actual_amount: '',
    startTime: '',
    endTime: ''
  },

  searchFormItems: [
    {
      key: 'send_user_id',
      label: '发送用户ID',
      type: 'input',
      placeholder: '请输入发送用户ID'
    },
    {
      key: 'receive_user_id',
      label: '接收用户ID',
      type: 'input',
      placeholder: '请输入接收用户ID'
    },
    {
      key: 'asset_id',
      label: '资产ID',
      type: 'input',
      placeholder: '请输入资产ID'
    },
    {
      key: 'type',
      label: '转账类型',
      type: 'select',
      placeholder: '请选择转账类型',
      options: transferTypeOptions
    },
    {
      key: 'status',
      label: '转账状态',
      type: 'select',
      placeholder: '请选择转账状态',
      options: transferStatusOptions
    },
    {
      key: 'min_amount',
      label: '最小转账金额',
      type: 'input',
      placeholder: '请输入最小转账金额'
    },
    {
      key: 'max_amount',
      label: '最大转账金额',
      type: 'input',
      placeholder: '请输入最大转账金额'
    },
    {
      key: 'min_actual_amount',
      label: '最小到账金额',
      type: 'input',
      placeholder: '请输入最小到账金额'
    },
    {
      key: 'max_actual_amount',
      label: '最大到账金额',
      type: 'input',
      placeholder: '请输入最大到账金额'
    },
    {
      key: 'startTime',
      label: '开始时间',
      type: 'date',
      placeholder: '请选择开始时间'
    },
    {
      key: 'endTime',
      label: '结束时间',
      type: 'date',
      placeholder: '请选择结束时间'
    }
  ],

  tableColumns: [
    { type: 'selection', width: 55 },
    { label: 'ID', prop: 'id', width: 80, sortable: true },
    {
      label: '发送用户',
      prop: 'send_user',
      minWidth: 150,
      useSlot: true,
      slotName: 'sendUser'
    },
    {
      label: '接收用户',
      prop: 'receive_user',
      minWidth: 150,
      useSlot: true,
      slotName: 'receiveUser'
    },
    {
      label: '转账资产',
      prop: 'asset',
      minWidth: 120,
      useSlot: true,
      slotName: 'asset'
    },
    {
      label: '转账金额',
      prop: 'amount',
      minWidth: 130,
      useSlot: true,
      slotName: 'amount',
      sortable: true
    },
    {
      label: '手续费',
      prop: 'fee',
      minWidth: 150,
      useSlot: true,
      slotName: 'fee'
    },
    {
      label: '实际到账',
      prop: 'actual_amount',
      minWidth: 130,
      useSlot: true,
      slotName: 'actualAmount',
      sortable: true
    },
    {
      label: '转账类型',
      prop: 'type',
      width: 100,
      useSlot: true,
      slotName: 'transferType'
    },
    {
      label: '状态',
      prop: 'status',
      width: 100,
      useSlot: true,
      slotName: 'status'
    },
    {
      label: '原因',
      prop: 'reason',
      minWidth: 200,
      showOverflowTooltip: true
    },
    {
      label: '创建时间',
      prop: 'created_at',
      minWidth: 160,
      sortable: true,
      columnType: 'date'
    },
    {
      label: '操作',
      width: 250,
      fixed: 'right',
      useSlot: true,
      slotName: 'actions'
    }
  ],

  apiService: createTransferApiService(),

  // 分页参数映射
  paginationKey: {
    current: 'page',
    size: 'pageSize'
  },

  dialog: {
    title: {
      add: '添加用户转账',
      edit: '编辑用户转账'
    },
    width: '800px',
    formItems: [
      {
        label: '发送用户ID',
        prop: 'send_user_id',
        type: 'input',
        required: true,
        config: {
          placeholder: '请输入发送用户ID'
        },
        rules: [{ required: true, message: '请输入发送用户ID', trigger: 'blur' }],
        span: 12
      },
      {
        label: '接收用户ID',
        prop: 'receive_user_id',
        type: 'input',
        required: true,
        config: {
          placeholder: '请输入接收用户ID'
        },
        rules: [{ required: true, message: '请输入接收用户ID', trigger: 'blur' }],
        span: 12
      },
      {
        label: '资产ID',
        prop: 'asset_id',
        type: 'input',
        required: true,
        config: {
          placeholder: '请输入资产ID'
        },
        rules: [{ required: true, message: '请输入资产ID', trigger: 'blur' }],
        span: 12
      },
      {
        label: '转账类型',
        prop: 'type',
        type: 'select',
        required: true,
        options: transferTypeOptions,
        rules: [{ required: true, message: '请选择转账类型', trigger: 'change' }],
        span: 12
      },
      {
        label: '转账金额',
        prop: 'amount',
        type: 'input',
        required: true,
        config: {
          placeholder: '请输入转账金额',
          type: 'number',
          min: 0,
          step: 0.0001,
          precision: 4
        },
        rules: [
          { required: true, message: '请输入转账金额', trigger: 'blur' },
          { pattern: /^\d+(\.\d{1,4})?$/, message: '请输入有效的金额', trigger: 'blur' }
        ],
        span: 8
      },
      {
        label: '固定手续费',
        prop: 'fixed_fee',
        type: 'input',
        config: {
          placeholder: '请输入固定手续费',
          type: 'number',
          min: 0,
          step: 0.0001,
          precision: 4
        },
        rules: [{ pattern: /^\d+(\.\d{1,4})?$/, message: '请输入有效的手续费', trigger: 'blur' }],
        span: 8
      },
      {
        label: '比例手续费(%)',
        prop: 'rate_fee',
        type: 'input',
        config: {
          placeholder: '请输入比例手续费',
          type: 'number',
          min: 0,
          max: 100,
          step: 0.001,
          precision: 3
        },
        rules: [
          { pattern: /^\d+(\.\d{1,3})?$/, message: '请输入有效的手续费比例', trigger: 'blur' }
        ],
        span: 8
      },
      {
        label: '实际到账金额',
        prop: 'actual_amount',
        type: 'input',
        required: true,
        config: {
          placeholder: '请输入实际到账金额',
          type: 'number',
          min: 0,
          step: 0.0001,
          precision: 4
        },
        rules: [
          { required: true, message: '请输入实际到账金额', trigger: 'blur' },
          { pattern: /^\d+(\.\d{1,4})?$/, message: '请输入有效的金额', trigger: 'blur' }
        ],
        span: 12
      },
      {
        label: '状态',
        prop: 'status',
        type: 'select',
        required: true,
        options: transferStatusOptions,
        rules: [{ required: true, message: '请选择状态', trigger: 'change' }],
        span: 12
      },
      {
        label: '原因',
        prop: 'reason',
        type: 'textarea',
        config: {
          placeholder: '请输入原因说明',
          rows: 3,
          maxlength: 1024
        },
        rules: [{ max: 1024, message: '原因长度不能超过 1024 个字符', trigger: 'blur' }],
        span: 24
      }
    ],
    initialFormData: {
      send_user_id: '',
      receive_user_id: '',
      asset_id: '',
      amount: '0.0000',
      fixed_fee: '0.0000',
      rate_fee: '0.000',
      actual_amount: '0.0000',
      type: 1,
      status: 1,
      reason: ''
    }
  },

  // 功能配置
  batchActions,
  export: exportConfig,
  permissions: permissionConfig,
  responsive: responsiveConfig,
  security: securityConfig,
  performance: performanceConfig,

  // 操作权限
  allowBatchDelete: true
}
