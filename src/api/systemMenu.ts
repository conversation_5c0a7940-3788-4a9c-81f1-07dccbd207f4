import request from '@/utils/http'

/**
 * 菜单管理服务（前台菜单）
 * 类型：1 导航菜单 2 用户菜单 3 用户快捷菜单 4 首页快捷菜单
 * 模式：1 移动端 2 电脑端
 * 状态：1 启用 0 禁用
 */
export class SystemMenuService {
  private static baseUrl = '/system/front-menus'

  static readonly TYPE_TABBAR = 1
  static readonly TYPE_USERS = 2
  static readonly TYPE_USERS_QUICK = 3
  static readonly TYPE_HOME_QUICK = 4

  static readonly MODE_MOBILE = 1
  static readonly MODE_DESKTOP = 2

  static readonly STATUS_ENABLED = 1
  static readonly STATUS_DISABLED = 0

  // ============ 基础 CRUD ============
  static getList(params?: any) {
    return request.get<any>({ url: this.baseUrl, params })
  }

  static getTree(params?: any) {
    return request.get<any>({ url: `${this.baseUrl}/tree`, params })
  }

  static getDetail(id: number) {
    return request.get<any>({ url: `${this.baseUrl}/${id}` })
  }

  static create(data: any) {
    return request.post<any>({ url: this.baseUrl, data })
  }

  static update(id: number, data: any) {
    return request.put<any>({ url: `${this.baseUrl}/${id}`, data })
  }

  static remove(id: number) {
    return request.del<any>({ url: `${this.baseUrl}/${id}` })
  }

  static batchRemove(ids: number[]) {
    return request.del<any>({ url: `${this.baseUrl}/batch`, data: { ids } })
  }

  // ============ 状态/排序/移动 ============
  static enable(id: number) {
    return request.put<any>({ url: `${this.baseUrl}/${id}/enable` })
  }

  static disable(id: number) {
    return request.put<any>({ url: `${this.baseUrl}/${id}/disable` })
  }

  static setSort(id: number, sort: number) {
    return request.put<any>({ url: `${this.baseUrl}/${id}/sort`, data: { sort } })
  }

  static move(id: number, newParentId: number) {
    return request.put<any>({ url: `${this.baseUrl}/${id}/move`, data: { parent_id: newParentId } })
  }

  // ============ 导出/搜索 ============
  static exportList(params?: any) {
    return request.get<any>({ url: `${this.baseUrl}/export`, params, responseType: 'blob' })
  }

  static search(params: any) {
    return request.get<any>({ url: `${this.baseUrl}/search`, params })
  }
}
