import request from '@/utils/http'

export class CountryService {
  static baseUrl = '/system/country'

  /**
   * 获取国家列表
   */
  static async getCountryList(params: any): Promise<any> {
    return request.get<any>({
      url: `${this.baseUrl}`,
      params
    })
  }

  /**
   * 创建国家
   */
  static async createCountry(data: any): Promise<any> {
    return request.post<any>({
      url: `${this.baseUrl}`,
      data
    })
  }

  /**
   * 更新国家
   */
  static async updateCountry(id: number, data: any): Promise<any> {
    return request.put<any>({
      url: `${this.baseUrl}/${id}`,
      data
    })
  }

  /**
   * 删除国家
   */
  static async deleteCountry(id: number): Promise<any> {
    return request.del<any>({
      url: `${this.baseUrl}/${id}`
    })
  }

  /**
   * 批量删除国家
   */
  static async batchDeleteCountries(ids: number[]): Promise<any> {
    return request.del<any>({
      url: `${this.baseUrl}/batch`,
      data: { ids }
    })
  }

  /**
   * 获取国家详情
   */
  static async getCountryDetail(id: number): Promise<any> {
    return request.get<any>({
      url: `${this.baseUrl}/${id}`
    })
  }
}
