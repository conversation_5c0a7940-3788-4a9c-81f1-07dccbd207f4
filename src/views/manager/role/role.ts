import ManagerRoleService from '../../../api/managerRole'
import type {
  CrudTableConfig,
  BatchAction,
  ExportConfig,
  PermissionConfig,
  ResponsiveConfig,
  SecurityConfig,
  PerformanceConfig
} from '@/components/core/tables/ArtCrudTable.vue'

export const statusOptions = [
  { label: '启用', value: 1 },
  { label: '禁用', value: 0 }
]

export const dataScopeOptions = [
  { label: '全部', value: 1 },
  { label: '自定义', value: 2 },
  { label: '本部门', value: 3 },
  { label: '本部门及以下', value: 4 },
  { label: '仅本人', value: 5 }
]

export const yesNoOptions = [
  { label: '是', value: true },
  { label: '否', value: false }
]

export const initialSearchState = {
  name: '',
  code: '',
  status: undefined as number | undefined,
  dataScope: undefined as number | undefined,
  isSystem: undefined as boolean | undefined
}

export const searchFormItems = [
  { key: 'name', label: '角色名称', type: 'input', placeholder: '输入名称' },
  { key: 'code', label: '角色编码', type: 'input', placeholder: '输入编码' },
  { key: 'status', label: '状态', type: 'select', options: statusOptions },
  { key: 'dataScope', label: '数据范围', type: 'select', options: dataScopeOptions },
  { key: 'isSystem', label: '系统内置', type: 'select', options: yesNoOptions }
]

export const getStatusText = (status: number) =>
  status === 1 ? '启用' : status === 0 ? '禁用' : '未知'
export const getStatusClass = (status: number) =>
  status === 1 ? 'status-enabled' : status === 0 ? 'status-disabled' : 'status-unknown'

export const tableColumns = [
  { prop: 'id', label: 'ID', width: 80, fixed: 'left' as const, sortable: true, align: 'center' },
  { prop: 'name', label: '名称', minWidth: 140 },
  { prop: 'code', label: '编码', minWidth: 120 },
  { prop: 'status', label: '状态', width: 100, slot: 'status', align: 'center' },
  { prop: 'sort', label: '排序', width: 90, align: 'center', sortable: true },
  { prop: 'dataScope', label: '数据范围', minWidth: 120 },
  { prop: 'isSystem', label: '系统内置', width: 100, slot: 'isSystem', align: 'center' },
  { prop: 'remark', label: '备注', minWidth: 180 },
  { prop: 'created_at', label: '创建时间', minWidth: 160, align: 'center' },
  {
    prop: 'actions',
    label: '操作',
    width: 220,
    fixed: 'right' as const,
    slot: 'actions',
    align: 'center'
  }
]

const createApiService = () => ({
  getList: (params: any) => ManagerRoleService.list(params),
  delete: (id: string | number) => ManagerRoleService.delete(id),
  batchDelete: (ids: Array<string | number>) => ManagerRoleService.batchDelete(ids),
  update: (id: string | number, data: any) => ManagerRoleService.update(id, data),
  create: (data: any) => ManagerRoleService.create(data)
})

const batchActions: BatchAction[] = [
  {
    key: 'batchEnable',
    label: '批量启用',
    type: 'success',
    icon: 'Open',
    handler: (rows: any[]) => ManagerRoleService.batchEnable(rows.map((r) => r.id))
  },
  {
    key: 'batchDisable',
    label: '批量禁用',
    type: 'warning',
    icon: 'Close',
    handler: (rows: any[]) => ManagerRoleService.batchDisable(rows.map((r) => r.id))
  },
  {
    key: 'batchDelete',
    label: '批量删除',
    type: 'danger',
    icon: 'Delete',
    confirm: '确认删除选中的 {count} 个角色吗？',
    handler: (rows: any[]) => ManagerRoleService.batchDelete(rows.map((r) => r.id))
  }
]

const exportConfig: ExportConfig = {
  enabled: true,
  filename: '角色数据导出',
  columns: [
    'id',
    'name',
    'code',
    'status',
    'sort',
    'dataScope',
    'isSystem',
    'remark',
    'created_at'
  ],
  formats: ['excel', 'csv']
}

const permissions: PermissionConfig = { create: true, update: true, delete: true, export: true }
const responsive: ResponsiveConfig = {
  mobile: { hideColumns: ['remark', 'created_at'], breakpoint: 768, cardView: false }
}
const security: SecurityConfig = {
  enableXssProtection: true,
  allowedTags: ['span', 'div'],
  allowedAttributes: { div: ['class'], span: ['class'] }
}
const performance: PerformanceConfig = {
  enableCache: true,
  cacheTime: 3 * 60 * 1000,
  debounceTime: 300,
  maxCacheSize: 100,
  lazyLoad: true,
  virtualScroll: { enabled: false, itemHeight: 60, buffer: 10 }
}

export const roleTableConfig: CrudTableConfig = {
  title: '角色管理',
  addButtonText: '新增角色',
  deleteButtonText: '删除角色',
  deleteConfirmText: '确定要删除该角色吗？',
  batchDeleteConfirmText: '确定要删除选中的 {count} 个角色吗？',

  initialSearchState,
  searchFormItems,
  tableColumns,

  apiService: createApiService(),
  paginationKey: { current: 'page', size: 'page_size' },

  dialog: {
    formItems: [
      {
        prop: 'name',
        label: '角色名称',
        type: 'input' as const,
        rules: [{ required: true, message: '必填', trigger: 'blur' }],
        span: 12
      },
      {
        prop: 'code',
        label: '角色编码',
        type: 'input' as const,
        rules: [{ required: true, message: '必填', trigger: 'blur' }],
        span: 12
      },
      { prop: 'status', label: '状态', type: 'select' as const, options: statusOptions, span: 12 },
      { prop: 'sort', label: '排序', type: 'number' as const, span: 12, config: { precision: 0 } },
      {
        prop: 'dataScope',
        label: '数据范围',
        type: 'select' as const,
        options: dataScopeOptions,
        span: 12
      },
      {
        prop: 'isSystem',
        label: '系统内置',
        type: 'select' as const,
        options: yesNoOptions,
        span: 12
      },
      { prop: 'remark', label: '备注', type: 'textarea' as const, span: 24, config: { rows: 3 } }
    ]
  },

  batchActions,
  export: exportConfig,
  permissions,
  responsive,
  security,
  performance,

  hasStatusSwitch: true,
  statusField: 'status',
  statusActiveValue: 1,
  statusInactiveValue: 0,
  allowBatchDelete: true
}

export default roleTableConfig
