import ManagerService from '../../../api/managerManagers'
import type {
  CrudTableConfig,
  BatchAction,
  ExportConfig,
  PermissionConfig,
  ResponsiveConfig,
  SecurityConfig,
  PerformanceConfig
} from '@/components/core/tables/ArtCrudTable.vue'

export const genderOptions = [
  { label: '保密', value: 0 },
  { label: '男', value: 1 },
  { label: '女', value: 2 }
]

export const statusOptions = [
  { label: '正常', value: 10 },
  { label: '禁用', value: 20 },
  { label: '锁定', value: 30 },
  { label: '过期', value: 40 }
]

export const loginModeOptions = [
  { label: '单设备登录', value: 1 },
  { label: '多设备登录', value: 2 },
  { label: '限制设备数', value: 3 }
]

export const initialSearchState = {
  username: '',
  nickname: '',
  mobile: '',
  email: '',
  gender: undefined as number | undefined,
  loginMode: undefined as number | undefined,
  status: undefined as number | undefined
}

export const searchFormItems = [
  { key: 'username', label: '用户名', type: 'input' },
  { key: 'nickname', label: '昵称', type: 'input' },
  { key: 'mobile', label: '手机号', type: 'input' },
  { key: 'email', label: '邮箱', type: 'input' },
  { key: 'gender', label: '性别', type: 'select', options: genderOptions },
  { key: 'loginMode', label: '登录模式', type: 'select', options: loginModeOptions },
  { key: 'status', label: '状态', type: 'select', options: statusOptions }
]

export const getStatusText = (status: number) =>
  status === 10
    ? '正常'
    : status === 20
      ? '禁用'
      : status === 30
        ? '锁定'
        : status === 40
          ? '过期'
          : '未知'
export const getStatusClass = (status: number) =>
  status === 10
    ? 'status-enabled'
    : status === 20
      ? 'status-disabled'
      : status === 30
        ? 'status-locked'
        : status === 40
          ? 'status-expired'
          : 'status-unknown'

export const tableColumns = [
  { prop: 'id', label: 'ID', width: 80, fixed: 'left' as const, sortable: true, align: 'center' },
  { prop: 'avatar', label: '头像', width: 80, slot: 'avatar', align: 'center' },
  { prop: 'username', label: '用户名', minWidth: 140 },
  { prop: 'nickname', label: '昵称', minWidth: 120 },
  { prop: 'mobile', label: '手机号', minWidth: 140 },
  { prop: 'email', label: '邮箱', minWidth: 180 },
  { prop: 'gender', label: '性别', width: 90, slot: 'gender', align: 'center' },
  { prop: 'loginMode', label: '登录模式', minWidth: 120, slot: 'loginMode' },
  { prop: 'maxDevices', label: '设备上限', width: 100, align: 'center' },
  { prop: 'lastLoginIp', label: '最后登录IP', minWidth: 140 },
  { prop: 'lastLoginTime', label: '最后登录时间', minWidth: 160 },
  { prop: 'status', label: '状态', width: 100, slot: 'status', align: 'center' },
  { prop: 'expiredAt', label: '过期时间', minWidth: 160 },
  { prop: 'customer_service_url', label: '客服链接', minWidth: 180 },
  { prop: 'created_at', label: '创建时间', minWidth: 160, align: 'center' },
  {
    prop: 'actions',
    label: '操作',
    width: 220,
    fixed: 'right' as const,
    slot: 'actions',
    align: 'center'
  }
]

const createApiService = () => ({
  getList: (params: any) => ManagerService.list(params),
  delete: (id: string | number) => ManagerService.delete(id),
  batchDelete: (ids: Array<string | number>) => ManagerService.batchDelete(ids),
  update: (id: string | number, data: any) => ManagerService.update(id, data),
  create: (data: any) => ManagerService.create(data)
})

const batchActions: BatchAction[] = [
  {
    key: 'batchEnable',
    label: '批量启用',
    type: 'success',
    icon: 'Open',
    handler: (rows: any[]) => ManagerService.batchEnable(rows.map((r) => r.id))
  },
  {
    key: 'batchDisable',
    label: '批量禁用',
    type: 'warning',
    icon: 'Close',
    handler: (rows: any[]) => ManagerService.batchDisable(rows.map((r) => r.id))
  },
  {
    key: 'batchDelete',
    label: '批量删除',
    type: 'danger',
    icon: 'Delete',
    confirm: '确认删除选中的 {count} 个管理员吗？',
    handler: (rows: any[]) => ManagerService.batchDelete(rows.map((r) => r.id))
  }
]

const exportConfig: ExportConfig = {
  enabled: true,
  filename: '管理员数据导出',
  columns: [
    'id',
    'username',
    'nickname',
    'mobile',
    'email',
    'gender',
    'loginMode',
    'maxDevices',
    'lastLoginIp',
    'lastLoginTime',
    'status',
    'expiredAt',
    'customer_service_url',
    'created_at'
  ],
  formats: ['excel', 'csv']
}

const permissions: PermissionConfig = { create: true, update: true, delete: true, export: true }
const responsive: ResponsiveConfig = {
  mobile: {
    hideColumns: ['email', 'customer_service_url', 'created_at'],
    breakpoint: 768,
    cardView: false
  }
}
const security: SecurityConfig = {
  enableXssProtection: true,
  allowedTags: ['span', 'div'],
  allowedAttributes: { div: ['class'], span: ['class'] }
}
const performance: PerformanceConfig = {
  enableCache: true,
  cacheTime: 3 * 60 * 1000,
  debounceTime: 300,
  maxCacheSize: 100,
  lazyLoad: true,
  virtualScroll: { enabled: false, itemHeight: 60, buffer: 10 }
}

export const managersTableConfig: CrudTableConfig = {
  title: '管理员',
  addButtonText: '新增管理员',
  deleteButtonText: '删除管理员',
  deleteConfirmText: '确定要删除该管理员吗？',
  batchDeleteConfirmText: '确定要删除选中的 {count} 个管理员吗？',

  initialSearchState,
  searchFormItems,
  tableColumns,

  apiService: createApiService(),
  paginationKey: { current: 'page', size: 'page_size' },

  dialog: {
    formItems: [
      {
        prop: 'username',
        label: '用户名',
        type: 'input' as const,
        rules: [{ required: true, message: '必填', trigger: 'blur' }],
        span: 12
      },
      { prop: 'nickname', label: '昵称', type: 'input' as const, span: 12 },
      { prop: 'mobile', label: '手机号', type: 'input' as const, span: 12 },
      { prop: 'email', label: '邮箱', type: 'input' as const, span: 12 },
      { prop: 'avatar', label: '头像', type: 'upload' as const, span: 12 },
      { prop: 'gender', label: '性别', type: 'select' as const, options: genderOptions, span: 12 },
      {
        prop: 'loginMode',
        label: '登录模式',
        type: 'select' as const,
        options: loginModeOptions,
        span: 12
      },
      {
        prop: 'maxDevices',
        label: '设备上限',
        type: 'number' as const,
        span: 12,
        config: { precision: 0 }
      },
      { prop: 'status', label: '状态', type: 'select' as const, options: statusOptions, span: 12 },
      { prop: 'customer_service_url', label: '客服链接', type: 'input' as const, span: 12 },
      { prop: 'remark', label: '备注', type: 'textarea' as const, span: 24, config: { rows: 3 } }
    ]
  },

  batchActions,
  export: exportConfig,
  permissions,
  responsive,
  security,
  performance,

  hasStatusSwitch: true,
  statusField: 'status',
  statusActiveValue: 10,
  statusInactiveValue: 20,
  allowBatchDelete: true
}

export default managersTableConfig
