import request from '@/utils/http'

export class UserAssetService {
  private static baseUrl = '/wallet/asset'

  // 获取用户资产列表
  static getUserAssetList(params?: any) {
    return request.get<any>({
      url: this.baseUrl,
      params
    })
  }

  // 创建用户资产
  static createUserAsset(data: any) {
    return request.post<any>({
      url: this.baseUrl,
      data
    })
  }

  // 更新用户资产
  static updateUserAsset(id: number, data: any) {
    return request.put<any>({
      url: `${this.baseUrl}/${id}`,
      data
    })
  }

  // 删除用户资产
  static deleteUserAsset(id: number) {
    return request.del<any>({
      url: `${this.baseUrl}/${id}`
    })
  }

  // 批量删除用户资产
  static batchDeleteUserAssets(ids: number[]) {
    return request.del<any>({
      url: `${this.baseUrl}/batch`,
      data: { ids }
    })
  }
}
