import request from '@/utils/http'

/**
 * 用户管理服务类
 * 对应的Go结构体：
 * - User: 用户主表结构
 * - UserListRequest: 用户列表请求参数
 * - UserCreateRequest: 用户创建请求参数
 * - UserUpdateRequest: 用户更新请求参数
 */
export class UserService {
  private static baseUrl = '/user/users'

  // ==================== 认证相关 ====================
  // 登录
  static login(params: Api.Auth.LoginParams) {
    return request.post<Api.Auth.LoginResponse>({
      url: '/auth/login',
      params
      // showErrorMessage: false // 不显示错误消息
    })
  }

  // 获取当前用户信息
  static getUserInfo() {
    return request.get<Api.User.UserInfo>({
      url: '/manager/info'
      // 自定义请求头
      // headers: {
      //   'X-Custom-Header': 'your-custom-value'
      // }
    })
  }

  // ==================== 用户管理 ====================
  // 获取用户列表 (对应 UserListRequest)
  static getUserList(params?: any) {
    return request.get<any>({
      url: this.baseUrl,
      params
    })
  }

  // 获取用户详情
  static getUserDetail(id: number) {
    return request.get<any>({
      url: `${this.baseUrl}/${id}`
    })
  }

  // 创建用户 (对应 UserCreateRequest)
  static createUser(data: any) {
    return request.post<any>({
      url: this.baseUrl,
      data
    })
  }

  // 更新用户 (对应 UserUpdateRequest)
  static updateUser(id: number, data: any) {
    return request.put<any>({
      url: `${this.baseUrl}/${id}`,
      data
    })
  }

  // 删除用户
  static deleteUser(id: number) {
    return request.del<any>({
      url: `${this.baseUrl}/${id}`
    })
  }

  // 批量删除用户
  static batchDeleteUsers(ids: number[]) {
    return request.del<any>({
      url: `${this.baseUrl}/batch`,
      data: { ids }
    })
  }

  // ==================== 用户状态管理 ====================
  // 修改用户状态
  static updateUserStatus(id: number, status: number) {
    return request.put<any>({
      url: `${this.baseUrl}/${id}/status`,
      data: { status }
    })
  }

  // 批量修改用户状态
  static batchUpdateUserStatus(ids: number[], status: number) {
    return request.put<any>({
      url: `${this.baseUrl}/batch/status`,
      data: { ids, status }
    })
  }

  // 冻结用户
  static freezeUser(id: number) {
    return this.updateUserStatus(id, 3)
  }

  // 解冻用户
  static unfreezeUser(id: number) {
    return this.updateUserStatus(id, 1)
  }

  // 锁定用户
  static lockUser(id: number, lockedUntil?: string) {
    return request.put<any>({
      url: `${this.baseUrl}/${id}/lock`,
      data: { lockedUntil }
    })
  }

  // 解锁用户
  static unlockUser(id: number) {
    return request.put<any>({
      url: `${this.baseUrl}/${id}/unlock`
    })
  }

  // ==================== 密码管理 ====================
  // 重置用户密码
  static resetUserPassword(id: number, newPassword: string) {
    return request.put<any>({
      url: `${this.baseUrl}/${id}/password/reset`,
      data: { password: newPassword }
    })
  }

  // 修改用户密码
  static changeUserPassword(id: number, oldPassword: string, newPassword: string) {
    return request.put<any>({
      url: `${this.baseUrl}/${id}/password/change`,
      data: { oldPassword, newPassword }
    })
  }

  // 重置支付密码
  static resetSecurityKey(id: number, newSecurityKey: string) {
    return request.put<any>({
      url: `${this.baseUrl}/${id}/security/reset`,
      data: { securityKey: newSecurityKey }
    })
  }

  // ==================== 文件上传 ====================
  // 上传用户头像
  static uploadUserAvatar(id: number, file: File) {
    const formData = new FormData()
    formData.append('avatar', file)
    return request.post<any>({
      url: `${this.baseUrl}/${id}/avatar`,
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }

  // ==================== 用户资产相关 ====================
  // 获取用户资产信息
  static getUserAssets(id: number) {
    return request.get<any>({
      url: `${this.baseUrl}/${id}/assets`
    })
  }

  // 调整用户余额
  static adjustUserBalance(
    id: number,
    amount: number,
    type: 'available' | 'frozen',
    reason: string
  ) {
    return request.put<any>({
      url: `${this.baseUrl}/${id}/balance/adjust`,
      data: { amount, type, reason }
    })
  }

  // 获取用户积分记录
  static getUserIntegralHistory(id: number, params?: any) {
    return request.get<any>({
      url: `${this.baseUrl}/${id}/integral/history`,
      params
    })
  }

  // 调整用户积分
  static adjustUserIntegral(id: number, integral: number, reason: string) {
    return request.put<any>({
      url: `${this.baseUrl}/${id}/integral/adjust`,
      data: { integral, reason }
    })
  }

  // ==================== 用户认证相关 ====================
  // 获取用户认证信息
  static getUserCertification(id: number) {
    return request.get<any>({
      url: `${this.baseUrl}/${id}/certification`
    })
  }

  // 审核用户认证
  static reviewUserCertification(id: number, status: number, reason?: string) {
    return request.put<any>({
      url: `${this.baseUrl}/${id}/certification/review`,
      data: { status, reason }
    })
  }

  // ==================== 统计数据 ====================
  // 获取用户统计数据
  static getUserStats() {
    return request.get<any>({
      url: `${this.baseUrl}/stats`
    })
  }

  // 获取用户登录记录
  static getUserLoginHistory(id: number, params?: any) {
    return request.get<any>({
      url: `${this.baseUrl}/${id}/login/history`,
      params
    })
  }

  // ==================== 导出功能 ====================
  // 导出用户列表
  static exportUserList(params?: any) {
    return request.get<any>({
      url: `${this.baseUrl}/export`,
      params,
      responseType: 'blob'
    })
  }

  // ==================== 用户认证管理 ====================

  // 获取用户认证列表
  static getUserCertificationList(params?: any) {
    return request.get<any>({
      url: '/user/certifications',
      params
    })
  }

  // 获取用户认证详情
  static getCertificationDetail(id: number) {
    return request.get<any>({
      url: `/user/certifications/${id}`
    })
  }

  // 创建用户认证
  static createCertification(data: any) {
    return request.post<any>({
      url: '/user/certifications',
      data
    })
  }

  // 更新用户认证
  static updateCertification(id: number, data: any) {
    return request.put<any>({
      url: `/user/certifications/${id}`,
      data
    })
  }

  // 删除用户认证
  static deleteCertification(id: number) {
    return request.del<any>({
      url: `/user/certifications/${id}`
    })
  }

  // 批量删除用户认证
  static batchDeleteCertifications(ids: number[]) {
    return request.del<any>({
      url: '/user/certifications/batch',
      data: { ids }
    })
  }

  // 批量审核用户认证
  static batchReviewCertifications(ids: number[], status: number, reason?: string) {
    return request.put<any>({
      url: '/user/certifications/batch/review',
      data: { ids, status, reason }
    })
  }

  // 上传认证证件照
  static uploadCertificationPhoto(file: File, type: 'photo1' | 'photo2' | 'photo3') {
    const formData = new FormData()
    formData.append('photo', file)
    formData.append('type', type)

    return request.post<any>({
      url: '/user/certifications/upload',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }

  // 获取认证统计数据
  static getCertificationStats() {
    return request.get<any>({
      url: '/user/certifications/stats'
    })
  }

  // 导出认证列表
  static exportCertificationList(params?: any) {
    return request.get<any>({
      url: '/user/certifications/export',
      params,
      responseType: 'blob'
    })
  }

  // ==================== 用户通知管理 ====================

  // 获取用户通知列表
  static getUserNoticeList(params?: any) {
    return request.get<any>({
      url: '/user/notices',
      params
    })
  }

  // 获取用户通知详情
  static getNoticeDetail(id: number) {
    return request.get<any>({
      url: `/user/notices/${id}`
    })
  }

  // 创建用户通知
  static createNotice(data: any) {
    return request.post<any>({
      url: '/user/notices',
      data
    })
  }

  // 更新用户通知
  static updateNotice(id: number, data: any) {
    return request.put<any>({
      url: `/user/notices/${id}`,
      data
    })
  }

  // 删除用户通知
  static deleteNotice(id: number) {
    return request.del<any>({
      url: `/user/notices/${id}`
    })
  }

  // 批量删除用户通知
  static batchDeleteNotices(ids: number[]) {
    return request.del<any>({
      url: '/user/notices/batch',
      data: { ids }
    })
  }

  // 标记通知为已读
  static markNoticeAsRead(id: number) {
    return request.put<any>({
      url: `/user/notices/${id}/read`
    })
  }

  // 批量标记通知为已读
  static batchMarkNoticesAsRead(ids: number[]) {
    return request.put<any>({
      url: '/user/notices/batch/read',
      data: { ids }
    })
  }

  // 标记所有通知为已读
  static markAllNoticesAsRead(userId: number) {
    return request.put<any>({
      url: `/user/notices/user/${userId}/read-all`
    })
  }

  // 获取用户未读通知数量
  static getUnreadNoticeCount(userId: number) {
    return request.get<any>({
      url: `/user/notices/user/${userId}/unread-count`
    })
  }

  // 获取用户通知列表（按类型）
  static getUserNoticesByType(userId: number, type: number, params?: any) {
    return request.get<any>({
      url: `/user/notices/user/${userId}/type/${type}`,
      params
    })
  }

  // 设置通知状态
  static setNoticeStatus(id: number, status: number) {
    return request.put<any>({
      url: `/user/notices/${id}/status`,
      data: { status }
    })
  }

  // 批量设置通知状态
  static batchSetNoticeStatus(ids: number[], status: number) {
    return request.put<any>({
      url: '/user/notices/batch/status',
      data: { ids, status }
    })
  }

  // 置顶通知
  static pinNotice(id: number) {
    return this.setNoticeStatus(id, 2)
  }

  // 取消置顶通知
  static unpinNotice(id: number) {
    return this.setNoticeStatus(id, 1)
  }

  // 隐藏通知
  static hideNotice(id: number) {
    return this.setNoticeStatus(id, 3)
  }

  // 设置通知失效
  static expireNotice(id: number) {
    return this.setNoticeStatus(id, 4)
  }

  // 发送系统通知
  static sendSystemNotice(data: any) {
    return request.post<any>({
      url: '/user/notices/system',
      data
    })
  }

  // 批量发送通知
  static batchSendNotices(data: any) {
    return request.post<any>({
      url: '/user/notices/batch/send',
      data
    })
  }

  // 获取通知统计数据
  static getNoticeStats(params?: any) {
    return request.get<any>({
      url: '/user/notices/stats',
      params
    })
  }

  // 获取用户通知统计
  static getUserNoticeStats(userId: number) {
    return request.get<any>({
      url: `/user/notices/user/${userId}/stats`
    })
  }

  // 清理过期通知
  static cleanExpiredNotices() {
    return request.del<any>({
      url: '/user/notices/expired/clean'
    })
  }

  // 导出通知列表
  static exportNoticeList(params?: any) {
    return request.get<any>({
      url: '/user/notices/export',
      params,
      responseType: 'blob'
    })
  }

  // 获取通知模板列表
  static getNoticeTemplates() {
    return request.get<any>({
      url: '/user/notices/templates'
    })
  }

  // 根据模板创建通知
  static createNoticeFromTemplate(templateId: number, data: any) {
    return request.post<any>({
      url: `/user/notices/template/${templateId}`,
      data
    })
  }

  // 获取用户最新通知
  static getUserLatestNotices(userId: number, limit: number = 10) {
    return request.get<any>({
      url: `/user/notices/user/${userId}/latest`,
      params: { limit }
    })
  }

  // 搜索通知
  static searchNotices(params: any) {
    return request.get<any>({
      url: '/user/notices/search',
      params
    })
  }

  // ==================== 用户等级管理 ====================

  // 获取用户等级列表
  static getUserLevelList(params?: any) {
    return request.get<any>({
      url: '/user/levels',
      params
    })
  }

  // 获取用户等级详情
  static getUserLevelDetail(id: number) {
    return request.get<any>({
      url: `/user/levels/${id}`
    })
  }

  // 创建用户等级
  static createUserLevel(data: any) {
    return request.post<any>({
      url: '/user/levels',
      data
    })
  }

  // 更新用户等级
  static updateUserLevel(id: number, data: any) {
    return request.put<any>({
      url: `/user/levels/${id}`,
      data
    })
  }

  // 删除用户等级
  static deleteUserLevel(id: number) {
    return request.del<any>({
      url: `/user/levels/${id}`
    })
  }

  // 批量删除用户等级
  static batchDeleteUserLevels(ids: number[]) {
    return request.del<any>({
      url: '/user/levels/batch',
      data: { ids }
    })
  }

  // 激活用户等级
  static activateUserLevel(id: number) {
    return request.put<any>({
      url: `/user/levels/${id}/activate`
    })
  }

  // 禁用用户等级
  static disableUserLevel(id: number) {
    return request.put<any>({
      url: `/user/levels/${id}/disable`
    })
  }

  // 批量激活用户等级
  static batchActivateUserLevels(ids: number[]) {
    return request.put<any>({
      url: '/user/levels/batch/activate',
      data: { ids }
    })
  }

  // 批量禁用用户等级
  static batchDisableUserLevels(ids: number[]) {
    return request.put<any>({
      url: '/user/levels/batch/disable',
      data: { ids }
    })
  }

  // 延期用户等级
  static extendUserLevel(id: number, days: number) {
    return request.put<any>({
      url: `/user/levels/${id}/extend`,
      data: { days }
    })
  }

  // 批量延期用户等级
  static batchExtendUserLevels(ids: number[], days: number) {
    return request.put<any>({
      url: '/user/levels/batch/extend',
      data: { ids, days }
    })
  }

  // 设置用户等级过期时间
  static setUserLevelExpiredAt(id: number, expiredAt: string) {
    return request.put<any>({
      url: `/user/levels/${id}/expired-at`,
      data: { expired_at: expiredAt }
    })
  }

  // 获取用户的所有等级
  static getUserLevels(userId: number, params?: any) {
    return request.get<any>({
      url: `/user/levels/user/${userId}`,
      params
    })
  }

  // 获取用户当前激活的等级
  static getUserActiveLevel(userId: number) {
    return request.get<any>({
      url: `/user/levels/user/${userId}/active`
    })
  }

  // 为用户分配等级
  static assignUserLevel(userId: number, levelId: number, type: number, expiredAt?: string) {
    return request.post<any>({
      url: `/user/levels/assign`,
      data: { user_id: userId, level_id: levelId, type, expired_at: expiredAt }
    })
  }

  // 批量为用户分配等级
  static batchAssignUserLevel(
    userIds: number[],
    levelId: number,
    type: number,
    expiredAt?: string
  ) {
    return request.post<any>({
      url: '/user/levels/batch/assign',
      data: { user_ids: userIds, level_id: levelId, type, expired_at: expiredAt }
    })
  }

  // 升级用户等级
  static upgradeUserLevel(userId: number, newLevelId: number, type: number = 1) {
    return request.put<any>({
      url: `/user/levels/user/${userId}/upgrade`,
      data: { level_id: newLevelId, type }
    })
  }

  // 降级用户等级
  static downgradeUserLevel(userId: number, newLevelId: number, reason?: string) {
    return request.put<any>({
      url: `/user/levels/user/${userId}/downgrade`,
      data: { level_id: newLevelId, reason }
    })
  }

  // 获取等级统计数据
  static getUserLevelStats() {
    return request.get<any>({
      url: '/user/levels/stats'
    })
  }

  // 获取按类型分组的等级统计
  static getUserLevelStatsByType() {
    return request.get<any>({
      url: '/user/levels/stats/by-type'
    })
  }

  // 获取即将过期的等级列表
  static getExpiringUserLevels(days: number = 7, params?: any) {
    return request.get<any>({
      url: '/user/levels/expiring',
      params: { days, ...params }
    })
  }

  // 获取已过期的等级列表
  static getExpiredUserLevels(params?: any) {
    return request.get<any>({
      url: '/user/levels/expired',
      params
    })
  }

  // 清理已过期的等级
  static cleanExpiredUserLevels() {
    return request.del<any>({
      url: '/user/levels/expired/clean'
    })
  }

  // 续费用户等级
  static renewUserLevel(id: number, months: number) {
    return request.put<any>({
      url: `/user/levels/${id}/renew`,
      data: { months }
    })
  }

  // 冻结用户等级
  static freezeUserLevel(id: number, reason?: string) {
    return request.put<any>({
      url: `/user/levels/${id}/freeze`,
      data: { reason }
    })
  }

  // 解冻用户等级
  static unfreezeUserLevel(id: number) {
    return request.put<any>({
      url: `/user/levels/${id}/unfreeze`
    })
  }

  // 转移用户等级
  static transferUserLevel(fromUserId: number, toUserId: number, levelId: number) {
    return request.put<any>({
      url: '/user/levels/transfer',
      data: { from_user_id: fromUserId, to_user_id: toUserId, level_id: levelId }
    })
  }

  // 获取等级变更记录
  static getUserLevelHistory(userId: number, params?: any) {
    return request.get<any>({
      url: `/user/levels/user/${userId}/history`,
      params
    })
  }

  // 导出用户等级数据
  static exportUserLevelList(params?: any) {
    return request.get<any>({
      url: '/user/levels/export',
      params,
      responseType: 'blob'
    })
  }

  // 搜索用户等级
  static searchUserLevels(params: any) {
    return request.get<any>({
      url: '/user/levels/search',
      params
    })
  }

  // 验证用户等级权限
  static verifyUserLevelPermission(userId: number, permission: string) {
    return request.get<any>({
      url: `/user/levels/user/${userId}/verify/${permission}`
    })
  }

  // 获取用户等级权益列表
  static getUserLevelBenefits(userId: number) {
    return request.get<any>({
      url: `/user/levels/user/${userId}/benefits`
    })
  }

  // ==================== 用户账户管理 ====================

  // 获取用户账户列表
  static getUserAccountList(params?: any) {
    return request.get<any>({
      url: '/user/accounts',
      params
    })
  }

  // 获取用户账户详情
  static getUserAccountDetail(id: number) {
    return request.get<any>({
      url: `/user/accounts/${id}`
    })
  }

  // 创建用户账户
  static createUserAccount(data: any) {
    return request.post<any>({
      url: '/user/accounts',
      data
    })
  }

  // 更新用户账户
  static updateUserAccount(id: number, data: any) {
    return request.put<any>({
      url: `/user/accounts/${id}`,
      data
    })
  }

  // 删除用户账户
  static deleteUserAccount(id: number) {
    return request.del<any>({
      url: `/user/accounts/${id}`
    })
  }

  // 批量删除用户账户
  static batchDeleteUserAccounts(ids: number[]) {
    return request.del<any>({
      url: '/user/accounts/batch',
      data: { ids }
    })
  }

  // 启用用户账户
  static enableUserAccount(id: number) {
    return request.put<any>({
      url: `/user/accounts/${id}/enable`
    })
  }

  // 禁用用户账户
  static disableUserAccount(id: number) {
    return request.put<any>({
      url: `/user/accounts/${id}/disable`
    })
  }

  // 批量启用用户账户
  static batchEnableUserAccounts(ids: number[]) {
    return request.put<any>({
      url: '/user/accounts/batch/enable',
      data: { ids }
    })
  }

  // 批量禁用用户账户
  static batchDisableUserAccounts(ids: number[]) {
    return request.put<any>({
      url: '/user/accounts/batch/disable',
      data: { ids }
    })
  }

  // 验证用户账户
  static verifyUserAccount(id: number) {
    return request.put<any>({
      url: `/user/accounts/${id}/verify`
    })
  }

  // 批量验证用户账户
  static batchVerifyUserAccounts(ids: number[]) {
    return request.put<any>({
      url: '/user/accounts/batch/verify',
      data: { ids }
    })
  }

  // 冻结用户账户
  static freezeUserAccount(id: number, reason?: string) {
    return request.put<any>({
      url: `/user/accounts/${id}/freeze`,
      data: { reason }
    })
  }

  // 解冻用户账户
  static unfreezeUserAccount(id: number) {
    return request.put<any>({
      url: `/user/accounts/${id}/unfreeze`
    })
  }

  // 获取用户的所有账户
  static getUserAccounts(userId: number, params?: any) {
    return request.get<any>({
      url: `/user/accounts/user/${userId}`,
      params
    })
  }

  // 获取用户指定类型的账户
  static getUserAccountsByType(userId: number, type: number, params?: any) {
    return request.get<any>({
      url: `/user/accounts/user/${userId}/type/${type}`,
      params
    })
  }

  // 获取用户默认账户
  static getUserDefaultAccount(userId: number, assetId: number) {
    return request.get<any>({
      url: `/user/accounts/user/${userId}/default/${assetId}`
    })
  }

  // 设置用户默认账户
  static setUserDefaultAccount(userId: number, accountId: number) {
    return request.put<any>({
      url: `/user/accounts/user/${userId}/default`,
      data: { account_id: accountId }
    })
  }

  // 验证账户信息
  static validateAccountInfo(data: any) {
    return request.post<any>({
      url: '/user/accounts/validate',
      data
    })
  }

  // 获取账户统计数据
  static getUserAccountStats() {
    return request.get<any>({
      url: '/user/accounts/stats'
    })
  }

  // 获取按类型分组的账户统计
  static getUserAccountStatsByType() {
    return request.get<any>({
      url: '/user/accounts/stats/by-type'
    })
  }

  // 获取按资产分组的账户统计
  static getUserAccountStatsByAsset() {
    return request.get<any>({
      url: '/user/accounts/stats/by-asset'
    })
  }

  // 获取账户余额操作记录
  static getUserAccountOperationHistory(accountId: number, params?: any) {
    return request.get<any>({
      url: `/user/accounts/${accountId}/operations`,
      params
    })
  }

  // 执行账户余额操作
  static executeAccountOperation(accountId: number, data: any) {
    return request.post<any>({
      url: `/user/accounts/${accountId}/operation`,
      data
    })
  }

  // 批量执行账户余额操作
  static batchExecuteAccountOperations(operations: any[]) {
    return request.post<any>({
      url: '/user/accounts/batch/operations',
      data: { operations }
    })
  }

  // 获取账户变更日志
  static getUserAccountChangeLogs(accountId: number, params?: any) {
    return request.get<any>({
      url: `/user/accounts/${accountId}/logs`,
      params
    })
  }

  // 导出用户账户数据
  static exportUserAccountList(params?: any) {
    return request.get<any>({
      url: '/user/accounts/export',
      params,
      responseType: 'blob'
    })
  }

  // 搜索用户账户
  static searchUserAccounts(params: any) {
    return request.get<any>({
      url: '/user/accounts/search',
      params
    })
  }

  // 检查账户是否可用
  static checkAccountAvailability(accountId: number) {
    return request.get<any>({
      url: `/user/accounts/${accountId}/availability`
    })
  }

  // 获取账户支持的资产列表
  static getAccountSupportedAssets(accountId: number) {
    return request.get<any>({
      url: `/user/accounts/${accountId}/assets`
    })
  }

  // 更新账户数据
  static updateAccountData(id: number, data: any) {
    return request.put<any>({
      url: `/user/accounts/${id}/data`,
      data
    })
  }

  // 同步账户状态
  static syncAccountStatus(id: number) {
    return request.put<any>({
      url: `/user/accounts/${id}/sync`
    })
  }

  // 获取账户风险评级
  static getAccountRiskRating(id: number) {
    return request.get<any>({
      url: `/user/accounts/${id}/risk`
    })
  }

  // 设置账户风险等级
  static setAccountRiskLevel(id: number, level: number) {
    return request.put<any>({
      url: `/user/accounts/${id}/risk`,
      data: { level }
    })
  }

  // 获取账户审计日志
  static getAccountAuditLogs(accountId: number, params?: any) {
    return request.get<any>({
      url: `/user/accounts/${accountId}/audit`,
      params
    })
  }

  // 复制账户配置
  static copyAccountConfig(fromAccountId: number, toAccountId: number) {
    return request.post<any>({
      url: '/user/accounts/copy-config',
      data: { from_account_id: fromAccountId, to_account_id: toAccountId }
    })
  }

  // 获取账户模板
  static getAccountTemplates(type?: number) {
    return request.get<any>({
      url: '/user/accounts/templates',
      params: type ? { type } : {}
    })
  }

  // 根据模板创建账户
  static createAccountFromTemplate(templateId: number, data: any) {
    return request.post<any>({
      url: `/user/accounts/template/${templateId}`,
      data
    })
  }

  // 清理无效账户
  static cleanInvalidAccounts() {
    return request.del<any>({
      url: '/user/accounts/clean/invalid'
    })
  }

  // 修复账户数据
  static repairAccountData(accountId: number) {
    return request.put<any>({
      url: `/user/accounts/${accountId}/repair`
    })
  }
}
