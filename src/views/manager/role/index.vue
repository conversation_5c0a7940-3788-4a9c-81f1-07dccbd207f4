<script setup lang="ts">
  import { ref } from 'vue'
  import ArtCrudTable from '@/components/core/tables/ArtCrudTable.vue'
  import ArtButtonTable from '@/components/core/forms/art-button-table/index.vue'
  import { roleTableConfig, getStatusText, getStatusClass } from './role'
  import ManagerRoleService from '@/api/managerRole'

  const tableRef = ref()

  const handleRefresh = () => {
    tableRef.value?.refresh?.()
  }

  const openGrantDialog = (row: any) => {
    console.log('打开授权对话框', row)
    // 这里可以触发实际的授权对话框组件
  }
</script>

<template>
  <ArtCrudTable ref="tableRef" :config="roleTableConfig">
    <template #status="{ row }">
      <span :class="['status-tag', getStatusClass(row.status)]">
        {{ getStatusText(row.status) }}
      </span>
    </template>

    <template #isSystem="{ row }">
      <span :class="['system-tag', row.isSystem ? 'is-system' : 'not-system']">
        {{ row.isSystem ? '是' : '否' }}
      </span>
    </template>

    <template #actions="{ row }">
      <div class="flex gap-2">
        <ArtButtonTable type="edit" />
        <ArtButtonTable type="delete" />
        <el-dropdown trigger="click">
          <ArtButtonTable type="more" />
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="() => ManagerRoleService.setStatus(row.id, 1)">
                <el-icon><Open /></el-icon>
                启用
              </el-dropdown-item>
              <el-dropdown-item @click="() => ManagerRoleService.setStatus(row.id, 0)">
                <el-icon><Close /></el-icon>
                禁用
              </el-dropdown-item>
              <el-dropdown-item divided @click="() => openGrantDialog(row)">
                <el-icon><Key /></el-icon>
                授权
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </template>

    <template #toolbar-right>
      <ArtButtonTable type="more" text="刷新" @click="handleRefresh" />
    </template>
  </ArtCrudTable>
</template>

<style lang="scss" scoped>
  .status-tag {
    display: inline-block;
    padding: 2px 8px;
    font-size: 12px;
    border-radius: 10px;

    &.status-enabled {
      color: #52c41a;
      background-color: #f6ffed;
    }

    &.status-disabled {
      color: #8c8c8c;
      background-color: #f5f5f5;
    }
  }

  .system-tag {
    display: inline-block;
    padding: 2px 6px;
    font-size: 12px;
    border-radius: 3px;

    &.is-system {
      color: #fa541c;
      background-color: #fff2e8;
    }

    &.not-system {
      color: #52c41a;
      background-color: #f6ffed;
    }
  }

  .flex {
    display: flex;
  }

  .gap-2 {
    gap: 8px;
  }
</style>
