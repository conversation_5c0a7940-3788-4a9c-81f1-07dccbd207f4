<script setup lang="ts">
  import { ref } from 'vue'
  import { ElTag } from 'element-plus'
  import ArtCrudTable from '@/components/core/tables/ArtCrudTable.vue'
  import ArtButtonTable from '@/components/core/forms/art-button-table/index.vue'
  import {
    loginLogTableConfig,
    getStatusTagType,
    getStatusText,
    getTypeTagType,
    getTypeText
  } from './loginLog'

  defineOptions({ name: 'LoginLog' })

  const crudTableRef = ref()
</script>

<template>
  <ArtCrudTable ref="crudTableRef" :config="loginLogTableConfig">
    <!-- 登录状态列 -->
    <template #status="{ row }">
      <ElTag :type="getStatusTagType(row.status)" size="small">
        {{ getStatusText(row.status) }}
      </ElTag>
    </template>

    <!-- 访问类型列 -->
    <template #type="{ row }">
      <ElTag :type="getTypeTagType(row.type)" size="small">
        {{ getTypeText(row.type) }}
      </ElTag>
    </template>

    <!-- 是否爬虫列 -->
    <template #isBot="{ row }">
      <ElTag :type="row.isBot ? 'danger' : 'success'" size="small">
        {{ row.isBot ? '是' : '否' }}
      </ElTag>
    </template>

    <!-- 操作列 -->
    <template #actions="{ row }">
      <div class="flex gap-2">
        <ArtButtonTable type="delete" @click="crudTableRef?.deleteRow(row)" />
      </div>
    </template>
  </ArtCrudTable>
</template>

<style lang="scss" scoped>
  .flex {
    display: flex;
  }

  .gap-2 {
    gap: 8px;
  }
</style>
