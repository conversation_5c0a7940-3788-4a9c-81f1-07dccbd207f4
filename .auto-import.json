{"globals": {"Component": true, "ComponentPublicInstance": true, "ComputedRef": true, "DirectiveBinding": true, "EffectScope": true, "ExtractDefaultPropTypes": true, "ExtractPropTypes": true, "ExtractPublicPropTypes": true, "InjectionKey": true, "MaybeRef": true, "MaybeRefOrGetter": true, "PropType": true, "Ref": true, "VNode": true, "WritableComputedRef": true, "acceptHMRUpdate": true, "asyncComputed": true, "autoResetRef": true, "computed": true, "computedAsync": true, "computedEager": true, "computedInject": true, "computedWithControl": true, "controlledComputed": true, "controlledRef": true, "createApp": true, "createEventHook": true, "createGlobalState": true, "createInjectionState": true, "createPinia": true, "createReactiveFn": true, "createReusableTemplate": true, "createSharedComposable": true, "createTemplatePromise": true, "createUnrefFn": true, "customRef": true, "debouncedRef": true, "debouncedWatch": true, "defineAsyncComponent": true, "defineComponent": true, "defineStore": true, "eagerComputed": true, "effectScope": true, "extendRef": true, "getActivePinia": true, "getCurrentInstance": true, "getCurrentScope": true, "h": true, "ignorableWatch": true, "inject": true, "injectLocal": true, "isDefined": true, "isProxy": true, "isReactive": true, "isReadonly": true, "isRef": true, "makeDestructurable": true, "mapActions": true, "mapGetters": true, "mapState": true, "mapStores": true, "mapWritableState": true, "markRaw": true, "nextTick": true, "onActivated": true, "onBeforeMount": true, "onBeforeRouteLeave": true, "onBeforeRouteUpdate": true, "onBeforeUnmount": true, "onBeforeUpdate": true, "onClickOutside": true, "onDeactivated": true, "onErrorCaptured": true, "onKeyStroke": true, "onLongPress": true, "onMounted": true, "onRenderTracked": true, "onRenderTriggered": true, "onScopeDispose": true, "onServerPrefetch": true, "onStartTyping": true, "onUnmounted": true, "onUpdated": true, "onWatcherCleanup": true, "pausableWatch": true, "provide": true, "provideLocal": true, "reactify": true, "reactifyObject": true, "reactive": true, "reactiveComputed": true, "reactiveOmit": true, "reactivePick": true, "readonly": true, "ref": true, "refAutoReset": true, "refDebounced": true, "refDefault": true, "refThrottled": true, "refWithControl": true, "resolveComponent": true, "resolveRef": true, "resolveUnref": true, "setActivePinia": true, "setMapStoreSuffix": true, "shallowReactive": true, "shallowReadonly": true, "shallowRef": true, "storeToRefs": true, "syncRef": true, "syncRefs": true, "templateRef": true, "throttledRef": true, "throttledWatch": true, "toRaw": true, "toReactive": true, "toRef": true, "toRefs": true, "toValue": true, "triggerRef": true, "tryOnBeforeMount": true, "tryOnBeforeUnmount": true, "tryOnMounted": true, "tryOnScopeDispose": true, "tryOnUnmounted": true, "unref": true, "unrefElement": true, "until": true, "useActiveElement": true, "useAnimate": true, "useArrayDifference": true, "useArrayEvery": true, "useArrayFilter": true, "useArrayFind": true, "useArrayFindIndex": true, "useArrayFindLast": true, "useArrayIncludes": true, "useArrayJoin": true, "useArrayMap": true, "useArrayReduce": true, "useArraySome": true, "useArrayUnique": true, "useAsyncQueue": true, "useAsyncState": true, "useAttrs": true, "useBase64": true, "useBattery": true, "useBluetooth": true, "useBreakpoints": true, "useBroadcastChannel": true, "useBrowserLocation": true, "useCached": true, "useClipboard": true, "useClipboardItems": true, "useCloned": true, "useColorMode": true, "useConfirmDialog": true, "useCounter": true, "useCssModule": true, "useCssVar": true, "useCssVars": true, "useCurrentElement": true, "useCycleList": true, "useDark": true, "useDateFormat": true, "useDebounce": true, "useDebounceFn": true, "useDebouncedRefHistory": true, "useDeviceMotion": true, "useDeviceOrientation": true, "useDevicePixelRatio": true, "useDevicesList": true, "useDisplayMedia": true, "useDocumentVisibility": true, "useDraggable": true, "useDropZone": true, "useElementBounding": true, "useElementByPoint": true, "useElementHover": true, "useElementSize": true, "useElementVisibility": true, "useEventBus": true, "useEventListener": true, "useEventSource": true, "useEyeDropper": true, "useFavicon": true, "useFetch": true, "useFileDialog": true, "useFileSystemAccess": true, "useFocus": true, "useFocusWithin": true, "useFps": true, "useFullscreen": true, "useGamepad": true, "useGeolocation": true, "useId": true, "useIdle": true, "useImage": true, "useInfiniteScroll": true, "useIntersectionObserver": true, "useInterval": true, "useIntervalFn": true, "useKeyModifier": true, "useLastChanged": true, "useLink": true, "useLocalStorage": true, "useMagicKeys": true, "useManualRefHistory": true, "useMediaControls": true, "useMediaQuery": true, "useMemoize": true, "useMemory": true, "useModel": true, "useMounted": true, "useMouse": true, "useMouseInElement": true, "useMousePressed": true, "useMutationObserver": true, "useNavigatorLanguage": true, "useNetwork": true, "useNow": true, "useObjectUrl": true, "useOffsetPagination": true, "useOnline": true, "usePageLeave": true, "useParallax": true, "useParentElement": true, "usePerformanceObserver": true, "usePermission": true, "usePointer": true, "usePointerLock": true, "usePointerSwipe": true, "usePreferredColorScheme": true, "usePreferredContrast": true, "usePreferredDark": true, "usePreferredLanguages": true, "usePreferredReducedMotion": true, "usePrevious": true, "useRafFn": true, "useRefHistory": true, "useResizeObserver": true, "useRoute": true, "useRouter": true, "useScreenOrientation": true, "useScreenSafeArea": true, "useScriptTag": true, "useScroll": true, "useScrollLock": true, "useSessionStorage": true, "useShare": true, "useSlots": true, "useSorted": true, "useSpeechRecognition": true, "useSpeechSynthesis": true, "useStepper": true, "useStorage": true, "useStorageAsync": true, "useStyleTag": true, "useSupported": true, "useSwipe": true, "useTemplateRef": true, "useTemplateRefsList": true, "useTextDirection": true, "useTextSelection": true, "useTextareaAutosize": true, "useThrottle": true, "useThrottleFn": true, "useThrottledRefHistory": true, "useTimeAgo": true, "useTimeout": true, "useTimeoutFn": true, "useTimeoutPoll": true, "useTimestamp": true, "useTitle": true, "useToNumber": true, "useToString": true, "useToggle": true, "useTransition": true, "useUrlSearchParams": true, "useUserMedia": true, "useVModel": true, "useVModels": true, "useVibrate": true, "useVirtualList": true, "useWakeLock": true, "useWebNotification": true, "useWebSocket": true, "useWebWorker": true, "useWebWorkerFn": true, "useWindowFocus": true, "useWindowScroll": true, "useWindowSize": true, "watch": true, "watchArray": true, "watchAtMost": true, "watchDebounced": true, "watchDeep": true, "watchEffect": true, "watchIgnorable": true, "watchImmediate": true, "watchOnce": true, "watchPausable": true, "watchPostEffect": true, "watchSyncEffect": true, "watchThrottled": true, "watchTriggerable": true, "watchWithFilter": true, "whenever": true, "ElMessage": true, "ElTag": true, "ElTimeSelect": true, "ElRadio": true}}