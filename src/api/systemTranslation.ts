import request from '@/utils/http'

export class TranslationService {
  static baseUrl = '/system/translation'

  /**
   * 获取翻译列表
   */
  static async getTranslationList(params: any): Promise<any> {
    return request.get<any>({
      url: `${this.baseUrl}`,
      params
    })
  }

  /**
   * 创建翻译
   */
  static async createTranslation(data: any): Promise<any> {
    return request.post<any>({
      url: `${this.baseUrl}`,
      data
    })
  }

  /**
   * 更新翻译
   */
  static async updateTranslation(id: number, data: any): Promise<any> {
    return request.put<any>({
      url: `${this.baseUrl}/${id}`,
      data
    })
  }

  /**
   * 删除翻译
   */
  static async deleteTranslation(id: number): Promise<any> {
    return request.del<any>({
      url: `${this.baseUrl}/${id}`
    })
  }

  /**
   * 批量删除翻译
   */
  static async batchDeleteTranslations(ids: number[]): Promise<any> {
    return request.del<any>({
      url: `${this.baseUrl}/batch`,
      data: { ids }
    })
  }

  /**
   * 获取翻译详情
   */
  static async getTranslationDetail(id: number): Promise<any> {
    return request.get<any>({
      url: `${this.baseUrl}/${id}`
    })
  }
}
