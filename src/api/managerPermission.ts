import request from '@/utils/http'

const baseUrl = '/admin/manager/permission'

export class ManagerPermissionService {
  // 列表查询（分页）
  static list(params: any) {
    return request.get<any>({ url: `${baseUrl}/list`, params })
  }

  // 创建
  static create(data: any) {
    return request.post<any>({ url: `${baseUrl}/create`, data })
  }

  // 更新
  static update(id: string | number, data: any) {
    return request.post<any>({ url: `${baseUrl}/update/${id}`, data })
  }

  // 删除
  static delete(id: string | number) {
    return request.post<any>({ url: `${baseUrl}/delete/${id}` })
  }

  // 详情
  static detail(id: string | number) {
    return request.get<any>({ url: `${baseUrl}/detail/${id}` })
  }

  // 状态切换
  static setStatus(id: string | number, status: number) {
    return request.post<any>({ url: `${baseUrl}/status/${id}`, data: { status } })
  }

  // 批量删除
  static batchDelete(ids: Array<string | number>) {
    return request.post<any>({ url: `${baseUrl}/batch/delete`, data: { ids } })
  }

  // 批量启用
  static batchEnable(ids: Array<string | number>) {
    return request.post<any>({ url: `${baseUrl}/batch/enable`, data: { ids } })
  }

  // 批量禁用
  static batchDisable(ids: Array<string | number>) {
    return request.post<any>({ url: `${baseUrl}/batch/disable`, data: { ids } })
  }

  // 导出
  static export(params: any) {
    return request.get<any>({ url: `${baseUrl}/export`, params, responseType: 'blob' })
  }
}

export default ManagerPermissionService
