<script setup lang="ts">
  import { ref, h, computed, watch } from 'vue'
  import {
    ElDialog,
    ElForm,
    ElFormItem,
    ElInput,
    ElInputNumber,
    ElSelect,
    ElOption,
    ElButton,
    ElRow,
    ElCol
  } from 'element-plus'

  // 接口定义
  interface FormItem {
    label: string
    prop: string
    type: string
    disabled: boolean
    required: boolean
    config: Record<string, any>
    options?: Array<{ label: string; value: any; disabled?: boolean }>
    span: number
  }

  interface Action {
    label: string
    color: string
    size: string
    class: string
    disabled: boolean
    type: string
    method: string
    url: string
    show: string
  }

  interface ToolbarItem {
    id: string
    title: string
    width: string
    fullscreen: string
    modal: boolean
    type: string
    form: {
      id: string
      label: string
      class: string
      fields: FormItem[]
    }
    action: Action[]
  }

  interface Props {
    visible: boolean
    toolbarItem: ToolbarItem | null
    loading?: boolean
  }

  interface Emits {
    (e: 'update:visible', value: boolean): void
    (e: 'submit', data: Record<string, any>): void
    (e: 'cancel'): void
  }

  const props = withDefaults(defineProps<Props>(), {
    loading: false
  })

  const emits = defineEmits<Emits>()

  // 表单数据
  const formData = ref<Record<string, any>>({})

  // 监听对话框显示状态
  const dialogVisible = computed({
    get: () => props.visible,
    set: (value) => emits('update:visible', value)
  })

  // 监听工具栏项变化，初始化表单数据
  watch(
    () => props.toolbarItem,
    (newItem) => {
      if (newItem && newItem.form?.fields) {
        const initialData: Record<string, any> = {}

        newItem.form.fields.forEach((field: FormItem) => {
          switch (field.type?.toLowerCase()) {
            case 'number':
              initialData[field.prop] = 0
              break
            case 'select':
              initialData[field.prop] = field.options?.[0]?.value || ''
              break
            case 'checkbox':
              initialData[field.prop] = []
              break
            default:
              initialData[field.prop] = ''
          }
        })

        formData.value = initialData
      }
    },
    { immediate: true }
  )

  // 渲染表单字段
  const renderFormField = (field: FormItem) => {
    const commonProps = {
      modelValue: formData.value[field.prop],
      'onUpdate:modelValue': (value: any) => {
        formData.value[field.prop] = value
      },
      placeholder: field.config?.placeholder || `请输入${field.label}`,
      disabled: field.disabled || props.loading,
      clearable: field.config?.clearable !== false
    }

    switch (field.type?.toLowerCase()) {
      case 'number':
        return h(ElInputNumber, {
          ...commonProps,
          min: field.config?.min,
          max: field.config?.max,
          step: field.config?.step || 1,
          style: { width: '100%' }
        })
      case 'select': {
        const options = field.options || []
        return h(ElSelect, commonProps, {
          default: () =>
            options.map((option) =>
              h(ElOption, {
                key: option.value,
                label: option.label,
                value: option.value,
                disabled: option.disabled
              })
            )
        })
      }
      case 'textarea':
        return h(ElInput, {
          ...commonProps,
          type: 'textarea',
          rows: field.config?.rows || 4
        })
      default:
        return h(ElInput, commonProps)
    }
  }

  // 提交表单
  const handleSubmit = () => {
    emits('submit', formData.value)
  }

  // 取消表单
  const handleCancel = () => {
    emits('cancel')
  }

  // 关闭对话框
  const handleClose = () => {
    emits('update:visible', false)
  }
</script>

<template>
  <ElDialog
    v-model="dialogVisible"
    :title="toolbarItem?.title || '表单'"
    :width="toolbarItem?.width || '600px'"
    :fullscreen="!!toolbarItem?.fullscreen"
    :modal="toolbarItem?.modal !== false"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <ElForm
      v-if="toolbarItem?.form?.fields && toolbarItem.form.fields.length > 0"
      :model="formData"
      label-width="120px"
    >
      <ElRow :gutter="20">
        <ElCol v-for="field in toolbarItem.form.fields" :key="field.prop" :span="field.span || 12">
          <ElFormItem :label="field.label" :prop="field.prop" :required="field.required">
            <component :is="renderFormField(field)" />
          </ElFormItem>
        </ElCol>
      </ElRow>
    </ElForm>

    <div v-else style="padding: 40px; color: #999; text-align: center">暂无表单配置</div>

    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="handleCancel">取消</ElButton>
        <ElButton type="primary" :loading="loading" @click="handleSubmit">
          {{ toolbarItem?.action?.find((a) => a.type === 'submit')?.label || '确定' }}
        </ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<style scoped lang="scss">
  .dialog-footer {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
  }
</style>
