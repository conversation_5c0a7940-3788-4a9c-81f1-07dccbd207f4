<script setup lang="ts">
  import { ref } from 'vue'
  import ArtCrudTable from '@/components/core/tables/ArtCrudTable.vue'
  import ArtButtonTable from '@/components/core/forms/art-button-table/index.vue'
  import { depositTableConfig, formatAmount, getStatusClass, getStatusText } from './deposit'
  import { UserDepositService } from '@/api/walletDeposit'

  defineOptions({ name: 'WalletDeposit' })

  const crudRef = ref()

  const complete = (row: any) => {
    UserDepositService.complete(row.id, '手动完成').then(() => crudRef.value?.refreshData())
  }
  const cancel = (row: any) => {
    UserDepositService.cancel(row.id, '手动取消').then(() => crudRef.value?.refreshData())
  }
  const fail = (row: any) => {
    UserDepositService.fail(row.id, '手动设为失败').then(() => crudRef.value?.refreshData())
  }
</script>

<template>
  <ArtCrudTable ref="crudRef" :config="depositTableConfig">
    <!-- 用户 -->
    <template #user="{ row }">
      <div class="user-info">
        <div class="user-id">ID: {{ row.user?.id || row.user_id }}</div>
        <div class="user-name">{{ row.user?.username || '未知用户' }}</div>
        <div v-if="row.user?.nickname" class="user-nickname">({{ row.user?.nickname }})</div>
      </div>
    </template>

    <!-- 资产 -->
    <template #asset="{ row }">
      <div class="asset-info">
        <div class="asset-symbol">{{ row.asset?.symbol || 'UNKNOWN' }}</div>
        <div class="asset-name">{{ row.asset?.name || '未知资产' }}</div>
      </div>
    </template>

    <!-- 金额 -->
    <template #amount="{ row }">
      <div class="amount">{{ formatAmount(row.amount, row.asset?.precision || 4) }}</div>
    </template>

    <!-- 到账金额 -->
    <template #arrivalAmount="{ row }">
      <div class="amount arr">{{
        formatAmount(row.arrival_amount, row.asset?.precision || 4)
      }}</div>
    </template>

    <!-- 固定手续费 -->
    <template #fixedFee="{ row }">
      <div class="fee">{{ formatAmount(row.fixed_fee, row.asset?.precision || 4) }}</div>
    </template>

    <!-- 状态 -->
    <template #status="{ row }">
      <span :class="['status-tag', getStatusClass(row.status)]">{{
        getStatusText(row.status)
      }}</span>
    </template>

    <!-- 凭证 -->
    <template #proof="{ row }">
      <span class="proof" v-if="row.proof">{{ row.proof }}</span>
      <span class="proof empty" v-else>无</span>
    </template>

    <!-- 操作 -->
    <template #actions="{ row }">
      <div class="actions">
        <ArtButtonTable type="edit" @click="crudRef?.showDialog('edit', row)" />
        <ArtButtonTable type="delete" @click="crudRef?.deleteItem(row.id)" />
        <el-dropdown trigger="click">
          <ArtButtonTable type="more" />
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item v-if="row.status === 1" @click="complete(row)">
                <el-icon><CircleCheck /></el-icon> 完成
              </el-dropdown-item>
              <el-dropdown-item v-if="row.status === 1" @click="cancel(row)">
                <el-icon><Close /></el-icon> 取消
              </el-dropdown-item>
              <el-dropdown-item v-if="row.status === 1" @click="fail(row)">
                <el-icon><CloseBold /></el-icon> 设为失败
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </template>
  </ArtCrudTable>
</template>

<style scoped lang="scss">
  .user-info {
    .user-id {
      font-size: 12px;
      color: #999;
    }

    .user-name {
      font-weight: 500;
      color: #333;
    }

    .user-nickname {
      font-size: 12px;
      color: #666;
    }
  }

  .asset-info {
    .asset-symbol {
      font-weight: 600;
    }

    .asset-name {
      font-size: 12px;
      color: #666;
    }
  }

  .amount {
    font-family: Monaco, Menlo, monospace;
    font-weight: 600;
    text-align: right;
  }

  .fee {
    font-family: Monaco, Menlo, monospace;
    color: #faad14;
    text-align: right;
  }

  .status-tag {
    padding: 2px 6px;
    font-size: 12px;
    font-weight: 500;
    border-radius: 4px;
  }

  .status-pending {
    color: #faad14;
    background: #fffbe6;
    border: 1px solid #ffe58f;
  }

  .status-success {
    color: #52c41a;
    background: #f6ffed;
    border: 1px solid #b7eb8f;
  }

  .status-cancel {
    color: #8c8c8c;
    background: #f5f5f5;
    border: 1px solid #d9d9d9;
  }

  .status-failed {
    color: #ff4d4f;
    background: #fff2f0;
    border: 1px solid #ffb3b3;
  }

  .proof.empty {
    color: #bbb;
  }

  .actions {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }
</style>
