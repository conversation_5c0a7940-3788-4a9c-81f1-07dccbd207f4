<script setup lang="ts">
  import { ref } from 'vue'
  import ArtCrudTable from '@/components/core/tables/ArtCrudTable.vue'
  import ArtButtonTable from '@/components/core/forms/art-button-table/index.vue'
  import { settingTableConfig } from './setting'

  defineOptions({ name: 'SystemSetting' })

  const crudRef = ref()

  const UpSettingData = (row?: Record<string, unknown>): void => {
    console.log(row)
  }
</script>

<template>
  <ArtCrudTable ref="crudRef" :config="settingTableConfig">
    <!-- 操作 -->
    <template #actions="{ row }">
      <div class="actions">
        <ArtButtonTable type="edit" @click="crudRef?.showDialog('edit', row)" />
        <ArtButtonTable type="edit" icon="&#xe816;" @click="UpSettingData(row)" />
      </div>
    </template>
  </ArtCrudTable>
</template>

<style scoped lang="scss">
  .actions {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }
</style>
