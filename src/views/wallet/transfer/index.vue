<script setup lang="ts">
  import { ref } from 'vue'
  import ArtCrudTable from '@/components/core/tables/ArtCrudTable.vue'
  import ArtButtonTable from '@/components/core/forms/art-button-table/index.vue'
  import {
    transferTableConfig,
    getTransferTypeText,
    getTransferStatusText,
    getStatusTagClass,
    formatAmount,
    calculateTotalFee,
    calculateActualAmount,
    calculateFeeRate
  } from './transfer'
  import { UserTransferService } from '@/api/walletTransfer'

  defineOptions({ name: 'UserTransfer' })

  const crudTableRef = ref()
  const componentKey = ref(Date.now())

  // 发送用户信息渲染
  const renderSendUser = (row: any) => {
    if (row.send_user) {
      return {
        id: row.send_user.id,
        username: row.send_user.username,
        nickname: row.send_user.nickname,
        avatar: row.send_user.avatar
      }
    }
    return { id: row.send_user_id, username: '未知用户', nickname: '', avatar: '' }
  }

  // 接收用户信息渲染
  const renderReceiveUser = (row: any) => {
    if (row.receive_user) {
      return {
        id: row.receive_user.id,
        username: row.receive_user.username,
        nickname: row.receive_user.nickname,
        avatar: row.receive_user.avatar
      }
    }
    return { id: row.receive_user_id, username: '未知用户', nickname: '', avatar: '' }
  }

  // 资产信息渲染
  const renderAsset = (row: any) => {
    if (row.asset) {
      return {
        id: row.asset.id,
        name: row.asset.name,
        symbol: row.asset.symbol,
        icon: row.asset.icon,
        precision: row.asset.precision || 4
      }
    }
    return { id: row.asset_id, name: '未知资产', symbol: 'UNKNOWN', icon: '', precision: 4 }
  }

  // 转账金额渲染
  const renderAmount = (row: any) => {
    const amount = formatAmount(row.amount, renderAsset(row).precision)
    return {
      amount,
      symbol: renderAsset(row).symbol
    }
  }

  // 手续费渲染
  const renderFee = (row: any) => {
    const fixedFee = formatAmount(row.fixed_fee, renderAsset(row).precision)
    const rateFee = formatAmount(row.rate_fee, 3)
    const totalFee = calculateTotalFee(row.amount, row.fixed_fee, row.rate_fee)
    const feeRate = calculateFeeRate(row.amount, totalFee)
    return {
      fixedFee,
      rateFee,
      totalFee: formatAmount(totalFee, renderAsset(row).precision),
      feeRate,
      symbol: renderAsset(row).symbol
    }
  }

  // 实际到账金额渲染
  const renderActualAmount = (row: any) => {
    const actualAmount = formatAmount(row.actual_amount, renderAsset(row).precision)
    const calculatedAmount = calculateActualAmount(row.amount, row.fixed_fee, row.rate_fee)
    return {
      actualAmount,
      calculatedAmount: formatAmount(calculatedAmount, renderAsset(row).precision),
      symbol: renderAsset(row).symbol
    }
  }

  // 转账状态渲染
  const renderStatus = (row: any) => {
    const statusText = getTransferStatusText(row.status)
    const statusClass = getStatusTagClass(row.status)
    return { text: statusText, class: statusClass }
  }

  // 查看转账详情
  const viewTransferDetail = (row: any) => {
    console.log('查看转账详情:', row)
    // 这里可以实现跳转到转账详情页面的逻辑
  }

  // 完成转账
  const completeTransfer = (row: any) => {
    console.log('完成转账:', row.id)
    UserTransferService.completeTransfer(row.id, '手动完成').then(() => {
      crudTableRef.value?.refreshData()
    })
  }

  // 取消转账
  const cancelTransfer = (row: any) => {
    console.log('取消转账:', row.id)
    UserTransferService.cancelTransfer(row.id, '手动取消').then(() => {
      crudTableRef.value?.refreshData()
    })
  }

  // 设为失败
  const failTransfer = (row: any) => {
    console.log('设为失败:', row.id)
    UserTransferService.failTransfer(row.id, '手动设为失败').then(() => {
      crudTableRef.value?.refreshData()
    })
  }

  // 审核转账
  const reviewTransfer = (row: any, approved: boolean) => {
    console.log('审核转账:', row.id, approved)
    const reason = approved ? '审核通过' : '审核拒绝'
    UserTransferService.reviewTransfer(row.id, approved, reason).then(() => {
      crudTableRef.value?.refreshData()
    })
  }

  // 验证转账信息
  const validateTransfer = (row: any) => {
    console.log('验证转账信息:', row.id)
    UserTransferService.validateTransfer(row).then((result) => {
      console.log('验证结果:', result)
    })
  }

  // 检查余额
  const checkBalance = (row: any) => {
    console.log('检查用户余额:', row.send_user_id, row.asset_id, row.amount)
    UserTransferService.checkUserBalance(row.send_user_id, row.asset_id, row.amount).then(
      (result) => {
        console.log('余额检查结果:', result)
      }
    )
  }

  // 查看用户转账历史
  const viewUserTransferHistory = (userId: number, type: 'send' | 'receive') => {
    console.log('查看用户转账历史:', userId, type)
    // 这里可以实现跳转到用户转账历史页面的逻辑
  }

  // 查看资产转账记录
  const viewAssetTransferHistory = (assetId: number) => {
    console.log('查看资产转账记录:', assetId)
    // 这里可以实现跳转到资产转账记录页面的逻辑
  }

  // 处理下拉菜单命令
  const handleDropdownCommand = (command: string, row: any) => {
    switch (command) {
      case 'complete':
        completeTransfer(row)
        break
      case 'cancel':
        cancelTransfer(row)
        break
      case 'fail':
        failTransfer(row)
        break
      case 'approve':
        reviewTransfer(row, true)
        break
      case 'reject':
        reviewTransfer(row, false)
        break
      case 'validate':
        validateTransfer(row)
        break
      case 'check-balance':
        checkBalance(row)
        break
      case 'send-user-history':
        viewUserTransferHistory(row.send_user_id, 'send')
        break
      case 'receive-user-history':
        viewUserTransferHistory(row.receive_user_id, 'receive')
        break
      case 'asset-history':
        viewAssetTransferHistory(row.asset_id)
        break
      default:
        console.log('未知命令:', command)
    }
  }
</script>

<template>
  <ArtCrudTable ref="crudTableRef" :key="componentKey" :config="transferTableConfig">
    <!-- 发送用户列 -->
    <template #sendUser="{ row }">
      <div class="user-info">
        <div class="user-avatar" v-if="renderSendUser(row).avatar">
          <img
            :src="renderSendUser(row).avatar"
            :alt="renderSendUser(row).username"
            class="avatar-img"
          />
        </div>
        <div class="user-details">
          <div class="user-id">ID: {{ renderSendUser(row).id }}</div>
          <div class="user-name">{{ renderSendUser(row).username }}</div>
          <div v-if="renderSendUser(row).nickname" class="user-nickname">
            ({{ renderSendUser(row).nickname }})
          </div>
        </div>
        <div class="user-badge send-badge">发送方</div>
      </div>
    </template>

    <!-- 接收用户列 -->
    <template #receiveUser="{ row }">
      <div class="user-info">
        <div class="user-avatar" v-if="renderReceiveUser(row).avatar">
          <img
            :src="renderReceiveUser(row).avatar"
            :alt="renderReceiveUser(row).username"
            class="avatar-img"
          />
        </div>
        <div class="user-details">
          <div class="user-id">ID: {{ renderReceiveUser(row).id }}</div>
          <div class="user-name">{{ renderReceiveUser(row).username }}</div>
          <div v-if="renderReceiveUser(row).nickname" class="user-nickname">
            ({{ renderReceiveUser(row).nickname }})
          </div>
        </div>
        <div class="user-badge receive-badge">接收方</div>
      </div>
    </template>

    <!-- 转账资产列 -->
    <template #asset="{ row }">
      <div class="asset-info">
        <div class="asset-icon" v-if="renderAsset(row).icon">
          <img :src="renderAsset(row).icon" :alt="renderAsset(row).symbol" class="asset-icon-img" />
        </div>
        <div class="asset-details">
          <div class="asset-symbol">{{ renderAsset(row).symbol }}</div>
          <div class="asset-name">{{ renderAsset(row).name }}</div>
        </div>
        <div class="asset-badge">转账资产</div>
      </div>
    </template>

    <!-- 转账金额列 -->
    <template #amount="{ row }">
      <div class="amount-container transfer-amount">
        <span class="amount-value">{{ renderAmount(row).amount }}</span>
        <span class="amount-symbol">{{ renderAmount(row).symbol }}</span>
        <div class="amount-direction">
          <el-icon class="transfer-icon"><Wallet /></el-icon>
          <span class="direction-text">转账</span>
        </div>
      </div>
    </template>

    <!-- 手续费列 -->
    <template #fee="{ row }">
      <div class="fee-container">
        <div class="fee-item">
          <span class="fee-label">固定:</span>
          <span class="fee-value">{{ renderFee(row).fixedFee }}</span>
        </div>
        <div class="fee-item">
          <span class="fee-label">比例:</span>
          <span class="fee-value">{{ renderFee(row).rateFee }}%</span>
        </div>
        <div class="fee-total">
          <span class="fee-label">总计:</span>
          <span class="fee-total-value">{{ renderFee(row).totalFee }}</span>
          <span class="fee-symbol">{{ renderFee(row).symbol }}</span>
        </div>
        <div class="fee-rate"> 费率: {{ renderFee(row).feeRate }}% </div>
      </div>
    </template>

    <!-- 实际到账金额列 -->
    <template #actualAmount="{ row }">
      <div class="actual-amount-container">
        <span class="actual-amount-value">{{ renderActualAmount(row).actualAmount }}</span>
        <span class="actual-amount-symbol">{{ renderActualAmount(row).symbol }}</span>
        <div class="actual-amount-direction">
          <el-icon class="receive-icon"><Checked /></el-icon>
          <span class="direction-text">到账</span>
        </div>
        <div
          v-if="renderActualAmount(row).actualAmount !== renderActualAmount(row).calculatedAmount"
          class="calculated-hint"
        >
          预计: {{ renderActualAmount(row).calculatedAmount }}
        </div>
      </div>
    </template>

    <!-- 转账类型列 -->
    <template #transferType="{ row }">
      <span class="type-tag">{{ getTransferTypeText(row.type) }}</span>
    </template>

    <!-- 状态列 -->
    <template #status="{ row }">
      <span :class="['status-tag', renderStatus(row).class]">
        {{ renderStatus(row).text }}
      </span>
    </template>

    <!-- 操作列 -->
    <template #actions="{ row }">
      <div class="flex gap-2">
        <ArtButtonTable type="edit" @click="crudTableRef?.showDialog('edit', row)" />
        <ArtButtonTable type="delete" @click="crudTableRef?.deleteItem(row.id)" />
        <ArtButtonTable type="view" @click="viewTransferDetail(row)" />

        <!-- 状态操作按钮 -->
        <ArtButtonTable v-if="row.status === 1" type="add" @click="completeTransfer(row)" />
        <ArtButtonTable v-if="row.status === 1" type="delete" @click="cancelTransfer(row)" />

        <!-- 更多操作下拉菜单 -->
        <el-dropdown trigger="click" @command="(command) => handleDropdownCommand(command, row)">
          <ArtButtonTable type="more" />
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item v-if="row.status === 1" command="complete">
                <el-icon><CircleCheck /></el-icon>
                完成转账
              </el-dropdown-item>
              <el-dropdown-item v-if="row.status === 1" command="cancel">
                <el-icon><Close /></el-icon>
                取消转账
              </el-dropdown-item>
              <el-dropdown-item v-if="row.status === 1" command="fail" divided>
                <el-icon><CloseBold /></el-icon>
                设为失败
              </el-dropdown-item>
              <el-dropdown-item v-if="row.status === 1" command="approve" divided>
                <el-icon><Select /></el-icon>
                审核通过
              </el-dropdown-item>
              <el-dropdown-item v-if="row.status === 1" command="reject">
                <el-icon><Remove /></el-icon>
                审核拒绝
              </el-dropdown-item>
              <el-dropdown-item command="validate" divided>
                <el-icon><Document /></el-icon>
                验证转账
              </el-dropdown-item>
              <el-dropdown-item command="check-balance">
                <el-icon><Money /></el-icon>
                检查余额
              </el-dropdown-item>
              <el-dropdown-item command="send-user-history" divided>
                <el-icon><User /></el-icon>
                发送方历史
              </el-dropdown-item>
              <el-dropdown-item command="receive-user-history">
                <el-icon><User /></el-icon>
                接收方历史
              </el-dropdown-item>
              <el-dropdown-item command="asset-history" divided>
                <el-icon><Coin /></el-icon>
                资产记录
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </template>
  </ArtCrudTable>
</template>

<style lang="scss" scoped>
  .flex {
    display: flex;
  }

  .gap-2 {
    gap: 8px;
  }

  // 用户信息样式
  .user-info {
    position: relative;
    display: flex;
    gap: 8px;
    align-items: center;

    .user-avatar {
      .avatar-img {
        width: 32px;
        height: 32px;
        object-fit: cover;
        border: 1px solid #e0e0e0;
        border-radius: 50%;
      }
    }

    .user-details {
      .user-id {
        margin-bottom: 2px;
        font-size: 12px;
        color: #999;
      }

      .user-name {
        font-weight: 500;
        color: #333;
      }

      .user-nickname {
        margin-top: 2px;
        font-size: 12px;
        color: #666;
      }
    }

    .user-badge {
      position: absolute;
      top: -4px;
      right: -4px;
      padding: 2px 6px;
      font-size: 10px;
      font-weight: 500;
      border-radius: 10px;

      &.send-badge {
        color: #ff4d4f;
        background-color: #fff2f0;
        border: 1px solid #ffb3b3;
      }

      &.receive-badge {
        color: #52c41a;
        background-color: #f6ffed;
        border: 1px solid #b7eb8f;
      }
    }
  }

  // 资产信息样式
  .asset-info {
    position: relative;
    display: flex;
    gap: 8px;
    align-items: center;

    .asset-icon {
      .asset-icon-img {
        width: 24px;
        height: 24px;
        object-fit: cover;
        border-radius: 50%;
      }
    }

    .asset-details {
      .asset-symbol {
        font-size: 14px;
        font-weight: 600;
        color: #333;
      }

      .asset-name {
        margin-top: 2px;
        font-size: 12px;
        color: #666;
      }
    }

    .asset-badge {
      position: absolute;
      top: -4px;
      right: -4px;
      padding: 2px 6px;
      font-size: 10px;
      font-weight: 500;
      color: #722ed1;
      background-color: #f9f0ff;
      border: 1px solid #d3adf7;
      border-radius: 10px;
    }
  }

  // 金额容器样式
  .amount-container {
    display: flex;
    flex-direction: column;
    align-items: flex-end;

    .amount-value {
      font-family: Monaco, Menlo, monospace;
      font-size: 14px;
      font-weight: 600;
    }

    .amount-symbol {
      margin-top: 2px;
      font-size: 11px;
      color: #8c8c8c;
    }

    .amount-direction {
      display: flex;
      gap: 4px;
      align-items: center;
      margin-top: 4px;

      .direction-text {
        font-size: 10px;
        font-weight: 500;
      }
    }

    &.transfer-amount {
      .amount-value {
        color: #1890ff;
      }

      .amount-direction {
        color: #1890ff;

        .transfer-icon {
          font-size: 12px;
        }
      }
    }
  }

  // 实际到账金额样式
  .actual-amount-container {
    display: flex;
    flex-direction: column;
    align-items: flex-end;

    .actual-amount-value {
      font-family: Monaco, Menlo, monospace;
      font-size: 14px;
      font-weight: 600;
      color: #52c41a;
    }

    .actual-amount-symbol {
      margin-top: 2px;
      font-size: 11px;
      color: #8c8c8c;
    }

    .actual-amount-direction {
      display: flex;
      gap: 4px;
      align-items: center;
      margin-top: 4px;
      color: #52c41a;

      .receive-icon {
        font-size: 12px;
      }

      .direction-text {
        font-size: 10px;
        font-weight: 500;
      }
    }

    .calculated-hint {
      padding: 1px 4px;
      margin-top: 2px;
      font-size: 10px;
      color: #faad14;
      background-color: #fffbe6;
      border-radius: 2px;
    }
  }

  // 手续费容器样式
  .fee-container {
    display: flex;
    flex-direction: column;
    gap: 2px;

    .fee-item {
      display: flex;
      justify-content: space-between;
      font-size: 12px;

      .fee-label {
        min-width: 35px;
        color: #666;
      }

      .fee-value {
        font-family: Monaco, Menlo, monospace;
        font-weight: 500;
        color: #faad14;
      }
    }

    .fee-total {
      display: flex;
      gap: 4px;
      align-items: center;
      justify-content: space-between;
      padding-top: 4px;
      margin-top: 4px;
      font-size: 12px;
      border-top: 1px solid #f0f0f0;

      .fee-label {
        color: #666;
      }

      .fee-total-value {
        font-family: Monaco, Menlo, monospace;
        font-weight: 600;
        color: #faad14;
      }

      .fee-symbol {
        font-size: 10px;
        color: #8c8c8c;
      }
    }

    .fee-rate {
      padding: 2px 4px;
      margin-top: 2px;
      font-size: 10px;
      color: #666;
      text-align: center;
      background-color: #fafafa;
      border-radius: 2px;
    }
  }

  // 类型标签样式
  .type-tag {
    padding: 4px 8px;
    font-size: 12px;
    font-weight: 500;
    color: #722ed1;
    background-color: #f9f0ff;
    border: 1px solid #d3adf7;
    border-radius: 4px;
  }

  // 状态标签样式
  .status-tag {
    padding: 4px 8px;
    font-size: 12px;
    font-weight: 500;
    border-radius: 4px;

    &.status-pending {
      color: #faad14;
      background-color: #fffbe6;
      border: 1px solid #ffe58f;
    }

    &.status-completed {
      color: #52c41a;
      background-color: #f6ffed;
      border: 1px solid #b7eb8f;
    }

    &.status-canceled {
      color: #8c8c8c;
      background-color: #f5f5f5;
      border: 1px solid #d9d9d9;
    }

    &.status-failed {
      color: #ff4d4f;
      background-color: #fff2f0;
      border: 1px solid #ffb3b3;
    }

    &.status-unknown {
      color: #8c8c8c;
      background-color: #f5f5f5;
      border: 1px solid #d9d9d9;
    }
  }

  // 悬停效果
  .amount-container:hover,
  .actual-amount-container:hover,
  .fee-container:hover {
    background-color: #fafafa;
    border-radius: 4px;
    transition: all 0.3s ease;
  }

  // 特殊高亮
  .user-info:hover .user-badge,
  .asset-info:hover .asset-badge {
    transition: all 0.2s ease;
    transform: scale(1.1);
  }

  // 操作按钮区域

  /* 已有 .flex 定义，此处不再重复定义，若需换行可在需要处单独添加样式类 */

  // 响应式设计
  @media (width <= 768px) {
    .user-info,
    .asset-info {
      flex-direction: column;
      gap: 4px;
      align-items: flex-start;
    }

    .amount-container,
    .actual-amount-container {
      align-items: flex-start;
    }

    .user-badge,
    .asset-badge {
      position: static;
      margin-top: 4px;
    }
  }
</style>
