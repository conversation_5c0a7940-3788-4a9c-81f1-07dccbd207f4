// 登录日志管理配置
import { LoginLogService } from '@/api/loginLog'

export const loginLogTableConfig = {
  title: '登录日志',
  addButtonText: '新增日志', // 虽然不显示，但要有值
  deleteButtonText: '批量删除',
  deleteConfirmText: '确定要删除该登录日志吗？',
  batchDeleteConfirmText: '确定要删除选中的 {count} 条登录日志吗？',

  initialSearchState: {
    username: '',
    ip: '',
    status: '',
    type: '',
    os: '',
    device: '',
    startTime: '',
    endTime: ''
  },

  searchFormItems: [
    {
      label: '用户名',
      prop: 'username',
      type: 'input',
      config: {
        clearable: true,
        placeholder: '请输入用户名'
      }
    },
    {
      label: '登录IP',
      prop: 'ip',
      type: 'input',
      config: {
        clearable: true,
        placeholder: '请输入登录IP'
      }
    },
    {
      label: '登录状态',
      prop: 'status',
      type: 'select',
      config: {
        clearable: true,
        placeholder: '请选择登录状态'
      },
      options: [
        { label: '成功', value: '1' },
        { label: '失败', value: '0' }
      ]
    },
    {
      label: '访问类型',
      prop: 'type',
      type: 'select',
      config: {
        clearable: true,
        placeholder: '请选择访问类型'
      },
      options: [
        { label: '前台', value: '1' },
        { label: '后台', value: '2' }
      ]
    },
    {
      label: '操作系统',
      prop: 'os',
      type: 'input',
      config: {
        clearable: true,
        placeholder: '请输入操作系统'
      }
    },
    {
      label: '设备类型',
      prop: 'device',
      type: 'select',
      config: {
        clearable: true,
        placeholder: '请选择设备类型'
      },
      options: [
        { label: '桌面端', value: 'Desktop' },
        { label: '移动端', value: 'Mobile' },
        { label: '平板', value: 'Tablet' }
      ]
    },
    {
      prop: 'startTime',
      label: '开始时间',
      type: 'date',
      config: {
        type: 'datetime',
        placeholder: '请选择开始时间'
      }
    },
    {
      prop: 'endTime',
      label: '结束时间',
      type: 'date',
      config: {
        type: 'datetime',
        placeholder: '请选择结束时间'
      }
    }
  ],

  tableColumns: [
    { type: 'selection' as const, width: 55 },
    { label: 'ID', prop: 'id', width: 80, sortable: true },
    { label: '用户名', prop: 'username', minWidth: 100 },
    { label: '登录IP', prop: 'ip', minWidth: 120 },
    { label: '登录状态', prop: 'status', width: 100, slot: 'status' },
    { label: '访问类型', prop: 'type', width: 100, slot: 'type' },
    { label: '操作系统', prop: 'os', minWidth: 100, showOverflowTooltip: true },
    { label: '设备类型', prop: 'device', width: 100 },
    { label: '浏览器', prop: 'browser', minWidth: 120, showOverflowTooltip: true },
    { label: '登录地点', prop: 'loginLocation', minWidth: 150, showOverflowTooltip: true },
    { label: '登录信息', prop: 'message', minWidth: 150, showOverflowTooltip: true },
    { label: '是否爬虫', prop: 'isBot', width: 100, slot: 'isBot' },
    { label: '登录时间', prop: 'created_at', minWidth: 160, sortable: true, slot: 'created_at' },
    { label: '操作', width: 120, fixed: 'right' as const, slot: 'actions' }
  ],

  apiService: {
    getList: (params: any) => {
      // 参数格式化处理
      const formattedParams = {
        page: params.page || 1,
        pageSize: params.pageSize || 20,
        username: params.username || undefined,
        ip: params.ip || undefined,
        status: params.status ? Number(params.status) : undefined,
        type: params.type ? Number(params.type) : undefined,
        os: params.os || undefined,
        device: params.device || undefined,
        startTime: params.startTime || undefined,
        endTime: params.endTime || undefined
      }
      return LoginLogService.getLoginLogList(formattedParams)
    },
    delete: (id: any) => LoginLogService.deleteLoginLog(id),
    batchDelete: (ids: any[]) => LoginLogService.batchDeleteLoginLogs(ids),
    update: undefined, // 登录日志不允许更新
    create: undefined // 登录日志不允许创建
  },

  // 自定义分页字段映射，适配后端接口
  paginationKey: {
    current: 'page',
    size: 'pageSize',
    orderBy: 'orderBy',
    order: 'order'
  },

  // 按钮控制配置
  hideAddButton: true, // 隐藏新增按钮
  hideDeleteButton: false, // 显示删除按钮
  disableEdit: true, // 禁用编辑功能
  allowDelete: true, // 允许删除操作
  allowBatchDelete: true, // 允许批量删除

  // 空数据时的高度
  emptyHeight: '500px'
}

// 获取状态标签类型
export const getStatusTagType = (status: number) => {
  return status === 1 ? 'success' : 'danger'
}

// 获取状态文本
export const getStatusText = (status: number) => {
  return status === 1 ? '成功' : '失败'
}

// 获取访问类型标签类型
export const getTypeTagType = (type: number) => {
  return type === 1 ? 'primary' : 'warning'
}

// 获取访问类型文本
export const getTypeText = (type: number) => {
  return type === 1 ? '前台' : '后台'
}
