import request from '@/utils/http'

const baseUrl = '/admin/manager/role'

export class ManagerRoleService {
  // 列表
  static list(params: any) {
    return request.get<any>({ url: `${baseUrl}/list`, params })
  }

  // 详情
  static detail(id: string | number) {
    return request.get<any>({ url: `${baseUrl}/detail/${id}` })
  }

  // 创建
  static create(data: any) {
    return request.post<any>({ url: `${baseUrl}/create`, data })
  }

  // 更新
  static update(id: string | number, data: any) {
    return request.post<any>({ url: `${baseUrl}/update/${id}`, data })
  }

  // 删除
  static delete(id: string | number) {
    return request.post<any>({ url: `${baseUrl}/delete/${id}` })
  }

  // 批量删除
  static batchDelete(ids: Array<string | number>) {
    return request.post<any>({ url: `${baseUrl}/batch/delete`, data: { ids } })
  }

  // 启用/禁用
  static setStatus(id: string | number, status: number) {
    return request.post<any>({ url: `${baseUrl}/status/${id}`, data: { status } })
  }

  static batchEnable(ids: Array<string | number>) {
    return request.post<any>({ url: `${baseUrl}/batch/enable`, data: { ids } })
  }

  static batchDisable(ids: Array<string | number>) {
    return request.post<any>({ url: `${baseUrl}/batch/disable`, data: { ids } })
  }

  // 排序
  static setSort(id: string | number, sort: number) {
    return request.post<any>({ url: `${baseUrl}/sort/${id}`, data: { sort } })
  }

  // 导出
  static export(params: any) {
    return request.get<any>({ url: `${baseUrl}/export`, params, responseType: 'blob' })
  }

  // 角色授权（绑定权限ID集合）
  static grantPermissions(id: string | number, permissionIds: Array<string | number>) {
    return request.post<any>({
      url: `${baseUrl}/grant/${id}`,
      data: { permissions: permissionIds }
    })
  }
}

export default ManagerRoleService
