import { SystemSettingService } from '@/api/systemSetting'
import type {
  CrudTableConfig,
  ExportConfig,
  PermissionConfig,
  SecurityConfig,
  PerformanceConfig,
  ResponsiveConfig
} from '@/components/core/tables/ArtCrudTable.vue'
import { filterEmptyParams } from '@/utils'

// 包装API服务
const createApiService = () => ({
  getList: (params: any) => SystemSettingService.getList(filterEmptyParams(params)),
  update: async (id: string | number, data: any) => {
    await SystemSettingService.update(Number(id), data)
    return { code: 0 }
  }
})

// 选项
export const groupOptions = [
  { label: '站点配置', value: 1 },
  { label: '模版配置', value: 2 }
]

export const statusOptions = [
  { label: '启用', value: 1 },
  { label: '禁用', value: 0 }
]

export const typeOptions = [
  { label: '字符串', value: 'string' },
  { label: '数字', value: 'number' },
  { label: '布尔', value: 'bool' },
  { label: 'JSON', value: 'json' }
]

// 导出配置
const exportConfig: ExportConfig = {
  enabled: true,
  filename: '系统配置数据导出',
  columns: [
    'id',
    'group_id',
    'name',
    'field',
    'value',
    'type',
    'title',
    'description',
    'sort',
    'status',
    'created_at'
  ],
  formats: ['excel', 'csv'],
  maxRows: 10000
}

// 权限/响应/安全/性能
const permissionConfig: PermissionConfig = {
  create: true,
  update: true,
  delete: true,
  export: true
}

const responsiveConfig: ResponsiveConfig = {
  mobile: {
    hideColumns: ['title', 'description', 'created_at'],
    cardView: false,
    breakpoint: 768
  }
}

const securityConfig: SecurityConfig = {
  enableXssProtection: true,
  allowedTags: ['p', 'br', 'strong', 'em', 'code', 'pre'],
  allowedAttributes: { div: ['class'], span: ['class'] }
}

const performanceConfig: PerformanceConfig = {
  enableCache: true,
  cacheTime: 3 * 60 * 1000,
  debounceTime: 300,
  maxCacheSize: 100,
  lazyLoad: true,
  virtualScroll: { enabled: false, itemHeight: 60, buffer: 10 }
}

export const settingTableConfig: CrudTableConfig = {
  title: '系统配置',
  addButtonText: '新增配置',
  deleteButtonText: '删除配置',
  deleteConfirmText: '确定要删除该配置吗？',
  batchDeleteConfirmText: '确定要删除选中的 {count} 条配置吗？',

  initialSearchState: {},
  searchFormItems: [
    { key: 'group_id', label: '分组', type: 'select', options: groupOptions },
    { key: 'name', label: '名称', type: 'input' },
    { key: 'field', label: '键名', type: 'input' },
    { key: 'type', label: '值类型', type: 'select', options: typeOptions },
    { key: 'status', label: '状态', type: 'select', options: statusOptions },
    { key: 'created_at', label: '创建时间', type: 'date' }
  ],

  tableColumns: [
    { prop: 'id', label: 'ID', width: 80, fixed: 'left', sortable: true, align: 'center' },
    { prop: 'group_id', label: '分组', width: 100, align: 'center', options: groupOptions },
    { prop: 'name', label: '名称', minWidth: 140 },
    { prop: 'field', label: '键名', minWidth: 140 },
    { prop: 'type', label: '类型', width: 120, align: 'center' },
    { prop: 'sort', label: '排序', width: 90, align: 'center', sortable: true },
    {
      prop: 'status',
      label: '状态',
      width: 100,
      slot: 'status',
      align: 'center',
      columnType: 'toggle'
    },
    { prop: 'created_at', label: '创建时间', width: 160, align: 'center', columnType: 'date' },
    { prop: 'description', label: '描述', minWidth: 180 },
    { prop: 'actions', label: '操作', width: 180, fixed: 'right', slot: 'actions', align: 'center' }
  ],

  apiService: createApiService(),
  paginationKey: { current: 'page', size: 'pageSize' },

  dialog: {
    formItems: [
      {
        prop: 'group_id',
        label: '分组',
        type: 'select',
        options: groupOptions,
        rules: [{ required: true, message: '必选', trigger: 'change' }],
        span: 12
      },
      {
        prop: 'name',
        label: '名称',
        type: 'input',
        rules: [{ required: true, message: '必填', trigger: 'blur' }],
        span: 12
      },
      { prop: 'sort', label: '排序', type: 'number', span: 12, config: { precision: 0 } },
      {
        prop: 'status',
        label: '状态',
        type: 'select',
        options: statusOptions,
        rules: [{ required: true, message: '必选', trigger: 'change' }],
        span: 12
      },
      { prop: 'description', label: '描述', type: 'textarea', span: 24 }
    ]
  },

  export: exportConfig,
  permissions: permissionConfig,
  responsive: responsiveConfig,
  security: securityConfig,
  performance: performanceConfig,
  hasStatusSwitch: true,
  statusField: 'status',
  statusActiveValue: 1,
  statusInactiveValue: 0,
  allowBatchDelete: true
}
