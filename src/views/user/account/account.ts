import { UserService } from '@/api/usersApi'
import type {
  CrudTableConfig,
  BatchAction,
  ExportConfig,
  PermissionConfig,
  SecurityConfig,
  PerformanceConfig,
  ResponsiveConfig
} from '@/components/core/tables/ArtCrudTable.vue'

// 过滤空值参数的工具函数
const filterEmptyParams = (params: Record<string, any>) => {
  const filtered: Record<string, any> = {}
  Object.keys(params).forEach((key) => {
    const value = params[key]
    // 只保留非空值：非空字符串、非null、非undefined、数字0、布尔值false
    if (value !== '' && value !== null && value !== undefined) {
      filtered[key] = value
    }
  })
  return filtered
}

// 包装API服务，添加参数过滤
const createAccountApiService = () => ({
  getList: (params: any) => {
    // 过滤空值参数，解决URL中出现空值的问题
    const filteredParams = filterEmptyParams(params)
    console.log('UserAccount API - 过滤后的请求参数:', filteredParams)
    return UserService.getUserAccountList(filteredParams)
  },
  delete: (id: any) => UserService.deleteUserAccount(id),
  batchDelete: (ids: any[]) => UserService.batchDeleteUserAccounts(ids),
  update: (id: any, data: any) => UserService.updateUserAccount(id, data),
  create: (data: any) => UserService.createUserAccount(data)
})

// 批量操作配置
const batchActions: BatchAction[] = [
  {
    label: '批量启用',
    key: 'batchEnable',
    type: 'success',
    icon: 'Check',
    confirm: '确认启用选中的 {count} 个账户吗？',
    handler: async (selectedRows) => {
      console.log('批量启用账户:', selectedRows)
      await Promise.all(
        selectedRows.map((row) => UserService.updateUserAccount(row.id as number, { status: 1 }))
      )
    }
  },
  {
    label: '批量禁用',
    key: 'batchDisable',
    type: 'warning',
    icon: 'Close',
    confirm: '确认禁用选中的 {count} 个账户吗？',
    handler: async (selectedRows) => {
      console.log('批量禁用账户:', selectedRows)
      await Promise.all(
        selectedRows.map((row) => UserService.updateUserAccount(row.id as number, { status: 0 }))
      )
    }
  },
  {
    label: '批量审核',
    key: 'batchVerify',
    type: 'primary',
    icon: 'DocumentChecked',
    confirm: '确认审核选中的 {count} 个账户吗？',
    handler: async (selectedRows) => {
      console.log('批量审核账户:', selectedRows)
      await Promise.all(selectedRows.map((row) => UserService.verifyUserAccount(row.id as number)))
    }
  },
  {
    label: '批量导出',
    key: 'batchExport',
    type: 'info',
    icon: 'Download',
    handler: async (selectedRows) => {
      console.log('批量导出账户:', selectedRows)
      const exportData = selectedRows.map((row) => ({
        id: row.id as number,
        manager_id: row.manager_id as number,
        user_id: row.user_id as number,
        asset_id: row.asset_id as number,
        payment_id: row.payment_id as number,
        name: row.name as string,
        type: getTypeText(row.type as number),
        status: getStatusText(row.status as number),
        created_at: row.created_at as string
      }))
      console.log('导出数据:', exportData)
    }
  }
]

// 账户类型文本转换
export const getTypeText = (type: number) => {
  const typeMap: Record<number, string> = {
    1: '银行卡',
    2: '加密货币',
    3: '三方支付'
  }
  return typeMap[type] || '银行卡'
}

// 账户状态文本转换
export const getStatusText = (status: number) => {
  const statusMap: Record<number, string> = {
    1: '启用',
    0: '禁用'
  }
  return statusMap[status] || '启用'
}

// 解析账户数据
export const parseAccountData = (data: string, type: number) => {
  if (!data) return null
  try {
    const parsed = typeof data === 'string' ? JSON.parse(data) : data
    switch (type) {
      case 1: // 银行卡
        return {
          bank_name: parsed.bank_name || '',
          account_number: parsed.account_number || '',
          account_holder: parsed.account_holder || '',
          branch: parsed.branch || '',
          swift_code: parsed.swift_code || ''
        }
      case 2: // 加密货币
        return {
          address: parsed.address || '',
          network: parsed.network || '',
          memo: parsed.memo || '',
          min_amount: parsed.min_amount || 0
        }
      case 3: // 三方支付
        return {
          platform: parsed.platform || '',
          account_id: parsed.account_id || '',
          account_name: parsed.account_name || '',
          email: parsed.email || ''
        }
      default:
        return parsed
    }
  } catch {
    return { error: '数据格式错误' }
  }
}

// 导出配置
const exportConfig: ExportConfig = {
  enabled: true,
  filename: '用户账户数据导出',
  columns: [
    'id',
    'manager_id',
    'user_id',
    'asset_id',
    'payment_id',
    'name',
    'type',
    'status',
    'created_at'
  ],
  formats: ['excel', 'csv'],
  maxRows: 1000
}

// 权限配置
const permissionConfig: PermissionConfig = {
  create: () => {
    return true
  },
  update: () => {
    return true
  },
  delete: () => {
    // 启用状态的账户需要谨慎删除
    return true
  },
  export: () => true,
  batchDelete: () => true
}

// 响应式配置
const responsiveConfig: ResponsiveConfig = {
  mobile: {
    hideColumns: ['manager_id', 'asset_id', 'payment_id', 'data', 'created_at'],
    cardView: false,
    breakpoint: 768
  }
}

// 安全配置
const securityConfig: SecurityConfig = {
  enableXssProtection: true,
  allowedTags: ['p', 'br', 'strong', 'em'],
  allowedAttributes: {
    div: ['class'],
    span: ['class']
  }
}

// 性能配置
const performanceConfig: PerformanceConfig = {
  enableCache: true,
  cacheTime: 3 * 60 * 1000, // 3分钟
  debounceTime: 500,
  maxCacheSize: 50,
  lazyLoad: true,
  virtualScroll: {
    enabled: true,
    itemHeight: 60,
    buffer: 10
  }
}

// 用户账户管理配置
export const accountTableConfig: CrudTableConfig = {
  title: '用户账户',
  addButtonText: '新增账户',
  deleteButtonText: '删除账户',
  deleteConfirmText: '确定要删除该账户吗？',
  batchDeleteConfirmText: '确定要删除选中的 {count} 条账户吗？',
  emptyHeight: '500px',

  initialSearchState: {
    manager_id: '',
    user_id: '',
    asset_id: '',
    payment_id: '',
    name: '',
    type: '',
    status: '',
    startTime: '',
    endTime: ''
  },

  searchFormItems: [
    {
      key: 'manager_id',
      label: '管理员ID',
      type: 'input',
      placeholder: '请输入管理员ID'
    },
    {
      key: 'user_id',
      label: '用户ID',
      type: 'input',
      placeholder: '请输入用户ID'
    },
    {
      key: 'asset_id',
      label: '资产ID',
      type: 'input',
      placeholder: '请输入资产ID'
    },
    {
      key: 'payment_id',
      label: '支付ID',
      type: 'input',
      placeholder: '请输入支付ID'
    },
    {
      key: 'name',
      label: '账户名称',
      type: 'input',
      placeholder: '请输入账户名称'
    },
    {
      key: 'type',
      label: '账户类型',
      type: 'select',
      placeholder: '请选择账户类型',
      options: [
        { label: '银行卡', value: '1' },
        { label: '加密货币', value: '2' },
        { label: '三方支付', value: '3' }
      ]
    },
    {
      key: 'status',
      label: '状态',
      type: 'select',
      placeholder: '请选择状态',
      options: [
        { label: '启用', value: '1' },
        { label: '禁用', value: '0' }
      ]
    },
    {
      key: 'startTime',
      label: '开始时间',
      type: 'date',
      placeholder: '请选择开始时间'
    },
    {
      key: 'endTime',
      label: '结束时间',
      type: 'date',
      placeholder: '请选择结束时间'
    }
  ],

  tableColumns: [
    { type: 'selection', width: 55 },
    { label: 'ID', prop: 'id', width: 80, sortable: true },
    {
      label: '管理员',
      prop: 'manager',
      minWidth: 120,
      useSlot: true,
      slotName: 'manager'
    },
    {
      label: '用户信息',
      prop: 'user',
      minWidth: 150,
      useSlot: true,
      slotName: 'user'
    },
    {
      label: '资产信息',
      prop: 'asset',
      minWidth: 120,
      useSlot: true,
      slotName: 'asset'
    },
    {
      label: '支付方式',
      prop: 'payment',
      minWidth: 120,
      useSlot: true,
      slotName: 'payment'
    },
    {
      label: '账户名称',
      prop: 'name',
      minWidth: 150,
      showOverflowTooltip: true,
      columnType: 'copy'
    },
    {
      label: '账户类型',
      prop: 'type',
      width: 100,
      useSlot: true,
      slotName: 'type'
    },
    {
      label: '状态',
      prop: 'status',
      width: 80,
      useSlot: true,
      slotName: 'status'
    },
    {
      label: '账户数据',
      prop: 'data',
      minWidth: 250,
      useSlot: true,
      slotName: 'accountData'
    },
    {
      label: '创建时间',
      prop: 'created_at',
      minWidth: 160,
      sortable: true,
      columnType: 'date'
    },
    {
      label: '操作',
      width: 220,
      fixed: 'right',
      useSlot: true,
      slotName: 'actions'
    }
  ],

  apiService: createAccountApiService(),

  // 分页参数映射
  paginationKey: {
    current: 'page',
    size: 'pageSize'
  },

  dialog: {
    title: {
      add: '添加用户账户',
      edit: '编辑用户账户'
    },
    width: '900px',
    formItems: [
      {
        label: '管理员ID',
        prop: 'manager_id',
        type: 'input',
        required: true,
        config: {
          placeholder: '请输入管理员ID'
        },
        rules: [{ required: true, message: '请输入管理员ID', trigger: 'blur' }],
        span: 12
      },
      {
        label: '用户ID',
        prop: 'user_id',
        type: 'input',
        required: true,
        config: {
          placeholder: '请输入用户ID'
        },
        rules: [{ required: true, message: '请输入用户ID', trigger: 'blur' }],
        span: 12
      },
      {
        label: '资产ID',
        prop: 'asset_id',
        type: 'input',
        required: true,
        config: {
          placeholder: '请输入资产ID'
        },
        rules: [{ required: true, message: '请输入资产ID', trigger: 'blur' }],
        span: 12
      },
      {
        label: '支付ID',
        prop: 'payment_id',
        type: 'input',
        required: true,
        config: {
          placeholder: '请输入支付ID'
        },
        rules: [{ required: true, message: '请输入支付ID', trigger: 'blur' }],
        span: 12
      },
      {
        label: '账户名称',
        prop: 'name',
        type: 'input',
        required: true,
        config: {
          placeholder: '请输入账户名称',
          maxlength: 255
        },
        rules: [
          { required: true, message: '请输入账户名称', trigger: 'blur' },
          { max: 255, message: '账户名称长度不能超过 255 个字符', trigger: 'blur' }
        ],
        span: 12
      },
      {
        label: '账户类型',
        prop: 'type',
        type: 'select',
        required: true,
        options: [
          { label: '银行卡', value: 1 },
          { label: '加密货币', value: 2 },
          { label: '三方支付', value: 3 }
        ],
        span: 12
      },
      {
        label: '状态',
        prop: 'status',
        type: 'select',
        required: true,
        options: [
          { label: '启用', value: 1 },
          { label: '禁用', value: 0 }
        ],
        span: 12
      },
      {
        label: '账户数据',
        prop: 'data',
        type: 'textarea',
        config: {
          placeholder: '请输入账户数据（JSON格式）',
          rows: 6
        },
        span: 24
      }
    ],
    initialFormData: {
      manager_id: '',
      user_id: '',
      asset_id: '',
      payment_id: '',
      name: '',
      type: 1,
      status: 1,
      data: ''
    }
  },

  // 功能配置
  batchActions,
  export: exportConfig,
  permissions: permissionConfig,
  responsive: responsiveConfig,
  security: securityConfig,
  performance: performanceConfig,

  // 状态切换配置
  hasStatusSwitch: true,
  statusField: 'status',
  statusActiveValue: 1,
  statusInactiveValue: 0,

  // 操作权限
  allowBatchDelete: true
}
