import request from '@/utils/http'

export class SystemArticleService {
  private static baseUrl = '/system/article'

  static getList(params?: any) {
    return request.get<any>({ url: this.baseUrl, params })
  }

  static create(data: any) {
    return request.post<any>({ url: this.baseUrl, data })
  }

  static update(id: number, data: any) {
    return request.put<any>({ url: `${this.baseUrl}/${id}`, data })
  }

  static remove(id: number) {
    return request.del<any>({ url: `${this.baseUrl}/${id}` })
  }

  static batchRemove(ids: number[]) {
    return request.del<any>({ url: `${this.baseUrl}/batch`, data: { ids } })
  }
}
