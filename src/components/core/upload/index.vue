<template>
  <div class="upload-wrapper">
    <!-- 单文件上传模式 -->
    <div v-if="singleFile" class="single-file-upload">
      <!-- 已有文件显示 -->
      <div v-if="currentFileUrl" class="file-preview">
        <div v-if="isImage(currentFileUrl)" class="image-preview">
          <el-image
            :src="currentFileUrl"
            fit="cover"
            style="width: 100px; height: 100px; border-radius: 6px"
            :preview-src-list="[currentFileUrl]"
            preview-teleported
          />
          <div class="file-actions">
            <el-button type="primary" size="small" @click="triggerUpload"> 更换 </el-button>
            <el-button type="danger" size="small" @click="removeFile">删除</el-button>
          </div>
        </div>
        <div v-else class="file-info">
          <el-icon class="file-icon"><Document /></el-icon>
          <div class="file-details">
            <div class="file-name">{{ getFileName(currentFileUrl) }}</div>
            <div class="file-actions">
              <el-button type="primary" size="small" @click="triggerUpload"> 更换 </el-button>
              <el-button type="danger" size="small" @click="removeFile">删除</el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 隐藏的上传组件用于替换文件 -->
      <el-upload
        v-if="currentFileUrl"
        ref="uploadRef"
        :action="uploadUrl"
        :headers="uploadHeaders"
        :data="uploadData"
        :name="name"
        :with-credentials="withCredentials"
        :accept="accept"
        :auto-upload="autoUpload"
        :show-file-list="false"
        :before-upload="handleBeforeUpload"
        :on-success="handleSingleSuccess"
        :on-error="handleError"
        :disabled="disabled"
        style="display: none"
      />

      <!-- 空状态上传区域 -->
      <div v-else class="empty-upload">
        <el-upload
          ref="uploadRef"
          :action="uploadUrl"
          :headers="uploadHeaders"
          :data="uploadData"
          :name="name"
          :with-credentials="withCredentials"
          :accept="accept"
          :auto-upload="autoUpload"
          :show-file-list="false"
          :before-upload="handleBeforeUpload"
          :on-success="handleSingleSuccess"
          :on-error="handleError"
          :disabled="disabled"
          class="single-upload-trigger"
        >
          <div class="upload-trigger">
            <el-icon class="upload-icon"><UploadFilled /></el-icon>
            <div class="upload-text">{{ triggerText }}</div>
            <div v-if="tip" class="upload-tip">{{ tip }}</div>
          </div>
        </el-upload>
      </div>
    </div>

    <!-- 原有的多文件上传模式 -->
    <el-upload
      v-else
      ref="uploadRef"
      :action="uploadUrl"
      :headers="uploadHeaders"
      :data="uploadData"
      :name="name"
      :with-credentials="withCredentials"
      :multiple="multiple"
      :show-file-list="showFileList"
      :drag="drag"
      :accept="accept"
      :list-type="listType"
      :auto-upload="autoUpload"
      :file-list="fileList"
      :http-request="httpRequest"
      :before-upload="handleBeforeUpload"
      :on-progress="handleProgress"
      :on-success="handleSuccess"
      :on-error="handleError"
      :on-remove="handleRemove"
      :on-preview="handlePreview"
      :on-exceed="handleExceed"
      :limit="limit"
      :disabled="disabled"
      class="upload-component"
      v-bind="$attrs"
    >
      <slot name="trigger">
        <div
          class="drag-upload-area"
          :style="!drag ? 'padding-top: 15px; padding-bottom: 15px' : ''"
        >
          <el-icon class="el-icon--upload">
            <upload-filled />
          </el-icon>
          <div class="el-upload__text">
            {{ drag ? dragText : triggerText }}
          </div>
        </div>
      </slot>

      <template #tip>
        <slot name="tip">
          <div v-if="tip" class="el-upload__tip">
            {{ tip }}
          </div>
        </slot>
      </template>
    </el-upload>

    <!-- 图片预览对话框 -->
    <el-dialog v-model="dialogVisible" title="图片预览" width="50%" append-to-body>
      <img :src="dialogImageUrl" alt="预览图片" style="width: 100%; height: auto" />
    </el-dialog>
  </div>
</template>

<script setup>
  import { ref, computed, defineEmits, defineProps, watch } from 'vue'
  import { ElMessage } from 'element-plus'
  import { UploadFilled, Document } from '@element-plus/icons-vue'
  import { useUserStore } from '@/store/modules/user.js'
  const { accessToken } = useUserStore()
  // 环境变量
  const { VITE_API_URL, VITE_WITH_CREDENTIALS } = import.meta.env

  // Props 定义
  const props = defineProps({
    // v-model 支持 (单文件模式)
    modelValue: {
      type: String,
      default: ''
    },
    // 是否为单文件上传模式
    singleFile: {
      type: Boolean,
      default: false
    },
    // 上传地址
    action: {
      type: String,
      default: '/api/upload'
    },
    // 请求头
    headers: {
      type: Object,
      default: () => ({})
    },
    // 上传时附带的额外参数
    data: {
      type: Object,
      default: () => ({})
    },
    // 上传的文件字段名
    name: {
      type: String,
      default: 'file'
    },
    // 是否支持多选
    multiple: {
      type: Boolean,
      default: false
    },
    // 是否显示文件列表
    showFileList: {
      type: Boolean,
      default: true
    },
    // 是否启用拖拽上传
    drag: {
      type: Boolean,
      default: false
    },
    // 接受的文件类型
    accept: {
      type: String,
      default: ''
    },
    // 文件列表类型
    listType: {
      type: String,
      default: 'text', // text/picture/picture-card
      validator: (value) => ['text', 'picture', 'picture-card'].includes(value)
    },
    // 是否自动上传
    autoUpload: {
      type: Boolean,
      default: true
    },
    // 文件列表
    fileList: {
      type: Array,
      default: () => []
    },
    // 最大上传数量
    limit: {
      type: Number,
      default: undefined
    },
    // 文件大小限制 (MB)
    maxSize: {
      type: Number,
      default: 10
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 按钮文本
    triggerText: {
      type: String,
      default: '点击上传'
    },
    // 拖拽区域文本
    dragText: {
      type: String,
      default: '将文件拖到此处，或点击上传'
    },
    // 提示文本
    tip: {
      type: String,
      default: ''
    },
    // 自定义上传方法
    customRequest: {
      type: Function,
      default: null
    },
    // 上传前的校验
    beforeUpload: {
      type: Function,
      default: null
    }
  })

  // Emits 定义
  const emits = defineEmits([
    'update:modelValue',
    'before-upload',
    'progress',
    'success',
    'error',
    'remove',
    'preview',
    'exceed',
    'change'
  ])

  // 响应式数据
  const uploadRef = ref()
  const uploading = ref(false)
  const dialogVisible = ref(false)
  const dialogImageUrl = ref('')
  const currentFileUrl = ref(props.modelValue)

  // 监听 modelValue 变化
  watch(
    () => props.modelValue,
    (newVal) => {
      currentFileUrl.value = newVal
    },
    { immediate: true }
  )

  // 计算属性
  const uploadUrl = computed(() => {
    return props.action.startsWith('http') ? props.action : `${VITE_API_URL}${props.action}`
  })

  const uploadHeaders = computed(() => {
    return {
      Authorization: accessToken ? `Bearer ${accessToken}` : '',
      ...props.headers
    }
  })

  const uploadData = computed(() => {
    return {
      ...props.data
    }
  })

  const withCredentials = computed(() => {
    return VITE_WITH_CREDENTIALS === 'true'
  })

  // 工具方法
  const isImage = (url) => {
    return /\.(png|jpe?g|gif|svg|webp)(\?.*)?$/i.test(url)
  }

  const getFileName = (url) => {
    return url.split('/').pop() || 'unknown'
  }

  // 移除文件
  const removeFile = () => {
    currentFileUrl.value = ''
    emits('update:modelValue', '')
    emits('change', '')
  }

  // 触发文件选择
  const triggerUpload = () => {
    const input = uploadRef.value?.$el?.querySelector('input[type="file"]')
    if (input) {
      input.click()
    }
  }

  // 方法
  const handleBeforeUpload = (file) => {
    // 文件大小检查
    if (props.maxSize && file.size / 1024 / 1024 > props.maxSize) {
      ElMessage.error(`文件大小不能超过 ${props.maxSize}MB`)
      return false
    }

    // 自定义验证
    if (props.beforeUpload) {
      const result = props.beforeUpload(file)
      if (result === false) {
        return false
      }
    }

    uploading.value = true
    emits('before-upload', file)
    return true
  }

  // 单文件上传成功回调
  const handleSingleSuccess = (response, file) => {
    uploading.value = false

    if (response.code === 200 && response.data?.url) {
      const fullUrl = response.data.url.startsWith('http')
        ? response.data.url
        : `${VITE_API_URL}${response.data.url}`

      currentFileUrl.value = fullUrl
      emits('update:modelValue', fullUrl)
      emits('change', fullUrl)

      ElMessage.success(response.msg || '上传成功')
      emits('success', response, file, [])
    } else {
      ElMessage.error(response.msg || '上传失败')
      emits('error', new Error(response.msg || '上传失败'), file, [])
    }
  }

  const handleProgress = (event, file, fileList) => {
    emits('progress', event, file, fileList)
  }

  const handleSuccess = (response, file, fileList) => {
    uploading.value = false

    if (response.code === 200 && response.data?.url) {
      const fullUrl = response.data.url.startsWith('http')
        ? response.data.url
        : `${VITE_API_URL}${response.data.url}`

      // 更新文件URL以供预览
      const uploadedFile = fileList.find((f) => f.uid === file.uid)
      if (uploadedFile) {
        uploadedFile.url = fullUrl
      }
      file.url = fullUrl

      ElMessage.success(response.msg || '上传成功')
      emits('success', response, file, fileList)
    } else {
      ElMessage.error(response.msg || '上传失败')
      uploadRef.value?.handleRemove(file) // 从列表中移除失败的文件
      emits('error', new Error(response.msg || '上传失败'), file, fileList)
    }
  }

  const handleError = (error, file, fileList) => {
    uploading.value = false
    console.error('上传失败:', error)
    const message = error.message || '上传失败，请重试'
    ElMessage.error(message)
    emits('error', error, file, fileList)
  }

  const handleRemove = (file, fileList) => {
    emits('remove', file, fileList)
  }

  const handlePreview = (file) => {
    if (file.url && /\.(png|jpe?g|gif|svg)(\?.*)?$/i.test(file.url)) {
      dialogImageUrl.value = file.url
      dialogVisible.value = true
    } else {
      // 非图片文件的预览处理
      window.open(file.url)
    }
    emits('preview', file)
  }

  const handleExceed = (files, fileList) => {
    ElMessage.warning(`最多只能上传 ${props.limit} 个文件`)
    emits('exceed', files, fileList)
  }

  // 自定义上传方法
  const httpRequest = (options) => {
    if (props.customRequest) {
      return props.customRequest(options)
    }

    // 默认上传逻辑
    const formData = new FormData()
    formData.append(props.name, options.file)

    // 添加额外数据
    Object.keys(uploadData.value).forEach((key) => {
      formData.append(key, uploadData.value[key])
    })

    const xhr = new XMLHttpRequest()

    xhr.upload.addEventListener('progress', (e) => {
      if (e.lengthComputable) {
        options.onProgress({ percent: Math.round((e.loaded / e.total) * 100) })
      }
    })

    xhr.addEventListener('load', () => {
      let response
      try {
        response = JSON.parse(xhr.responseText)
      } catch (error) {
        options.onError(new Error('解析响应失败' + error))
        return
      }

      if (xhr.status === 200) {
        options.onSuccess(response, options.file)
      } else {
        options.onError(new Error(response.msg || `HTTP ${xhr.status}: ${xhr.statusText}`))
      }
    })

    xhr.addEventListener('error', () => {
      options.onError(new Error('网络错误'))
    })

    xhr.open('POST', uploadUrl.value)

    // 设置请求头
    Object.keys(uploadHeaders.value).forEach((key) => {
      if (uploadHeaders.value[key]) {
        xhr.setRequestHeader(key, uploadHeaders.value[key])
      }
    })

    if (withCredentials.value) {
      xhr.withCredentials = true
    }

    xhr.send(formData)

    return xhr
  }

  // 暴露方法
  const submit = () => {
    uploadRef.value?.submit()
  }

  const clearFiles = () => {
    if (props.singleFile) {
      removeFile()
    } else {
      uploadRef.value?.clearFiles()
    }
  }

  const abort = () => {
    uploadRef.value?.abort()
  }

  defineExpose({
    submit,
    clearFiles,
    abort,
    uploadRef
  })
</script>

<style scoped>
  .upload-wrapper {
    width: 100%;
  }

  .upload-component {
    width: 100%;
  }

  .drag-upload-area {
    padding: 40px;
    text-align: center;
    cursor: pointer;
    border: 2px dashed #d9d9d9;
    border-radius: 6px;
    transition: border-color 0.3s;
  }

  .drag-upload-area:hover {
    border-color: #409eff;
  }

  .el-icon--upload {
    margin-bottom: 16px;
    font-size: 67px;
    color: #c0c4cc;
  }

  .el-upload__text {
    font-size: 14px;
    color: #606266;
  }

  .el-upload__tip {
    margin-top: 7px;
    font-size: 12px;
    color: #606266;
  }

  /* 单文件上传样式 */
  .single-file-upload {
    width: 100%;
  }

  .file-preview {
    padding: 12px;
    background-color: #fafafa;
    border: 1px solid #dcdfe6;
    border-radius: 6px;
  }

  .image-preview {
    display: flex;
    gap: 12px;
    align-items: center;
  }

  .file-info {
    display: flex;
    gap: 12px;
    align-items: center;
  }

  .file-icon {
    font-size: 32px;
    color: #909399;
  }

  .file-details {
    flex: 1;
  }

  .file-name {
    margin-bottom: 8px;
    font-size: 14px;
    color: #303133;
    word-break: break-all;
  }

  .file-actions {
    display: flex;
    gap: 8px;
  }

  .empty-upload {
    width: 100%;
  }

  .single-upload-trigger {
    width: 100%;
  }

  .upload-trigger {
    padding: 40px 20px;
    text-align: center;
    cursor: pointer;
    background-color: #fafbfc;
    border: 2px dashed #dcdfe6;
    border-radius: 6px;
    transition: border-color 0.3s;
  }

  .upload-trigger:hover {
    background-color: #f5f7fa;
    border-color: #409eff;
  }

  .upload-icon {
    margin-bottom: 16px;
    font-size: 48px;
    color: #c0c4cc;
  }

  .upload-text {
    margin-bottom: 8px;
    font-size: 14px;
    color: #606266;
  }

  .upload-tip {
    font-size: 12px;
    color: #909399;
  }
</style>
