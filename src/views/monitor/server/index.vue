<script setup lang="ts">
  import { onMounted, onUnmounted, ref } from 'vue'
  import { MonitorService, ServerMonitorInfo } from '@/api/monitorApi'
  import { ElMessage } from 'element-plus'
  import { Refresh } from '@element-plus/icons-vue'

  defineOptions({ name: 'ServerMonitor' })

  const loading = ref(true)
  const serverInfo = ref<Partial<ServerMonitorInfo>>({})
  let timer: number | null = null

  const colors = [
    { color: '#5cb87a', percentage: 20 },
    { color: '#1989fa', percentage: 40 },
    { color: '#6f7ad3', percentage: 60 },
    { color: '#e6a23c', percentage: 80 },
    { color: '#f56c6c', percentage: 100 }
  ]

  const fetchData = async () => {
    loading.value = true
    try {
      serverInfo.value = await MonitorService.getServerInfo()
    } catch (error) {
      ElMessage.error('获取服务器监控信息失败')
      console.error(error)
    } finally {
      loading.value = false
    }
  }

  const formatBytes = (bytes: number, decimals = 2): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const dm = decimals < 0 ? 0 : decimals
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i]
  }

  onMounted(() => {
    fetchData()
    timer = window.setInterval(() => {
      fetchData()
    }, 10000) // 每10秒刷新一次
  })

  onUnmounted(() => {
    if (timer) {
      clearInterval(timer)
    }
  })
</script>

<template>
  <div v-loading="loading" class="server-monitor-page">
    <el-row :gutter="16">
      <!-- CPU Usage -->
      <el-col :span="12" :xs="24">
        <el-card shadow="never" class="monitor-card">
          <template #header>
            <div class="card-header">
              <span>CPU</span>
            </div>
          </template>
          <div v-if="serverInfo.cpuUsage" class="usage-details">
            <el-progress
              type="dashboard"
              :percentage="parseFloat(serverInfo.cpuUsage.cpuUsed.toFixed(2))"
              :color="colors"
            >
              <template #default="{ percentage }">
                <span class="percentage-value">{{ percentage }}%</span>
                <span class="percentage-label">使用率</span>
              </template>
            </el-progress>
            <el-descriptions :column="1" class="details-table">
              <el-descriptions-item label="核心数">{{
                serverInfo.cpuUsage.cpuNum
              }}</el-descriptions-item>
              <el-descriptions-item label="5分钟负载">{{
                serverInfo.cpuUsage.cpuAvg5.toFixed(2)
              }}</el-descriptions-item>
              <el-descriptions-item label="15分钟负载">{{
                serverInfo.cpuUsage.cpuAvg15.toFixed(2)
              }}</el-descriptions-item>
            </el-descriptions>
          </div>
        </el-card>
      </el-col>

      <!-- Memory Usage -->
      <el-col :span="12" :xs="24">
        <el-card shadow="never" class="monitor-card">
          <template #header>
            <div class="card-header">
              <span>内存</span>
            </div>
          </template>
          <div v-if="serverInfo.memoryUsage" class="usage-details">
            <el-progress
              type="dashboard"
              :percentage="parseFloat(serverInfo.memoryUsage.memUsage.toFixed(2))"
              :color="colors"
            >
              <template #default="{ percentage }">
                <span class="percentage-value">{{ percentage }}%</span>
                <span class="percentage-label">使用率</span>
              </template>
            </el-progress>
            <el-descriptions :column="1" class="details-table">
              <el-descriptions-item label="总内存">{{
                formatBytes(serverInfo.memoryUsage.memTotal)
              }}</el-descriptions-item>
              <el-descriptions-item label="已用内存">{{
                formatBytes(serverInfo.memoryUsage.memUsed)
              }}</el-descriptions-item>
              <el-descriptions-item label="剩余内存">{{
                formatBytes(serverInfo.memoryUsage.memFree)
              }}</el-descriptions-item>
            </el-descriptions>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- Server Info -->
    <el-card shadow="never" class="info-card">
      <template #header>
        <div class="card-header">
          <span>服务器信息</span>
          <el-button text :icon="Refresh" @click="fetchData" :loading="loading">刷新</el-button>
        </div>
      </template>
      <el-descriptions v-if="serverInfo.systemSes" :column="2" border>
        <el-descriptions-item label="服务器名称">{{
          serverInfo.systemSes.sysComputerName
        }}</el-descriptions-item>
        <el-descriptions-item label="操作系统">{{
          serverInfo.systemSes.sysOsName
        }}</el-descriptions-item>
        <el-descriptions-item label="服务器IP">{{
          serverInfo.systemSes.sysComputerIp
        }}</el-descriptions-item>
        <el-descriptions-item label="系统架构">{{
          serverInfo.systemSes.sysOsArch
        }}</el-descriptions-item>
        <el-descriptions-item label="Go 版本">{{
          `${serverInfo.systemSes.goName} ${serverInfo.systemSes.goVersion}`
        }}</el-descriptions-item>
        <el-descriptions-item label="运行时长">{{
          serverInfo.systemSes.goRunTime
        }}</el-descriptions-item>
        <el-descriptions-item label="启动时间" :span="2">{{
          serverInfo.systemSes.goStartTime
        }}</el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- Disk Status -->
    <el-card shadow="never" class="info-card">
      <template #header>
        <div class="card-header">
          <span>磁盘状态</span>
        </div>
      </template>
      <el-table :data="serverInfo.storageSes" style="width: 100%" stripe>
        <el-table-column prop="path" label="盘符路径" min-width="120" />
        <el-table-column prop="fstype" label="文件系统" width="100" />
        <el-table-column label="总大小" width="100">
          <template #default="scope">{{ formatBytes(scope.row.total) }}</template>
        </el-table-column>
        <el-table-column label="已用大小" width="100">
          <template #default="scope">{{ formatBytes(scope.row.used) }}</template>
        </el-table-column>
        <el-table-column label="可用大小" width="100">
          <template #default="scope">{{ formatBytes(scope.row.free) }}</template>
        </el-table-column>
        <el-table-column label="使用率" min-width="150">
          <template #default="scope">
            <el-progress
              :percentage="parseFloat(scope.row.usedPercent.toFixed(2))"
              :color="colors"
            />
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<style scoped lang="scss">
  .server-monitor-page {
    padding: 16px;
    background-color: #f0f2f5;
  }

  .monitor-card,
  .info-card {
    margin-bottom: 16px;
  }

  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-weight: bold;
  }

  .usage-details {
    display: flex;
    gap: 20px;
    align-items: center;

    .details-table {
      flex: 1;
    }

    .percentage-value {
      display: block;
      font-size: 28px;
    }

    .percentage-label {
      display: block;
      margin-top: 10px;
      font-size: 14px;
      color: var(--el-text-color-secondary);
    }
  }

  // 响应式调整
  @media (width <= 768px) {
    .usage-details {
      flex-direction: column;
    }

    .el-col-xs-24 {
      margin-bottom: 16px;
    }
  }
</style>
