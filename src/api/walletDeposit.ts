import request from '@/utils/http'

/**
 * 用户充值管理服务类（UserWallet - 充值）
 * 类型固定：1 充值
 * 状态：1 待处理 2 已完成 3 已取消 4 已失败
 */
export class UserDepositService {
  private static baseUrl = '/wallet/deposit'

  static readonly TYPE_DEPOSIT = 1
  static readonly STATUS_PENDING = 1
  static readonly STATUS_SUCCESS = 2
  static readonly STATUS_CANCEL = 3
  static readonly STATUS_FAILED = 4

  // ============ 基础 CRUD ============
  static getList(params?: any) {
    return request.get<any>({ url: this.baseUrl, params })
  }

  static getDetail(id: number) {
    return request.get<any>({ url: `${this.baseUrl}/${id}` })
  }

  static create(data: any) {
    return request.post<any>({ url: this.baseUrl, data })
  }

  static update(id: number, data: any) {
    return request.put<any>({ url: `${this.baseUrl}/${id}`, data })
  }

  static remove(id: number) {
    return request.del<any>({ url: `${this.baseUrl}/${id}` })
  }

  static batchRemove(ids: number[]) {
    return request.del<any>({ url: `${this.baseUrl}/batch`, data: { ids } })
  }

  // ============ 状态操作 ============
  static complete(id: number, reason?: string) {
    return request.put<any>({ url: `${this.baseUrl}/${id}/complete`, data: { reason } })
  }

  static cancel(id: number, reason?: string) {
    return request.put<any>({ url: `${this.baseUrl}/${id}/cancel`, data: { reason } })
  }

  static fail(id: number, reason?: string) {
    return request.put<any>({ url: `${this.baseUrl}/${id}/fail`, data: { reason } })
  }

  static batchUpdateStatus(ids: number[], status: number, reason?: string) {
    return request.put<any>({ url: `${this.baseUrl}/batch/status`, data: { ids, status, reason } })
  }

  // ============ 查询与工具 ============
  static getUserDeposits(userId: number, params?: any) {
    return request.get<any>({ url: `${this.baseUrl}/user/${userId}`, params })
  }

  static getAssetDeposits(assetId: number, params?: any) {
    return request.get<any>({ url: `${this.baseUrl}/asset/${assetId}`, params })
  }

  static getStats(params?: any) {
    return request.get<any>({ url: `${this.baseUrl}/stats`, params })
  }

  static exportList(params?: any) {
    return request.get<any>({ url: `${this.baseUrl}/export`, params, responseType: 'blob' })
  }

  static search(params: any) {
    return request.get<any>({ url: `${this.baseUrl}/search`, params })
  }
}
