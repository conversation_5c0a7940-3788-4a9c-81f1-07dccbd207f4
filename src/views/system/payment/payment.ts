import { SystemPaymentService } from '@/api/systemPayment'
import type {
  CrudTableConfig,
  BatchAction,
  ExportConfig,
  PermissionConfig,
  SecurityConfig,
  PerformanceConfig,
  ResponsiveConfig
} from '@/components/core/tables/ArtCrudTable.vue'

// 过滤空值参数
const filterEmptyParams = (params: Record<string, any>) => {
  const filtered: Record<string, any> = {}
  Object.keys(params).forEach((key) => {
    const value = params[key]
    if (value !== '' && value !== null && value !== undefined) {
      filtered[key] = value
    }
  })
  return filtered
}

// 包装API服务
const createApiService = () => ({
  getList: (params: any) => SystemPaymentService.getList(filterEmptyParams(params)),
  delete: async (id: string | number) => {
    await SystemPaymentService.remove(Number(id))
    return { code: 0 }
  },
  batchDelete: async (ids: Array<string | number>) => {
    await SystemPaymentService.batchRemove(ids.map((v) => Number(v)))
    return { code: 0 }
  },
  update: async (id: string | number, data: any) => {
    await SystemPaymentService.update(Number(id), data)
    return { code: 0 }
  },
  create: (data: any) => SystemPaymentService.create(data)
})

// 选项
export const paymentTypeOptions = [
  { label: '银行卡', value: 1 },
  { label: '加密货币', value: 2 },
  { label: '三方支付', value: 3 }
]

export const paymentModeOptions = [
  { label: '充值', value: 1 },
  { label: '提现', value: 2 }
]

export const statusOptions = [
  { label: '启用', value: 1 },
  { label: '禁用', value: 0 }
]

export const getStatusText = (status: number) =>
  status === 1 ? '启用' : status === 0 ? '禁用' : '未知'
export const getStatusClass = (status: number) =>
  status === 1 ? 'status-enabled' : status === 0 ? 'status-disabled' : 'status-unknown'

// 批量操作
const batchActions: BatchAction[] = [
  {
    label: '批量启用',
    key: 'batchEnable',
    type: 'success',
    icon: 'Open',
    confirm: '确认启用选中的 {count} 个支付吗？',
    handler: async (rows) => {
      await Promise.all(rows.map((r: any) => SystemPaymentService.enable(r.id)))
    }
  },
  {
    label: '批量禁用',
    key: 'batchDisable',
    type: 'warning',
    icon: 'Close',
    confirm: '确认禁用选中的 {count} 个支付吗？',
    handler: async (rows) => {
      await Promise.all(rows.map((r: any) => SystemPaymentService.disable(r.id)))
    }
  },
  {
    label: '导出数据',
    key: 'export',
    type: 'info',
    icon: 'Download',
    handler: async (rows) => {
      const params = rows.length ? { ids: rows.map((r: any) => r.id) } : {}
      await SystemPaymentService.exportList(params)
    }
  }
]

// 导出配置
const exportConfig: ExportConfig = {
  enabled: true,
  filename: '支付数据导出',
  columns: [
    'id',
    'asset_id',
    'name',
    'subtitle_field',
    'type',
    'mode',
    'min_amount',
    'max_amount',
    'start_at',
    'end_at',
    'fixed_fee',
    'rate_fee',
    'level',
    'status',
    'sort',
    'is_proof',
    'is_redirect_cs',
    'description_field',
    'created_at'
  ],
  formats: ['excel', 'csv'],
  maxRows: 10000
}

// 权限/响应/安全/性能
const permissionConfig: PermissionConfig = {
  create: true,
  update: true,
  delete: true,
  export: true
}

const responsiveConfig: ResponsiveConfig = {
  mobile: {
    hideColumns: ['subtitle_field', 'description_field', 'created_at'],
    cardView: false,
    breakpoint: 768
  }
}

const securityConfig: SecurityConfig = {
  enableXssProtection: true,
  allowedTags: ['p', 'br', 'strong', 'em'],
  allowedAttributes: { div: ['class'], span: ['class'] }
}

const performanceConfig: PerformanceConfig = {
  enableCache: true,
  cacheTime: 3 * 60 * 1000,
  debounceTime: 300,
  maxCacheSize: 100,
  lazyLoad: true,
  virtualScroll: { enabled: false, itemHeight: 60, buffer: 10 }
}

export const paymentTableConfig: CrudTableConfig = {
  title: '支付管理',
  addButtonText: '新增支付',
  deleteButtonText: '删除支付',
  deleteConfirmText: '确定要删除该支付吗？',
  batchDeleteConfirmText: '确定要删除选中的 {count} 个支付吗？',
  emptyHeight: '500px',

  initialSearchState: {},
  searchFormItems: [
    { key: 'asset_id', label: '资产ID', type: 'select', options: [] },
    { key: 'name', label: '名称', type: 'input' },
    { key: 'type', label: '类型', type: 'select', options: paymentTypeOptions },
    { key: 'mode', label: '模式', type: 'select', options: paymentModeOptions },
    { key: 'status', label: '状态', type: 'select', options: statusOptions },
    { key: 'created_at', label: '创建时间', type: 'datetimerange' }
  ],

  tableColumns: [
    { prop: 'id', label: 'ID', width: 80, fixed: 'left', sortable: true, align: 'center' },
    { prop: 'icon', label: '图标', width: 80, slot: 'icon', align: 'center' },
    { prop: 'asset', label: '资产', minWidth: 140, slot: 'asset' },
    { prop: 'name', label: '名称', minWidth: 140 },
    { prop: 'subtitle_field', label: '副标题字段', minWidth: 160 },
    { prop: 'type', label: '类型', width: 120, slot: 'type', align: 'center' },
    { prop: 'mode', label: '模式', width: 120, slot: 'mode', align: 'center' },
    { prop: 'min_amount', label: '最小金额', width: 120, align: 'right' },
    { prop: 'max_amount', label: '最大金额', width: 120, align: 'right' },
    { prop: 'start_at', label: '开始时间', width: 120, align: 'center' },
    { prop: 'end_at', label: '结束时间', width: 120, align: 'center' },
    { prop: 'fixed_fee', label: '固定手续费', width: 120, align: 'right' },
    { prop: 'rate_fee', label: '手续费(%)', width: 120, align: 'right' },
    { prop: 'level', label: '等级', width: 100, align: 'center' },
    { prop: 'status', label: '状态', width: 100, slot: 'status', align: 'center' },
    { prop: 'sort', label: '排序', width: 90, align: 'center', sortable: true },
    { prop: 'is_proof', label: '凭证', width: 100, slot: 'proof', align: 'center' },
    { prop: 'is_redirect_cs', label: '跳转客服', width: 120, slot: 'redirectCS', align: 'center' },
    { prop: 'description_field', label: '描述字段', minWidth: 160 },
    { prop: 'created_at', label: '创建时间', width: 160, align: 'center' },
    { prop: 'actions', label: '操作', width: 220, fixed: 'right', slot: 'actions', align: 'center' }
  ],

  apiService: createApiService(),
  paginationKey: { current: 'page', size: 'page_size' },

  dialog: {
    formItems: [
      { prop: 'icon', label: '图标', type: 'upload', span: 24 },
      {
        prop: 'asset_id',
        label: '资产ID',
        type: 'select',
        options: [],
        rules: [{ required: true, message: '必选', trigger: 'change' }],
        span: 12
      },
      {
        prop: 'name',
        label: '名称',
        type: 'input',
        rules: [{ required: true, message: '必填', trigger: 'blur' }],
        span: 12
      },
      { prop: 'subtitle_field', label: '副标题字段', type: 'input', span: 12 },
      {
        prop: 'type',
        label: '类型',
        type: 'select',
        options: paymentTypeOptions,
        rules: [{ required: true, message: '必选', trigger: 'change' }],
        span: 12
      },
      {
        prop: 'mode',
        label: '模式',
        type: 'select',
        options: paymentModeOptions,
        rules: [{ required: true, message: '必选', trigger: 'change' }],
        span: 12
      },
      { prop: 'min_amount', label: '最小金额', type: 'number', span: 12, config: { precision: 4 } },
      { prop: 'max_amount', label: '最大金额', type: 'number', span: 12, config: { precision: 4 } },
      { prop: 'start_at', label: '开始时间(HH:mm:ss)', type: 'input', span: 12 },
      { prop: 'end_at', label: '结束时间(HH:mm:ss)', type: 'input', span: 12 },
      {
        prop: 'fixed_fee',
        label: '固定手续费',
        type: 'number',
        span: 12,
        config: { precision: 4 }
      },
      { prop: 'rate_fee', label: '手续费(%)', type: 'number', span: 12, config: { precision: 3 } },
      { prop: 'level', label: '等级', type: 'number', span: 12, config: { precision: 0 } },
      {
        prop: 'status',
        label: '状态',
        type: 'select',
        options: statusOptions,
        rules: [{ required: true, message: '必选', trigger: 'change' }],
        span: 12
      },
      { prop: 'sort', label: '排序', type: 'number', span: 12, config: { precision: 0 } },
      {
        prop: 'is_proof',
        label: '需要凭证',
        type: 'select',
        options: [
          { label: '是', value: true },
          { label: '否', value: false }
        ],
        span: 12
      },
      {
        prop: 'is_redirect_cs',
        label: '跳转客服',
        type: 'select',
        options: [
          { label: '是', value: true },
          { label: '否', value: false }
        ],
        span: 12
      },
      { prop: 'description_field', label: '描述字段', type: 'input', span: 24 },
      { prop: 'data', label: '数据(JSON)', type: 'textarea', span: 24, config: { rows: 3 } }
    ]
  },

  batchActions,
  export: exportConfig,
  permissions: permissionConfig,
  responsive: responsiveConfig,
  security: securityConfig,
  performance: performanceConfig,
  hasStatusSwitch: true,
  statusField: 'status',
  statusActiveValue: 1,
  statusInactiveValue: 0,
  allowBatchDelete: true
}
