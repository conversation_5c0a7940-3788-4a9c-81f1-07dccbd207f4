<script setup lang="ts">
  import { ref } from 'vue'
  import ArtCrudTable from '@/components/core/tables/ArtCrudTable.vue'
  import ArtButtonTable from '@/components/core/forms/art-button-table/index.vue'
  import { countryTableConfig } from './country'

  defineOptions({ name: 'Country' })

  const crudTableRef = ref()

  const componentKey = ref(Date.now())
</script>

<template>
  <div class="country-page">
    <ArtCrudTable ref="crudTableRef" :key="componentKey" :config="countryTableConfig">
      <!-- 操作列 -->
      <template #actions="{ row }">
        <div class="flex gap-2">
          <ArtButtonTable type="edit" @click="crudTableRef?.showDialog('edit', row)" />
        </div>
      </template>
    </ArtCrudTable>
  </div>
</template>

<style lang="scss" scoped>
  .flex {
    display: flex;
  }

  .gap-2 {
    gap: 8px;
  }

  .text-gray-400 {
    color: #9ca3af;
  }
</style>
