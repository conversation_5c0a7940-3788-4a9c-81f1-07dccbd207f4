import { SystemArticleService } from '@/api/systemArticle'
import type {
  CrudTableConfig,
  ExportConfig,
  PermissionConfig,
  SecurityConfig,
  PerformanceConfig,
  ResponsiveConfig
} from '@/components/core/tables/ArtCrudTable.vue'
import { filterEmptyParams } from '@/utils'

// 包装API服务
const createApiService = () => ({
  getList: (params: any) => SystemArticleService.getList(filterEmptyParams(params)),
  delete: async (id: string | number) => {
    await SystemArticleService.remove(Number(id))
    return { code: 0 }
  },
  batchDelete: async (ids: Array<string | number>) => {
    await SystemArticleService.batchRemove(ids.map((v) => Number(v)))
    return { code: 0 }
  },
  update: async (id: string | number, data: any) => {
    await SystemArticleService.update(Number(id), data)
    return { code: 0 }
  },
  create: (data: any) => SystemArticleService.create(data)
})

// 选项与工具
export const articleTypeOptions = [
  { label: '新闻资讯', value: 1 },
  { label: '帮助中心', value: 2 },
  { label: '公告中心', value: 3 }
]

export const statusOptions = [
  { label: '启用', value: 1 },
  { label: '禁用', value: 2 }
]

// 导出配置
const exportConfig: ExportConfig = {
  enabled: true,
  filename: '文章数据导出',
  columns: [
    'id',
    'title_field',
    'content_field',
    'type',
    'route',
    'sort',
    'views',
    'is_top',
    'status',
    'created_at'
  ],
  formats: ['excel', 'csv'],
  maxRows: 10000
}

// 权限/响应/安全/性能
const permissionConfig: PermissionConfig = {
  create: true,
  update: true,
  delete: true,
  export: true
}

const responsiveConfig: ResponsiveConfig = {
  mobile: {
    hideColumns: ['content_field', 'route', 'views', 'created_at'],
    cardView: false,
    breakpoint: 768
  }
}

const securityConfig: SecurityConfig = {
  enableXssProtection: true,
  allowedTags: ['p', 'br', 'strong', 'em', 'ul', 'ol', 'li', 'a', 'img'],
  allowedAttributes: { a: ['href', 'target'], img: ['src', 'alt'], div: ['class'], span: ['class'] }
}

const performanceConfig: PerformanceConfig = {
  enableCache: true,
  cacheTime: 3 * 60 * 1000,
  debounceTime: 300,
  maxCacheSize: 100,
  lazyLoad: true,
  virtualScroll: { enabled: false, itemHeight: 60, buffer: 10 }
}

export const articleTableConfig: CrudTableConfig = {
  title: '文章管理',
  addButtonText: '新增文章',
  deleteButtonText: '删除文章',
  deleteConfirmText: '确定要删除该文章吗？',
  batchDeleteConfirmText: '确定要删除选中的 {count} 篇文章吗？',
  emptyHeight: '500px',

  initialSearchState: {},
  searchFormItems: [
    { key: 'title', label: '标题', type: 'input' },
    { key: 'type', label: '类型', type: 'select', options: articleTypeOptions },
    { key: 'status', label: '状态', type: 'select', options: statusOptions },
    {
      key: 'is_top',
      label: '是否置顶',
      type: 'select',
      options: [
        { label: '是', value: true },
        { label: '否', value: false }
      ]
    },
    { key: 'created_at', label: '创建时间', type: 'datetimerange' }
  ],

  tableColumns: [
    { prop: 'id', label: 'ID', width: 80, fixed: 'left', sortable: true, align: 'center' },
    { prop: 'icon', label: '封面', width: 80, slot: 'icon', align: 'center' },
    { prop: 'title', label: '标题', minWidth: 160 },
    { prop: 'type', label: '类型', width: 120, slot: 'type', align: 'center' },
    { prop: 'route', label: '路由', minWidth: 160 },
    { prop: 'sort', label: '排序', width: 90, align: 'center', sortable: true },
    { prop: 'views', label: '浏览量', width: 100, align: 'right', sortable: true },
    { prop: 'is_top', label: '置顶', width: 90, slot: 'isTop', align: 'center' },
    {
      prop: 'status',
      label: '状态',
      width: 90,
      slot: 'status',
      align: 'center',
      columnType: 'toggle'
    },
    { prop: 'created_at', label: '创建时间', width: 160, align: 'center', columnType: 'date' },
    { prop: 'actions', label: '操作', width: 220, fixed: 'right', slot: 'actions', align: 'center' }
  ],

  apiService: createApiService(),
  paginationKey: { current: 'page', size: 'pageSize' },

  dialog: {
    formItems: [
      { prop: 'icon', label: '封面', type: 'upload', span: 24 },
      {
        prop: 'title_field',
        label: '标题字段',
        type: 'input',
        rules: [{ required: true, message: '必填', trigger: 'blur' }],
        span: 12
      },
      { prop: 'content_field', label: '内容字段', type: 'input', span: 12 },
      { prop: 'route', label: '路由', type: 'input', span: 12 },
      { prop: 'sort', label: '排序', type: 'number', span: 12, config: { precision: 0 } },
      { prop: 'views', label: '浏览量', type: 'number', span: 12, config: { precision: 0 } },
      {
        prop: 'type',
        label: '类型',
        type: 'select',
        options: articleTypeOptions,
        rules: [{ required: true, message: '必选', trigger: 'change' }],
        span: 12
      },
      {
        prop: 'is_top',
        label: '是否置顶',
        type: 'select',
        options: [
          { label: '是', value: true },
          { label: '否', value: false }
        ],
        span: 12
      },
      {
        prop: 'status',
        label: '状态',
        type: 'select',
        options: statusOptions,
        rules: [{ required: true, message: '必选', trigger: 'change' }],
        span: 12
      },
      { prop: 'title', label: '标题(当前语言)', type: 'input', span: 24 },
      { prop: 'content', label: '内容(当前语言)', type: 'richtext', span: 24 }
    ]
  },

  export: exportConfig,
  permissions: permissionConfig,
  responsive: responsiveConfig,
  security: securityConfig,
  performance: performanceConfig,
  hasStatusSwitch: true,
  statusField: 'status',
  statusActiveValue: 1,
  statusInactiveValue: 2,
  allowBatchDelete: true
}
