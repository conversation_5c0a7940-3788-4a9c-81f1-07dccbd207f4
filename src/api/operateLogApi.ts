import request from '@/utils/http'

// 操作日志服务类
export class OperateLogService {
  private static baseURL = '/monitor/operateLog'

  /**
   * 获取操作日志列表
   */
  static async getOperateLogList(params: any) {
    return request.get<any>({
      url: this.baseURL,
      params
    })
  }

  /**
   * 删除操作日志
   */
  static async deleteOperateLog(id: number) {
    return request.del<any>({
      url: `${this.baseURL}/${id}`
    })
  }

  /**
   * 批量删除操作日志
   */
  static async batchDeleteOperateLogs(ids: number[]) {
    return request.del<any>({
      url: `${this.baseURL}/batch`,
      data: { ids }
    })
  }

  /**
   * 获取操作日志详情
   */
  static async getOperateLogDetail(id: number) {
    return request.get<any>({
      url: `${this.baseURL}/${id}`
    })
  }

  /**
   * 导出操作日志
   */
  static async exportOperateLogs(params: any) {
    return request.get<any>({
      url: `${this.baseURL}/export`,
      params,
      responseType: 'blob'
    })
  }
}
