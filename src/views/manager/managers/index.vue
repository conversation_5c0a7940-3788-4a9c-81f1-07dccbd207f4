<script setup lang="ts">
  import { ref } from 'vue'
  import ArtCrudTable from '@/components/core/tables/ArtCrudTable.vue'
  import ArtButtonTable from '@/components/core/forms/art-button-table/index.vue'
  import { managersTableConfig, getStatusText, getStatusClass } from './managers'
  import ManagerService from '@/api/managerManagers'

  const tableRef = ref()

  const handleRefresh = () => {
    tableRef.value?.refresh?.()
  }
</script>

<template>
  <ArtCrudTable ref="tableRef" :config="managersTableConfig">
    <template #avatar="{ row }">
      <img
        v-if="row.avatar"
        :src="row.avatar"
        alt="avatar"
        style="width: 32px; height: 32px; border-radius: 50%"
      />
      <span v-else>—</span>
    </template>

    <template #gender="{ row }">
      <span>{{ row.gender === 1 ? '男' : row.gender === 2 ? '女' : '保密' }}</span>
    </template>

    <template #loginMode="{ row }">
      <span>{{
        row.loginMode === 1 ? '单设备登录' : row.loginMode === 2 ? '多设备登录' : '限制设备数'
      }}</span>
    </template>

    <template #status="{ row }">
      <span :class="['status-tag', getStatusClass(row.status)]">{{
        getStatusText(row.status)
      }}</span>
    </template>

    <template #actions="{ row }">
      <div class="flex gap-2">
        <ArtButtonTable type="edit" />
        <ArtButtonTable type="delete" />
        <el-dropdown trigger="click">
          <ArtButtonTable type="more" />
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="() => ManagerService.setStatus(row.id, 10)">
                <el-icon><Open /></el-icon> 正常
              </el-dropdown-item>
              <el-dropdown-item @click="() => ManagerService.setStatus(row.id, 20)">
                <el-icon><Close /></el-icon> 禁用
              </el-dropdown-item>
              <el-dropdown-item @click="() => ManagerService.setStatus(row.id, 30)">
                <el-icon><Lock /></el-icon> 锁定
              </el-dropdown-item>
              <el-dropdown-item @click="() => ManagerService.setStatus(row.id, 40)">
                <el-icon><Timer /></el-icon> 过期
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </template>

    <template #toolbar-right>
      <ArtButtonTable type="more" text="刷新" @click="handleRefresh" />
    </template>
  </ArtCrudTable>
</template>

<style scoped lang="scss">
  .status-tag {
    display: inline-block;
    padding: 2px 8px;
    font-size: 12px;
    border-radius: 10px;

    &.status-enabled {
      color: #52c41a;
      background-color: #f6ffed;
    }

    &.status-disabled {
      color: #8c8c8c;
      background-color: #f5f5f5;
    }

    &.status-locked {
      color: #faad14;
      background-color: #fff7e6;
    }

    &.status-expired {
      color: #ff4d4f;
      background-color: #fff2f0;
    }
  }

  .flex {
    display: flex;
  }

  .gap-2 {
    gap: 8px;
  }
</style>
