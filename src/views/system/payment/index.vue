<script setup lang="ts">
  import { ref } from 'vue'
  import ArtCrudTable from '@/components/core/tables/ArtCrudTable.vue'
  import ArtButtonTable from '@/components/core/forms/art-button-table/index.vue'
  import {
    paymentTableConfig,
    getStatusClass,
    getStatusText,
    paymentTypeOptions,
    paymentModeOptions
  } from './payment'
  import { SystemPaymentService } from '@/api/systemPayment'

  defineOptions({ name: 'SystemPayment' })

  const crudRef = ref()

  const setEnabled = (row: any, enabled: boolean) => {
    const fn = enabled ? SystemPaymentService.enable : SystemPaymentService.disable
    fn(row.id).then(() => crudRef.value?.refreshData())
  }
</script>

<template>
  <ArtCrudTable ref="crudRef" :config="paymentTableConfig">
    <!-- 图标 -->
    <template #icon="{ row }">
      <div class="icon-wrap">
        <img v-if="row.icon" :src="row.icon" alt="icon" />
        <span v-else class="icon-empty">无</span>
      </div>
    </template>

    <!-- 资产 -->
    <template #asset="{ row }">
      <div class="asset-info">
        <div class="asset-symbol">{{ row.asset?.symbol || 'UNKNOWN' }}</div>
        <div class="asset-name">{{ row.asset?.name || '未知资产' }}</div>
      </div>
    </template>

    <!-- 类型 -->
    <template #type="{ row }">
      <span class="type-tag">{{
        paymentTypeOptions.find((o) => o.value === row.type)?.label || '未知'
      }}</span>
    </template>

    <!-- 模式 -->
    <template #mode="{ row }">
      <span class="mode-tag">{{
        paymentModeOptions.find((o) => o.value === row.mode)?.label || '未知'
      }}</span>
    </template>

    <!-- 状态 -->
    <template #status="{ row }">
      <span :class="['status-tag', getStatusClass(row.status)]">{{
        getStatusText(row.status)
      }}</span>
    </template>

    <!-- 凭证 -->
    <template #proof="{ row }">
      <el-tag :type="row.is_proof ? 'success' : 'info'">{{ row.is_proof ? '需要' : '—' }}</el-tag>
    </template>

    <!-- 跳转客服 -->
    <template #redirectCS="{ row }">
      <el-tag :type="row.is_redirect_cs ? 'warning' : 'info'">{{
        row.is_redirect_cs ? '跳转' : '—'
      }}</el-tag>
    </template>

    <!-- 操作 -->
    <template #actions="{ row }">
      <div class="actions">
        <ArtButtonTable type="edit" @click="crudRef?.showDialog('edit', row)" />
        <ArtButtonTable type="delete" @click="crudRef?.deleteItem(row.id)" />
        <el-dropdown trigger="click">
          <ArtButtonTable type="more" />
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item divided @click="setEnabled(row, true)">
                <el-icon><Open /></el-icon> 启用
              </el-dropdown-item>
              <el-dropdown-item @click="setEnabled(row, false)">
                <el-icon><Close /></el-icon> 禁用
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </template>
  </ArtCrudTable>
</template>

<style scoped lang="scss">
  .icon-wrap {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    overflow: hidden;
    border: 1px solid #eee;
    border-radius: 4px;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .icon-empty {
      font-size: 12px;
      color: #bbb;
    }
  }

  .asset-info {
    .asset-symbol {
      font-weight: 600;
    }

    .asset-name {
      font-size: 12px;
      color: #666;
    }
  }

  .type-tag {
    padding: 2px 6px;
    font-size: 12px;
    color: #1890ff;
    background: #e6f7ff;
    border: 1px solid #91d5ff;
    border-radius: 4px;
  }

  .mode-tag {
    padding: 2px 6px;
    font-size: 12px;
    color: #faad14;
    background: #fffbe6;
    border: 1px solid #ffe58f;
    border-radius: 4px;
  }

  .status-tag {
    padding: 2px 6px;
    font-size: 12px;
    font-weight: 500;
    border-radius: 4px;
  }

  .status-enabled {
    color: #52c41a;
    background: #f6ffed;
    border: 1px solid #b7eb8f;
  }

  .status-disabled {
    color: #8c8c8c;
    background: #f5f5f5;
    border: 1px solid #d9d9d9;
  }

  .actions {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }
</style>
