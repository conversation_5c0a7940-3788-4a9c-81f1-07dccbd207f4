import { LanguageService } from '@/api/systemLanguage'
import type {
  CrudTableConfig,
  BatchAction,
  ExportConfig,
  PermissionConfig,
  SecurityConfig,
  PerformanceConfig,
  ResponsiveConfig
} from '@/components/core/tables/ArtCrudTable.vue'

// 过滤空值参数的工具函数
const filterEmptyParams = (params: Record<string, any>) => {
  const filtered: Record<string, any> = {}
  Object.keys(params).forEach((key) => {
    const value = params[key]
    // 只保留非空值：非空字符串、非null、非undefined、数字0、布尔值false
    if (value !== '' && value !== null && value !== undefined) {
      filtered[key] = value
    }
  })
  return filtered
}

// 包装API服务，添加参数过滤
const createLanguageApiService = () => ({
  getList: (params: any) => {
    // 过滤空值参数，解决URL中出现空值的问题
    const filteredParams = filterEmptyParams(params)
    console.log('Language API - 过滤后的请求参数:', filteredParams)
    return LanguageService.getLanguageList(filteredParams)
  },
  delete: (id: any) => LanguageService.deleteLanguage(id),
  batchDelete: (ids: any[]) => LanguageService.batchDeleteLanguages(ids),
  update: (id: any, data: any) => LanguageService.updateLanguage(id, data),
  create: (data: any) => LanguageService.createLanguage(data)
})

// 批量操作配置
const batchActions: BatchAction[] = [
  {
    label: '批量启用',
    key: 'batchEnable',
    type: 'success',
    icon: 'Check',
    confirm: '确认启用选中的 {count} 个语言吗？',
    handler: async (selectedRows) => {
      console.log('批量启用语言:', selectedRows)
      // 调用批量更新API
      await Promise.all(
        selectedRows.map((row) => LanguageService.updateLanguage(row.id as number, { status: 1 }))
      )
    }
  },
  {
    label: '批量禁用',
    key: 'batchDisable',
    type: 'warning',
    icon: 'Close',
    confirm: '确认禁用选中的 {count} 个语言吗？',
    handler: async (selectedRows) => {
      console.log('批量禁用语言:', selectedRows)
      await Promise.all(
        selectedRows.map((row) => LanguageService.updateLanguage(row.id as number, { status: 0 }))
      )
    }
  },
  {
    label: '批量导出',
    key: 'batchExport',
    type: 'primary',
    icon: 'Download',
    handler: async (selectedRows) => {
      console.log('批量导出语言:', selectedRows)
      // 这里可以实现导出逻辑
      const exportData = selectedRows.map((row) => ({
        code: row.code as string,
        name: row.name as string,
        nameNative: row.nameNative as string,
        status: (row.status as number) === 1 ? '启用' : '禁用',
        sort: row.sort as number,
        created_at: row.created_at as string
      }))

      // 模拟导出（实际项目中可以调用导出API或生成Excel）
      console.log('导出数据:', exportData)
    }
  }
]

// 导出配置
const exportConfig: ExportConfig = {
  enabled: true,
  filename: '语言数据导出',
  columns: ['code', 'name', 'nameNative', 'status', 'sort', 'created_at'],
  formats: ['excel', 'csv'],
  maxRows: 1000
}

// 权限配置
const permissionConfig: PermissionConfig = {
  create: () => {
    // 这里可以根据用户权限判断
    return true
  },
  update: (row) => {
    // 例如：某些系统语言不允许编辑
    return !['zh-CN', 'en-US'].includes(row.code as string)
  },
  delete: (row) => {
    // 例如：默认语言不允许删除
    return (row.code as string) !== 'zh-CN'
  },
  export: () => true,
  batchDelete: () => true
}

// 响应式配置
const responsiveConfig: ResponsiveConfig = {
  mobile: {
    hideColumns: ['nameNative', 'sort', 'created_at'],
    cardView: false,
    breakpoint: 768
  }
}

// 安全配置
const securityConfig: SecurityConfig = {
  enableXssProtection: true,
  allowedTags: ['p', 'br', 'strong', 'em'],
  allowedAttributes: {
    div: ['class'],
    span: ['class']
  }
}

// 性能配置
const performanceConfig: PerformanceConfig = {
  enableCache: true,
  cacheTime: 3 * 60 * 1000, // 3分钟
  debounceTime: 500,
  maxCacheSize: 50,
  lazyLoad: true,
  virtualScroll: {
    enabled: false, // 语言数据量通常不大
    itemHeight: 60,
    buffer: 10
  }
}

// 语言管理配置
export const languageTableConfig: CrudTableConfig = {
  title: '语言',
  addButtonText: '新增语言',
  deleteButtonText: '删除语言',
  deleteConfirmText: '确定要删除该语言吗？',
  batchDeleteConfirmText: '确定要删除选中的 {count} 条语言吗？',

  initialSearchState: {
    code: '',
    name: '',
    nameNative: '',
    status: ''
  },

  searchFormItems: [
    {
      key: 'code',
      label: '语言代码',
      type: 'input',
      placeholder: '请输入语言代码'
    },
    {
      key: 'name',
      label: '中文名称',
      type: 'input',
      placeholder: '请输入中文名称'
    },
    {
      key: 'nameNative',
      label: '本地名称',
      type: 'input',
      placeholder: '请输入本地名称'
    },
    {
      key: 'status',
      label: '状态',
      type: 'select',
      placeholder: '请选择状态',
      options: [
        { label: '启用', value: '1' },
        { label: '禁用', value: '0' }
      ]
    }
  ],

  tableColumns: [
    { type: 'selection', width: 55 },
    { label: 'ID', prop: 'id', width: 80, sortable: true },
    {
      label: '图标',
      prop: 'icon',
      width: 80,
      columnType: 'image',
      useSlot: true,
      slotName: 'icon'
    },
    {
      label: '语言代码',
      prop: 'code',
      minWidth: 120,
      columnType: 'copy'
    },
    { label: '中文名称', prop: 'name', minWidth: 120 },
    {
      label: '本地名称',
      prop: 'name_native',
      minWidth: 120,
      showOverflowTooltip: true,
      columnType: 'rows'
    },
    { label: '排序', prop: 'sort', width: 80, sortable: true },
    {
      label: '状态',
      prop: 'status',
      width: 100,
      columnType: 'toggle',
      useSlot: true,
      slotName: 'status'
    },
    {
      label: '创建时间',
      prop: 'created_at',
      minWidth: 160,
      sortable: true,
      columnType: 'date'
    },
    {
      label: '操作',
      width: 150,
      fixed: 'right',
      useSlot: true,
      slotName: 'actions'
    }
  ],

  apiService: createLanguageApiService(),

  // 🔧 解决分页参数重复问题的关键配置
  paginationKey: {
    current: 'page', // 将内部的 current 映射为 page
    size: 'pageSize' // 将内部的 size 映射为 pageSize
  },

  dialog: {
    title: {
      add: '添加语言',
      edit: '编辑语言'
    },
    width: '600px',
    formItems: [
      {
        label: '语言代码',
        prop: 'code',
        type: 'input',
        required: true,
        config: {
          placeholder: '例如: zh-CN',
          maxlength: 10
        },
        rules: [
          { required: true, message: '请输入语言代码', trigger: 'blur' },
          { max: 10, message: '语言代码长度不能超过 10 个字符', trigger: 'blur' }
        ],
        span: 12,
        show: (formData, type) => type === 'add'
      },
      {
        label: '中文名称',
        prop: 'name',
        type: 'input',
        required: true,
        config: {
          placeholder: '例如: 简体中文',
          maxlength: 60
        },
        rules: [
          { required: true, message: '请输入中文名称', trigger: 'blur' },
          { max: 60, message: '中文名称长度不能超过 60 个字符', trigger: 'blur' }
        ],
        span: 12
      },
      {
        label: '本地语言名称',
        prop: 'nameNative',
        type: 'input',
        required: true,
        config: {
          placeholder: '例如: Chinese (Simplified)',
          maxlength: 60
        },
        rules: [
          { required: true, message: '请输入本地语言名称', trigger: 'blur' },
          { max: 60, message: '本地语言名称长度不能超过 60 个字符', trigger: 'blur' }
        ],
        span: 24
      },
      {
        label: '语言图标',
        prop: 'icon',
        type: 'upload',
        config: {
          accept: 'image/*',
          maxSize: 5,
          triggerText: '选择语言图标',
          tip: '支持 JPG、PNG 格式，文件大小不超过 5MB'
        },
        span: 24
      },
      {
        label: '排序',
        prop: 'sort',
        type: 'number',
        required: true,
        config: {
          min: 0,
          max: 255,
          placeholder: '请输入排序值'
        },
        span: 12
      },
      {
        label: '状态',
        prop: 'status',
        type: 'select',
        required: true,
        options: [
          { label: '启用', value: 1 },
          { label: '禁用', value: 0 }
        ],
        span: 12
      }
    ],
    initialFormData: {
      code: '',
      name: '',
      nameNative: '',
      icon: '',
      sort: 1,
      status: 1
    }
  },

  // 🚀 新增的功能配置
  batchActions, // 批量操作
  export: exportConfig, // 导出功能
  permissions: permissionConfig, // 权限控制
  responsive: responsiveConfig, // 响应式配置
  security: securityConfig, // 安全配置
  performance: performanceConfig, // 性能配置

  // 状态切换配置
  hasStatusSwitch: true,
  statusField: 'status',
  statusActiveValue: 1,
  statusInactiveValue: 0,

  // 操作权限
  allowBatchDelete: true
}
