import request from '@/utils/http'

const baseUrl = '/admin/manager/managers'

export class ManagerService {
  static list(params: any) {
    return request.get<any>({ url: `${baseUrl}/list`, params })
  }

  static detail(id: string | number) {
    return request.get<any>({ url: `${baseUrl}/detail/${id}` })
  }

  static create(data: any) {
    return request.post<any>({ url: `${baseUrl}/create`, data })
  }

  static update(id: string | number, data: any) {
    return request.post<any>({ url: `${baseUrl}/update/${id}`, data })
  }

  static delete(id: string | number) {
    return request.post<any>({ url: `${baseUrl}/delete/${id}` })
  }

  static batchDelete(ids: Array<string | number>) {
    return request.post<any>({ url: `${baseUrl}/batch/delete`, data: { ids } })
  }

  static setStatus(id: string | number, status: number) {
    return request.post<any>({ url: `${baseUrl}/status/${id}`, data: { status } })
  }

  static batchEnable(ids: Array<string | number>) {
    return request.post<any>({ url: `${baseUrl}/batch/enable`, data: { ids } })
  }

  static batchDisable(ids: Array<string | number>) {
    return request.post<any>({ url: `${baseUrl}/batch/disable`, data: { ids } })
  }

  static export(params: any) {
    return request.get<any>({ url: `${baseUrl}/export`, params, responseType: 'blob' })
  }
}

export default ManagerService
