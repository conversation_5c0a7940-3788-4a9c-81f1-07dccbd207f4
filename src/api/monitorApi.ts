import request from '@/utils/http'

export interface SystemInfo {
  sysComputerName: string
  sysOsName: string
  sysComputerIp: string
  sysOsArch: string
  goName: string
  goVersion: string
  goStartTime: string
  goRunTime: string
}

export interface CpuUsage {
  cpuNum: number
  cpuUsed: number
  cpuAvg5: number
  cpuAvg15: number
}

export interface MemUsage {
  memTotal: number
  memUsed: number
  memFree: number
  memUsage: number
}

export interface DiskUsageStat {
  path: string
  fstype: string
  total: number
  free: number
  used: number
  usedPercent: number
  inodesTotal: number
  inodesUsed: number
  inodesFree: number
  inodesUsedPercent: number
}

export interface ServerMonitorInfo {
  systemSes: SystemInfo
  storageSes: DiskUsageStat[]
  cpuUsage: CpuUsage
  memoryUsage: MemUsage
}

export class MonitorService {
  /**
   * 获取服务器监控信息
   */
  static async getServerInfo(): Promise<ServerMonitorInfo> {
    return request.get<ServerMonitorInfo>({
      url: '/monitor/server'
    })
  }
}
