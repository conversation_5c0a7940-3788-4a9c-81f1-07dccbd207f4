import request from '@/utils/http'

/**
 * 等级管理服务
 * 类型：1 会员等级
 * 状态：1 启用 0 禁用
 */
export class SystemLevelService {
  private static baseUrl = '/system/levels'

  static readonly TYPE_MEMBER = 1

  static readonly STATUS_ENABLED = 1
  static readonly STATUS_DISABLED = 0

  // ============ 基础 CRUD ============
  static getList(params?: any) {
    return request.get<any>({ url: this.baseUrl, params })
  }

  static getDetail(id: number) {
    return request.get<any>({ url: `${this.baseUrl}/${id}` })
  }

  static create(data: any) {
    return request.post<any>({ url: this.baseUrl, data })
  }

  static update(id: number, data: any) {
    return request.put<any>({ url: `${this.baseUrl}/${id}`, data })
  }

  static remove(id: number) {
    return request.del<any>({ url: `${this.baseUrl}/${id}` })
  }

  static batchRemove(ids: number[]) {
    return request.del<any>({ url: `${this.baseUrl}/batch`, data: { ids } })
  }

  // ============ 状态/排序 ============
  static enable(id: number) {
    return request.put<any>({ url: `${this.baseUrl}/${id}/enable` })
  }

  static disable(id: number) {
    return request.put<any>({ url: `${this.baseUrl}/${id}/disable` })
  }

  static setSort(id: number, sort: number) {
    return request.put<any>({ url: `${this.baseUrl}/${id}/sort`, data: { sort } })
  }

  // ============ 导出/搜索 ============
  static exportList(params?: any) {
    return request.get<any>({ url: `${this.baseUrl}/export`, params, responseType: 'blob' })
  }

  static search(params: any) {
    return request.get<any>({ url: `${this.baseUrl}/search`, params })
  }
}
