<script setup lang="ts">
  import { ref } from 'vue'
  import ArtCrudTable from '@/components/core/tables/ArtCrudTable.vue'
  import ArtButtonTable from '@/components/core/forms/art-button-table/index.vue'
  import { languageTableConfig } from './language'

  defineOptions({ name: 'Language' })

  const crudTableRef = ref()

  const componentKey = ref(Date.now())
</script>

<template>
  <ArtCrudTable ref="crudTableRef" :key="componentKey" :config="languageTableConfig">
    <!-- 操作列 -->
    <template #actions="{ row }">
      <div class="flex gap-2">
        <ArtButtonTable type="edit" @click="crudTableRef?.showDialog('edit', row)" />
      </div>
    </template>
  </ArtCrudTable>
</template>

<style lang="scss" scoped>
  .flex {
    display: flex;
  }

  .gap-2 {
    gap: 8px;
  }

  .text-gray-400 {
    color: #9ca3af;
  }

  .demo-actions {
    .flex {
      flex-wrap: wrap;
    }

    h4 {
      font-weight: 500;
    }
  }
</style>
