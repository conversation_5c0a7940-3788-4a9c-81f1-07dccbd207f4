<script setup lang="ts">
  import { ref } from 'vue'
  import ArtCrudTable from '@/components/core/tables/ArtCrudTable.vue'
  import ArtButtonTable from '@/components/core/forms/art-button-table/index.vue'
  import { userTableConfig, getGenderText, getTypeText, getStatusText } from './users'

  defineOptions({ name: 'Users' })

  const crudTableRef = ref()
  const componentKey = ref(Date.now())

  // 性别渲染
  const renderGender = (row: any) => {
    return getGenderText(row.gender)
  }

  // 用户类型渲染
  const renderType = (row: any) => {
    const typeText = getTypeText(row.type)
    const typeClass = row.type === 1 ? 'virtual-user' : 'normal-user'
    return { text: typeText, class: typeClass }
  }

  // 状态渲染
  const renderStatus = (row: any) => {
    const statusText = getStatusText(row.status)
    let statusClass = ''
    switch (row.status) {
      case 1:
        statusClass = 'status-active'
        break
      case 2:
        statusClass = 'status-disabled'
        break
      case 3:
        statusClass = 'status-frozen'
        break
      case 4:
        statusClass = 'status-locked'
        break
    }
    return { text: statusText, class: statusClass }
  }
</script>

<template>
  <ArtCrudTable ref="crudTableRef" :key="componentKey" :config="userTableConfig">
    <!-- 性别列 -->
    <template #gender="{ row }">
      <span class="gender-text">{{ renderGender(row) }}</span>
    </template>

    <!-- 用户类型列 -->
    <template #type="{ row }">
      <span :class="['type-tag', renderType(row).class]">
        {{ renderType(row).text }}
      </span>
    </template>

    <!-- 操作列 -->
    <template #actions="{ row }">
      <div class="flex gap-2">
        <ArtButtonTable type="edit" @click="crudTableRef?.showDialog('edit', row)" />
        <ArtButtonTable type="delete" @click="crudTableRef?.deleteRow(row)" />
      </div>
    </template>
  </ArtCrudTable>
</template>

<style lang="scss" scoped>
  .flex {
    display: flex;
  }

  .gap-2 {
    gap: 8px;
  }

  // 头像样式
  .avatar-container {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .user-avatar {
    width: 40px;
    height: 40px;
    object-fit: cover;
    border: 2px solid #f0f0f0;
    border-radius: 50%;
  }

  // 性别样式
  .gender-text {
    font-size: 14px;
    color: #666;
  }

  // 用户类型标签样式
  .type-tag {
    padding: 4px 8px;
    font-size: 12px;
    font-weight: 500;
    border-radius: 4px;

    &.virtual-user {
      color: #1976d2;
      background-color: #e3f2fd;
      border: 1px solid #bbdefb;
    }

    &.normal-user {
      color: #7b1fa2;
      background-color: #f3e5f5;
      border: 1px solid #ce93d8;
    }
  }

  // 状态标签样式
  .status-tag {
    padding: 4px 8px;
    font-size: 12px;
    font-weight: 500;
    border-radius: 4px;

    &.status-active {
      color: #2e7d32;
      background-color: #e8f5e8;
      border: 1px solid #a5d6a7;
    }

    &.status-disabled {
      color: #ef6c00;
      background-color: #fff3e0;
      border: 1px solid #ffcc02;
    }

    &.status-frozen {
      color: #c62828;
      background-color: #ffebee;
      border: 1px solid #ef9a9a;
    }

    &.status-locked {
      color: #ad1457;
      background-color: #fce4ec;
      border: 1px solid #f48fb1;
    }
  }

  // 操作按钮样式
  .demo-actions {
    .flex {
      flex-wrap: wrap;
    }

    h4 {
      font-weight: 500;
    }
  }
</style>
