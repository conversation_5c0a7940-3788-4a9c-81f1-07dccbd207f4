<script setup lang="ts">
  import { ref } from 'vue'
  import { ElTag } from 'element-plus'
  import ArtCrudTable from '@/components/core/tables/ArtCrudTable.vue'
  import ArtButtonTable from '@/components/core/forms/art-button-table/index.vue'
  import {
    operateLogTableConfig,
    getOperateTypeTagType,
    getOperateTypeText,
    getOperateModeTagType,
    getOperateModeText,
    getOperateStatusTagType,
    getOperateStatusText
  } from './operateLog'

  defineOptions({ name: 'OperateLog' })

  const crudTableRef = ref()
</script>

<template>
  <ArtCrudTable ref="crudTableRef" :config="operateLogTableConfig">
    <!-- 操作类型列 -->
    <template #type="{ row }">
      <ElTag :type="getOperateTypeTagType(row.type)" size="small">
        {{ getOperateTypeText(row.type) }}
      </ElTag>
    </template>

    <!-- 操作模式列 -->
    <template #mode="{ row }">
      <ElTag :type="getOperateModeTagType(row.mode)" size="small">
        {{ getOperateModeText(row.mode) }}
      </ElTag>
    </template>

    <!-- 操作状态列 -->
    <template #status="{ row }">
      <ElTag :type="getOperateStatusTagType(row.status)" size="small">
        {{ getOperateStatusText(row.status) }}
      </ElTag>
    </template>

    <!-- 操作列 -->
    <template #actions="{ row }">
      <div class="flex gap-2">
        <ArtButtonTable type="delete" @click="crudTableRef?.deleteRow(row)" />
      </div>
    </template>
  </ArtCrudTable>
</template>

<style lang="scss" scoped>
  .flex {
    display: flex;
  }

  .gap-2 {
    gap: 8px;
  }

  :deep(.el-message-box__content) {
    text-align: left;
  }
</style>
