// 国家管理配置
import { CountryService } from '@/api/systemCountry'
import type {
  CrudTableConfig,
  BatchAction,
  ExportConfig,
  PermissionConfig,
  SecurityConfig,
  PerformanceConfig,
  ResponsiveConfig
} from '@/components/core/tables/ArtCrudTable.vue'

// 过滤空值参数的工具函数
const filterEmptyParams = (params: Record<string, any>) => {
  const filtered: Record<string, any> = {}
  Object.keys(params).forEach((key) => {
    const value = params[key]
    if (value !== '' && value !== null && value !== undefined) {
      filtered[key] = value
    }
  })
  return filtered
}

// 包装API服务，添加参数过滤
const createCountryApiService = () => ({
  getList: (params: any) => {
    const filteredParams = filterEmptyParams(params)
    console.log('Country API - 过滤后的请求参数:', filteredParams)
    return CountryService.getCountryList(filteredParams)
  },
  delete: (id: any) => CountryService.deleteCountry(id),
  batchDelete: (ids: any[]) => CountryService.batchDeleteCountries(ids),
  update: (id: any, data: any) => CountryService.updateCountry(id, data),
  create: (data: any) => CountryService.createCountry(data)
})

// 批量操作配置
const batchActions: BatchAction[] = [
  {
    label: '批量启用',
    key: 'batchEnable',
    type: 'success',
    icon: 'Check',
    confirm: '确认启用选中的 {count} 个国家吗？',
    handler: async (selectedRows) => {
      await Promise.all(
        selectedRows.map((row) => CountryService.updateCountry(row.id as number, { status: 1 }))
      )
    }
  },
  {
    label: '批量禁用',
    key: 'batchDisable',
    type: 'warning',
    icon: 'Close',
    confirm: '确认禁用选中的 {count} 个国家吗？',
    handler: async (selectedRows) => {
      await Promise.all(
        selectedRows.map((row) => CountryService.updateCountry(row.id as number, { status: 0 }))
      )
    }
  }
]

// 导出配置
const exportConfig: ExportConfig = {
  enabled: true,
  filename: '国家数据导出',
  columns: ['code', 'name', 'name_en', 'status', 'sort', 'created_at'],
  formats: ['excel', 'csv'],
  maxRows: 1000
}

// 权限配置
const permissionConfig: PermissionConfig = {
  create: () => true,
  update: (row) => (row.code as string) !== 'CN', // 例如：中国不允许编辑
  delete: (row) => (row.code as string) !== 'CN',
  export: () => true,
  batchDelete: () => true
}

// 性能配置
const performanceConfig: PerformanceConfig = {
  enableCache: true,
  cacheTime: 5 * 60 * 1000, // 5分钟
  debounceTime: 500,
  maxCacheSize: 100,
  lazyLoad: true,
  virtualScroll: {
    enabled: false,
    itemHeight: 60,
    buffer: 10
  }
}

export const countryTableConfig = {
  title: '国家',
  addButtonText: '新增国家',
  deleteButtonText: '删除国家',
  deleteConfirmText: '确定要删除该国家吗？',
  batchDeleteConfirmText: '确定要删除选中的 {count} 条国家吗？',

  initialSearchState: {
    nameZh: '',
    nameNative: '',
    iso2: '',
    code: '',
    status: ''
  },

  searchFormItems: [
    {
      key: 'nameZh',
      label: '中文名称',
      prop: 'name_zh',
      type: 'input',
      config: {
        clearable: true,
        placeholder: '请输入中文名称'
      }
    },
    {
      key: 'nameNative',
      label: '本地名称',
      prop: 'name_native',
      type: 'input',
      config: {
        clearable: true,
        placeholder: '请输入本地名称'
      }
    },
    {
      key: 'iso2',
      label: 'ISO代码',
      prop: 'iso2',
      type: 'input',
      config: {
        clearable: true,
        placeholder: '请输入ISO代码'
      }
    },
    {
      key: 'code',
      label: '国家区号',
      prop: 'code',
      type: 'input',
      config: {
        clearable: true,
        placeholder: '请输入国家区号'
      }
    },
    {
      key: 'status',
      label: '状态',
      prop: 'status',
      type: 'select',
      config: {
        clearable: true,
        placeholder: '请选择状态'
      },
      options: [
        { label: '启用', value: '1' },
        { label: '禁用', value: '0' }
      ]
    }
  ],

  tableColumns: [
    { type: 'selection' as const, width: 55 },
    { label: 'ID', prop: 'id', width: 80, sortable: true },
    { label: '国旗', prop: 'icon', width: 80, slot: 'icon', columnType: 'image' },
    { label: '中文名称', prop: 'name_zh', minWidth: 120 },
    { label: '本地名称', prop: 'name_native', minWidth: 120, showOverflowTooltip: true },
    { label: 'ISO代码', prop: 'iso2', width: 100, slot: 'iso2', columnType: 'tag' },
    { label: '国家区号', prop: 'code', width: 100, slot: 'code' },
    { label: '排序', prop: 'sort', width: 80, sortable: true },
    { label: '状态', prop: 'status', width: 100, slot: 'status', columnType: 'toggle' },
    {
      label: '创建时间',
      prop: 'created_at',
      minWidth: 160,
      sortable: true,
      slot: 'created_at',
      columnType: 'date'
    },
    { label: '操作', width: 150, fixed: 'right' as const, slot: 'actions' }
  ],

  apiService: createCountryApiService(),

  // 自定义分页字段映射，适配后端接口
  paginationKey: {
    current: 'page',
    size: 'pageSize',
    orderBy: 'orderBy',
    order: 'order'
  },

  dialog: {
    title: {
      add: '添加国家',
      edit: '编辑国家'
    },
    width: '600px',
    formItems: [
      {
        label: '中文名称',
        prop: 'nameZh',
        type: 'input' as const,
        required: true,
        config: {
          placeholder: '请输入中文名称',
          maxlength: 60
        },
        rules: [
          { required: true, message: '请输入中文名称', trigger: 'blur' },
          { max: 60, message: '中文名称长度不能超过 60 个字符', trigger: 'blur' }
        ],
        span: 12
      },
      {
        label: '本地名称',
        prop: 'nameNative',
        type: 'input' as const,
        required: true,
        config: {
          placeholder: '请输入本地语言名称',
          maxlength: 60
        },
        rules: [
          { required: true, message: '请输入本地名称', trigger: 'blur' },
          { max: 60, message: '本地名称长度不能超过 60 个字符', trigger: 'blur' }
        ],
        span: 12
      },
      {
        label: 'ISO代码',
        prop: 'iso2',
        type: 'input' as const,
        required: true,
        config: {
          placeholder: '请输入ISO代码',
          maxlength: 2
        },
        rules: [
          { required: true, message: '请输入ISO代码', trigger: 'blur' },
          { len: 2, message: 'ISO代码必须为2位字符', trigger: 'blur' },
          { pattern: /^[A-Z]{2}$/, message: 'ISO代码必须为大写字母', trigger: 'blur' }
        ],
        span: 12,
        show: (formData: any, type: 'add' | 'edit') => type === 'add'
      },
      {
        label: '国家区号',
        prop: 'code',
        type: 'input' as const,
        required: true,
        config: {
          placeholder: '请输入国家区号'
        },
        rules: [
          { required: true, message: '请输入国家区号', trigger: 'blur' },
          { max: 10, message: '长度不能超过 10 个字符', trigger: 'blur' },
          { pattern: /^\d+$/, message: '国家区号只能包含数字', trigger: 'blur' }
        ],
        span: 12
      },
      {
        label: '国旗图标',
        prop: 'icon',
        type: 'upload' as const,
        config: {
          accept: 'image/*',
          maxSize: 5,
          triggerText: '选择国旗图片',
          tip: '支持 JPG、PNG 格式，文件大小不超过 5MB'
        },
        span: 24
      },
      {
        label: '排序',
        prop: 'sort',
        type: 'number' as const,
        required: true,
        config: {
          min: 0,
          max: 255,
          placeholder: '请输入排序值'
        },
        span: 12
      },
      {
        label: '状态',
        prop: 'status',
        type: 'select' as const,
        required: true,
        options: [
          { label: '启用', value: 1 },
          { label: '禁用', value: 0 }
        ],
        span: 12
      }
    ],
    initialFormData: {
      nameZh: '',
      nameNative: '',
      iso2: '',
      code: '',
      icon: '',
      sort: 1,
      status: 1
    }
  },

  hasStatusSwitch: true,
  statusField: 'status',
  statusActiveValue: 1,
  statusInactiveValue: 0,

  // 批量操作配置
  batchActions: batchActions,

  // 导出配置
  exportConfig: exportConfig,

  // 权限配置
  permissionConfig: permissionConfig,

  // 性能配置
  performanceConfig: performanceConfig,

  // 响应式配置
  responsiveConfig: {} as ResponsiveConfig,

  // 安全配置
  securityConfig: {} as SecurityConfig,

  // 表格配置
  tableConfig: {} as CrudTableConfig,

  // 表格事件
  tableEvents: {}
}
