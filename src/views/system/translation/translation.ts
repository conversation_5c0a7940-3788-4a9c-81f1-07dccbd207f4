// 翻译管理配置
import { TranslationService } from '@/api/systemTranslation'

// 过滤空值参数的工具函数
const filterEmptyParams = (params: Record<string, any>) => {
  const filtered: Record<string, any> = {}
  Object.keys(params).forEach((key) => {
    const value = params[key]
    if (value !== '' && value !== null && value !== undefined) {
      filtered[key] = value
    }
  })
  return filtered
}

// 包装API服务，添加参数过滤
const createTranslationApiService = () => ({
  getList: (params: any) => {
    const filteredParams = filterEmptyParams(params)
    console.log('Translation API - 过滤后的请求参数:', filteredParams)
    return TranslationService.getTranslationList(filteredParams)
  },
  delete: (id: any) => TranslationService.deleteTranslation(id),
  batchDelete: (ids: any[]) => TranslationService.batchDeleteTranslations(ids),
  update: (id: any, data: any) => TranslationService.updateTranslation(id, data),
  create: (data: any) => TranslationService.createTranslation(data)
})

export const translationTableConfig = {
  title: '翻译',
  addButtonText: '新增翻译',
  deleteButtonText: '删除翻译',
  deleteConfirmText: '确定要删除该翻译吗？',
  batchDeleteConfirmText: '确定要删除选中的 {count} 条翻译吗？',

  initialSearchState: {
    key: '',
    lang: '',
    module: '',
    status: ''
  },

  searchFormItems: [
    {
      key: 'key',
      label: '翻译键',
      prop: 'key',
      type: 'input',
      config: {
        clearable: true,
        placeholder: '请输入翻译键'
      }
    },
    {
      key: 'lang',
      label: '语言代码',
      prop: 'lang',
      type: 'input',
      config: {
        clearable: true,
        placeholder: '请输入语言代码'
      }
    },
    {
      key: 'module',
      label: '所属模块',
      prop: 'module',
      type: 'input',
      config: {
        clearable: true,
        placeholder: '请输入所属模块'
      }
    },
    {
      key: 'status',
      label: '状态',
      prop: 'status',
      type: 'select',
      config: {
        clearable: true,
        placeholder: '请选择状态'
      },
      options: [
        { label: '启用', value: '1' },
        { label: '禁用', value: '0' }
      ]
    }
  ],

  tableColumns: [
    { type: 'selection' as const, width: 55 },
    { label: 'ID', prop: 'id', width: 80, sortable: true },
    { label: '翻译键', prop: 'key', minWidth: 150, showOverflowTooltip: true },
    { label: '翻译值', prop: 'value', minWidth: 200, showOverflowTooltip: true },
    { label: '语言代码', prop: 'lang', width: 100, slot: 'lang', columnType: 'tag' as const },
    { label: '所属模块', prop: 'module', width: 120 },
    {
      label: '类型',
      prop: 'type',
      width: 100,
      slot: 'type',
      columnType: 'tag' as const,
      options: [
        { label: '系统翻译', value: '1' },
        { label: '语言包', value: '2' }
      ]
    },
    { label: '状态', prop: 'status', width: 100, slot: 'status', columnType: 'toggle' },
    {
      label: '创建时间',
      prop: 'created_at',
      minWidth: 160,
      sortable: true,
      slot: 'created_at',
      columnType: 'date' as const
    },
    { label: '操作', width: 150, fixed: 'right' as const, slot: 'actions' }
  ],

  apiService: createTranslationApiService(),

  // 自定义分页字段映射，适配后端接口
  paginationKey: {
    current: 'page',
    size: 'pageSize',
    orderBy: 'orderBy',
    order: 'order'
  },

  dialog: {
    title: {
      add: '添加翻译',
      edit: '编辑翻译'
    },
    width: '700px',
    formItems: [
      {
        label: '翻译键',
        prop: 'key',
        type: 'input' as const,
        required: true,
        config: {
          placeholder: '请输入翻译键',
          maxlength: 100
        },
        rules: [
          { required: true, message: '请输入翻译键', trigger: 'blur' },
          { max: 100, message: '翻译键长度不能超过 100 个字符', trigger: 'blur' }
        ],
        span: 12,
        show: (formData: any, type: 'add' | 'edit') => type === 'add'
      },
      {
        label: '语言代码',
        prop: 'lang',
        type: 'input' as const,
        required: true,
        config: {
          placeholder: '请输入语言代码',
          maxlength: 10
        },
        rules: [
          { required: true, message: '请输入语言代码', trigger: 'blur' },
          { max: 10, message: '语言代码长度不能超过 10 个字符', trigger: 'blur' }
        ],
        span: 12,
        show: (formData: any, type: 'add' | 'edit') => type === 'add'
      },
      {
        label: '翻译值',
        prop: 'value',
        type: 'textarea' as const,
        required: true,
        config: {
          placeholder: '请输入翻译值',
          rows: 4
        },
        rules: [{ required: true, message: '请输入翻译值', trigger: 'blur' }],
        span: 24
      },
      {
        label: '所属模块',
        prop: 'module',
        type: 'input' as const,
        required: true,
        config: {
          placeholder: '请输入所属模块',
          maxlength: 50
        },
        rules: [
          { required: true, message: '请输入所属模块', trigger: 'blur' },
          { max: 50, message: '模块名称长度不能超过 50 个字符', trigger: 'blur' }
        ],
        span: 12
      },
      {
        label: '类型',
        prop: 'type',
        type: 'select' as const,
        required: true,
        options: [
          { label: '系统', value: 1 },
          { label: '语言包', value: 2 }
        ],
        span: 12
      },
      {
        label: '状态',
        prop: 'status',
        type: 'select' as const,
        required: true,
        options: [
          { label: '启用', value: 1 },
          { label: '禁用', value: 0 }
        ],
        span: 12
      }
    ],
    initialFormData: {
      key: '',
      value: '',
      lang: '',
      module: '',
      type: 1,
      status: 1
    }
  },

  hasStatusSwitch: true,
  statusField: 'status',
  statusActiveValue: 1,
  statusInactiveValue: 0
}
