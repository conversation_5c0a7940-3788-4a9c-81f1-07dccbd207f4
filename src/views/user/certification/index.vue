<script setup lang="ts">
  import { ref } from 'vue'
  import ArtCrudTable from '@/components/core/tables/ArtCrudTable.vue'
  import ArtButtonTable from '@/components/core/forms/art-button-table/index.vue'
  import {
    certificationTableConfig,
    getTypeText,
    getStatusText,
    getModeText
  } from './certification'

  defineOptions({ name: 'UserCertification' })

  const crudTableRef = ref()
  const componentKey = ref(Date.now())

  // 用户信息渲染
  const renderUser = (row: any) => {
    if (row.user) {
      return {
        id: row.user.id,
        username: row.user.username,
        nickname: row.user.nickname
      }
    }
    return { id: row.user_id, username: '未知用户', nickname: '' }
  }

  // 证件类型渲染
  const renderType = (row: any) => {
    const typeText = getTypeText(row.type)
    return { text: typeText, class: 'type-id-card' }
  }

  // 认证模式渲染
  const renderMode = (row: any) => {
    const modeText = getModeText(row.mode)
    return { text: modeText, class: 'mode-identity' }
  }

  // 证件照渲染
  const renderPhotos = (row: any) => {
    return {
      photo1: row.photo1,
      photo2: row.photo2,
      photo3: row.photo3
    }
  }

  // 状态渲染
  const renderStatus = (row: any) => {
    const statusText = getStatusText(row.status)
    let statusClass = ''
    switch (row.status) {
      case 1:
        statusClass = 'status-pending'
        break
      case 2:
        statusClass = 'status-approved'
        break
      case 3:
        statusClass = 'status-rejected'
        break
    }
    return { text: statusText, class: statusClass }
  }

  // 查看认证详情
  const viewCertificationDetail = (row: any) => {
    console.log('查看认证详情:', row)
    // 这里可以实现跳转到认证详情页面的逻辑
  }

  // 审核认证
  const reviewCertification = (row: any, status: number, reason?: string) => {
    console.log('审核认证:', { id: row.id, status, reason })
    // 这里可以调用审核API
    // 然后刷新表格
    crudTableRef.value?.refreshData()
  }
</script>

<template>
  <ArtCrudTable ref="crudTableRef" :key="componentKey" :config="certificationTableConfig">
    <!-- 用户信息列 -->
    <template #user="{ row }">
      <div class="user-info">
        <div class="user-id">ID: {{ renderUser(row).id }}</div>
        <div class="user-name">{{ renderUser(row).username }}</div>
        <div v-if="renderUser(row).nickname" class="user-nickname">
          ({{ renderUser(row).nickname }})
        </div>
      </div>
    </template>

    <!-- 证件类型列 -->
    <template #type="{ row }">
      <span :class="['type-tag', renderType(row).class]">
        {{ renderType(row).text }}
      </span>
    </template>

    <!-- 认证模式列 -->
    <template #mode="{ row }">
      <span :class="['mode-tag', renderMode(row).class]">
        {{ renderMode(row).text }}
      </span>
    </template>

    <!-- 证件照列 -->
    <template #photos="{ row }">
      <div class="photos-container">
        <div class="photo-item" v-if="renderPhotos(row).photo1">
          <img :src="renderPhotos(row).photo1" alt="正面照" class="photo-thumb" />
          <span class="photo-label">正面</span>
        </div>
        <div class="photo-item" v-if="renderPhotos(row).photo2">
          <img :src="renderPhotos(row).photo2" alt="反面照" class="photo-thumb" />
          <span class="photo-label">反面</span>
        </div>
        <div class="photo-item" v-if="renderPhotos(row).photo3">
          <img :src="renderPhotos(row).photo3" alt="手持照" class="photo-thumb" />
          <span class="photo-label">手持</span>
        </div>
      </div>
    </template>

    <!-- 状态列 -->
    <template #status="{ row }">
      <span :class="['status-tag', renderStatus(row).class]">
        {{ renderStatus(row).text }}
      </span>
    </template>

    <!-- 操作列 -->
    <template #actions="{ row }">
      <div class="flex gap-2">
        <ArtButtonTable
          type="edit"
          @click="crudTableRef?.showDialog('edit', row)"
          :disabled="row.status === 2"
        />
        <ArtButtonTable
          type="delete"
          @click="crudTableRef?.deleteItem(row.id)"
          :disabled="row.status === 2"
        />
        <ArtButtonTable type="view" @click="viewCertificationDetail(row)" />

        <!-- 审核按钮 -->
        <ArtButtonTable v-if="row.status === 1" type="add" @click="reviewCertification(row, 2)" />
        <ArtButtonTable
          v-if="row.status === 1"
          type="delete"
          @click="reviewCertification(row, 3, '审核不通过')"
        />
      </div>
    </template>
  </ArtCrudTable>
</template>

<style lang="scss" scoped>
  .flex {
    display: flex;
  }

  .gap-2 {
    gap: 8px;
  }

  // 用户信息样式
  .user-info {
    .user-id {
      margin-bottom: 2px;
      font-size: 12px;
      color: #999;
    }

    .user-name {
      font-weight: 500;
      color: #333;
    }

    .user-nickname {
      margin-top: 2px;
      font-size: 12px;
      color: #666;
    }
  }

  // 证件类型标签样式
  .type-tag {
    padding: 4px 8px;
    font-size: 12px;
    font-weight: 500;
    border-radius: 4px;

    &.type-id-card {
      color: #1976d2;
      background-color: #e3f2fd;
      border: 1px solid #bbdefb;
    }
  }

  // 认证模式标签样式
  .mode-tag {
    padding: 4px 8px;
    font-size: 12px;
    font-weight: 500;
    border-radius: 4px;

    &.mode-identity {
      color: #7b1fa2;
      background-color: #f3e5f5;
      border: 1px solid #ce93d8;
    }
  }

  // 证件照样式
  .photos-container {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
  }

  .photo-item {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .photo-thumb {
    width: 30px;
    height: 20px;
    object-fit: cover;
    border: 1px solid #ddd;
    border-radius: 2px;
  }

  .photo-label {
    margin-top: 2px;
    font-size: 10px;
    color: #666;
  }

  // 状态标签样式
  .status-tag {
    padding: 4px 8px;
    font-size: 12px;
    font-weight: 500;
    border-radius: 4px;

    &.status-pending {
      color: #ef6c00;
      background-color: #fff3e0;
      border: 1px solid #ffcc02;
    }

    &.status-approved {
      color: #2e7d32;
      background-color: #e8f5e8;
      border: 1px solid #a5d6a7;
    }

    &.status-rejected {
      color: #c62828;
      background-color: #ffebee;
      border: 1px solid #ef9a9a;
    }
  }
</style>
