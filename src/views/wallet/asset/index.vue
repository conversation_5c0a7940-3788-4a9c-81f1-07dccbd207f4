<script setup lang="ts">
  import { ref } from 'vue'
  import ArtCrudTable from '@/components/core/tables/ArtCrudTable.vue'
  import ArtButtonTable from '@/components/core/forms/art-button-table/index.vue'
  import {
    assetTableConfig,
    formatAmount,
    calculateTotalAmount,
    calculateAssetUtilization,
    getPrimaryStatus,
    getAmountStatusClass
  } from './asset'

  defineOptions({ name: 'UserAsset' })

  const crudTableRef = ref()
  const componentKey = ref(Date.now())

  // 用户信息渲染
  const renderUser = (row: any) => {
    if (row.user) {
      return {
        id: row.user.id,
        username: row.user.username,
        nickname: row.user.nickname,
        avatar: row.user.avatar
      }
    }
    return { id: row.user_id, username: '未知用户', nickname: '', avatar: '' }
  }

  // 资产信息渲染
  const renderAsset = (row: any) => {
    if (row.asset) {
      return {
        id: row.asset.id,
        name: row.asset.name,
        symbol: row.asset.symbol,
        icon: row.asset.icon,
        precision: row.asset.precision || 4
      }
    }
    return { id: row.asset_id, name: '未知资产', symbol: 'UNKNOWN', icon: '', precision: 4 }
  }

  // 金额渲染
  const renderAmount = (amount: number | string, type: string, precision?: number) => {
    const formattedAmount = formatAmount(amount, precision)
    const amountClass = getAmountStatusClass(parseFloat(formattedAmount), type)
    return {
      amount: formattedAmount,
      class: amountClass
    }
  }

  // 主资产状态渲染
  const renderPrimaryStatus = (row: any) => {
    const statusText = getPrimaryStatus(row.is_primary)
    const statusClass = row.is_primary ? 'primary-asset' : 'normal-asset'
    return { text: statusText, class: statusClass }
  }

  // 总资产渲染
  const renderTotalAmount = (row: any) => {
    const totalAmount = calculateTotalAmount(row)
    const utilization = calculateAssetUtilization(row)
    return {
      total: totalAmount,
      utilization,
      class: getAmountStatusClass(parseFloat(totalAmount), 'total')
    }
  }

  // 查看资产详情
  const viewAssetDetail = (row: any) => {
    console.log('查看资产详情:', row)
    // 这里可以实现跳转到资产详情页面的逻辑
  }

  // 冻结资产
  const freezeAsset = (row: any, amount?: number) => {
    console.log('冻结资产:', row.id, amount)
    // 调用API冻结资产
    crudTableRef.value?.refreshData()
  }

  // 解冻资产
  const unfreezeAsset = (row: any, amount?: number) => {
    console.log('解冻资产:', row.id, amount)
    // 调用API解冻资产
    crudTableRef.value?.refreshData()
  }

  // 设为主资产
  const setPrimaryAsset = (row: any) => {
    console.log('设为主资产:', row.user_id, row.asset_id)
    // 调用API设置主资产
    crudTableRef.value?.refreshData()
  }

  // 取消主资产
  const unsetPrimaryAsset = (row: any) => {
    console.log('取消主资产:', row.user_id, row.asset_id)
    // 调用API取消主资产
    crudTableRef.value?.refreshData()
  }

  // 同步资产
  const syncAsset = (row: any) => {
    console.log('同步资产:', row.id)
    // 调用API同步资产
    crudTableRef.value?.refreshData()
  }

  // 验证资产
  const verifyAsset = (row: any) => {
    console.log('验证资产:', row.id)
    // 调用API验证资产
    crudTableRef.value?.refreshData()
  }

  // 重新计算资产
  const recalculateAsset = (row: any) => {
    console.log('重新计算资产:', row.id)
    // 调用API重新计算资产
    crudTableRef.value?.refreshData()
  }

  // 查看资产历史
  const viewAssetHistory = (row: any) => {
    console.log('查看资产历史:', row.id)
    // 这里可以实现跳转到资产历史页面的逻辑
  }

  // 余额操作
  const balanceOperation = (row: any, type: string) => {
    console.log('余额操作:', row.id, type)
    // 这里可以打开余额操作对话框
  }

  // 处理下拉菜单命令
  const handleDropdownCommand = (command: string, row: any) => {
    switch (command) {
      case 'recalculate':
        recalculateAsset(row)
        break
      case 'history':
        viewAssetHistory(row)
        break
      case 'balance-operation':
        balanceOperation(row, 'adjust')
        break
      case 'unset-primary':
        unsetPrimaryAsset(row)
        break
      default:
        console.log('未知命令:', command)
    }
  }
</script>

<template>
  <ArtCrudTable ref="crudTableRef" :key="componentKey" :config="assetTableConfig">
    <!-- 用户信息列 -->
    <template #user="{ row }">
      <div class="user-info">
        <div class="user-avatar" v-if="renderUser(row).avatar">
          <img :src="renderUser(row).avatar" :alt="renderUser(row).username" class="avatar-img" />
        </div>
        <div class="user-details">
          <div class="user-id">ID: {{ renderUser(row).id }}</div>
          <div class="user-name">{{ renderUser(row).username }}</div>
          <div v-if="renderUser(row).nickname" class="user-nickname">
            ({{ renderUser(row).nickname }})
          </div>
        </div>
      </div>
    </template>

    <!-- 资产信息列 -->
    <template #asset="{ row }">
      <div class="asset-info">
        <div class="asset-icon" v-if="renderAsset(row).icon">
          <img :src="renderAsset(row).icon" :alt="renderAsset(row).symbol" class="asset-icon-img" />
        </div>
        <div class="asset-details">
          <div class="asset-symbol">{{ renderAsset(row).symbol }}</div>
          <div class="asset-name">{{ renderAsset(row).name }}</div>
          <div class="asset-id">ID: {{ renderAsset(row).id }}</div>
        </div>
      </div>
    </template>

    <!-- 可用余额列 -->
    <template #availableAmount="{ row }">
      <div class="amount-container">
        <span
          :class="[
            'amount-value',
            renderAmount(row.available_amount, 'available', renderAsset(row).precision).class
          ]"
        >
          {{ renderAmount(row.available_amount, 'available', renderAsset(row).precision).amount }}
        </span>
        <span class="amount-symbol">{{ renderAsset(row).symbol }}</span>
      </div>
    </template>

    <!-- 冻结余额列 -->
    <template #frozenAmount="{ row }">
      <div class="amount-container">
        <span
          :class="[
            'amount-value',
            renderAmount(row.frozen_amount, 'frozen', renderAsset(row).precision).class
          ]"
        >
          {{ renderAmount(row.frozen_amount, 'frozen', renderAsset(row).precision).amount }}
        </span>
        <span class="amount-symbol">{{ renderAsset(row).symbol }}</span>
      </div>
    </template>

    <!-- 收益余额列 -->
    <template #profitAmount="{ row }">
      <div class="amount-container">
        <span
          :class="[
            'amount-value',
            renderAmount(row.profit_amount, 'profit', renderAsset(row).precision).class
          ]"
        >
          {{ renderAmount(row.profit_amount, 'profit', renderAsset(row).precision).amount }}
        </span>
        <span class="amount-symbol">{{ renderAsset(row).symbol }}</span>
      </div>
    </template>

    <!-- 充值总额列 -->
    <template #depositAmount="{ row }">
      <div class="amount-container">
        <span
          :class="[
            'amount-value',
            renderAmount(row.sum_deposit_amount, 'deposit', renderAsset(row).precision).class
          ]"
        >
          {{ renderAmount(row.sum_deposit_amount, 'deposit', renderAsset(row).precision).amount }}
        </span>
        <span class="amount-symbol">{{ renderAsset(row).symbol }}</span>
      </div>
    </template>

    <!-- 提现总额列 -->
    <template #withdrawAmount="{ row }">
      <div class="amount-container">
        <span
          :class="[
            'amount-value',
            renderAmount(row.sum_withdraw_amount, 'withdraw', renderAsset(row).precision).class
          ]"
        >
          {{ renderAmount(row.sum_withdraw_amount, 'withdraw', renderAsset(row).precision).amount }}
        </span>
        <span class="amount-symbol">{{ renderAsset(row).symbol }}</span>
      </div>
    </template>

    <!-- 总资产列 -->
    <template #totalAmount="{ row }">
      <div class="total-amount-container">
        <div class="total-amount">
          <span :class="['amount-value', renderTotalAmount(row).class]">
            {{ renderTotalAmount(row).total }}
          </span>
          <span class="amount-symbol">{{ renderAsset(row).symbol }}</span>
        </div>
        <div class="utilization"> 利用率: {{ renderTotalAmount(row).utilization }} </div>
      </div>
    </template>

    <!-- 主资产状态列 -->
    <template #primaryStatus="{ row }">
      <span :class="['primary-tag', renderPrimaryStatus(row).class]">
        {{ renderPrimaryStatus(row).text }}
      </span>
    </template>

    <!-- 操作列 -->
    <template #actions="{ row }">
      <div class="flex gap-2">
        <ArtButtonTable type="edit" @click="crudTableRef?.showDialog('edit', row)" />
        <ArtButtonTable
          type="delete"
          @click="crudTableRef?.deleteItem(row.id)"
          :disabled="
            row.is_primary ||
            parseFloat(row.available_amount) +
              parseFloat(row.frozen_amount) +
              parseFloat(row.profit_amount) >
              0
          "
        />
        <ArtButtonTable type="view" @click="viewAssetDetail(row)" />

        <!-- 冻结/解冻按钮 -->
        <ArtButtonTable
          v-if="parseFloat(row.available_amount) > 0"
          type="more"
          @click="freezeAsset(row)"
        />
        <ArtButtonTable
          v-if="parseFloat(row.frozen_amount) > 0"
          type="add"
          @click="unfreezeAsset(row)"
        />

        <!-- 主资产操作按钮 -->
        <ArtButtonTable v-if="!row.is_primary" type="more" @click="setPrimaryAsset(row)" />

        <!-- 同步和验证按钮 -->
        <ArtButtonTable type="more" @click="syncAsset(row)" />
        <ArtButtonTable type="more" @click="verifyAsset(row)" />

        <!-- 更多操作下拉菜单 -->
        <el-dropdown trigger="click" @command="(command) => handleDropdownCommand(command, row)">
          <ArtButtonTable type="more" />
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="recalculate">重新计算</el-dropdown-item>
              <el-dropdown-item command="history">查看历史</el-dropdown-item>
              <el-dropdown-item command="balance-operation">余额操作</el-dropdown-item>
              <el-dropdown-item v-if="row.is_primary" command="unset-primary" divided
                >取消主资产</el-dropdown-item
              >
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </template>
  </ArtCrudTable>
</template>

<style lang="scss" scoped>
  .flex {
    display: flex;
  }

  .gap-2 {
    gap: 8px;
  }

  // 用户信息样式
  .user-info {
    display: flex;
    gap: 8px;
    align-items: center;

    .user-avatar {
      .avatar-img {
        width: 32px;
        height: 32px;
        object-fit: cover;
        border: 1px solid #e0e0e0;
        border-radius: 50%;
      }
    }

    .user-details {
      .user-id {
        margin-bottom: 2px;
        font-size: 12px;
        color: #999;
      }

      .user-name {
        font-weight: 500;
        color: #333;
      }

      .user-nickname {
        margin-top: 2px;
        font-size: 12px;
        color: #666;
      }
    }
  }

  // 资产信息样式
  .asset-info {
    display: flex;
    gap: 8px;
    align-items: center;

    .asset-icon {
      .asset-icon-img {
        width: 28px;
        height: 28px;
        object-fit: cover;
        border-radius: 50%;
      }
    }

    .asset-details {
      .asset-symbol {
        font-size: 14px;
        font-weight: 600;
        color: #333;
      }

      .asset-name {
        margin-top: 2px;
        font-size: 12px;
        color: #666;
      }

      .asset-id {
        margin-top: 2px;
        font-size: 11px;
        color: #999;
      }
    }
  }

  // 金额容器样式
  .amount-container {
    display: flex;
    flex-direction: column;
    align-items: flex-end;

    .amount-value {
      font-family: Monaco, Menlo, monospace;
      font-weight: 500;

      &.amount-positive {
        color: #52c41a;
      }

      &.amount-negative {
        color: #ff4d4f;
      }

      &.amount-zero {
        color: #8c8c8c;
      }

      &.available-positive {
        color: #52c41a;
      }

      &.frozen-positive {
        color: #faad14;
      }

      &.profit-positive {
        color: #1890ff;
      }

      &.profit-negative {
        color: #ff4d4f;
      }

      &.deposit-positive {
        color: #52c41a;
      }

      &.withdraw-positive {
        color: #ff4d4f;
      }

      &.total-positive {
        font-weight: 600;
        color: #52c41a;
      }
    }

    .amount-symbol {
      margin-top: 2px;
      font-size: 11px;
      color: #8c8c8c;
    }
  }

  // 总资产容器样式
  .total-amount-container {
    display: flex;
    flex-direction: column;
    align-items: flex-end;

    .total-amount {
      display: flex;
      flex-direction: column;
      align-items: flex-end;

      .amount-value {
        font-family: Monaco, Menlo, monospace;
        font-size: 14px;
        font-weight: 600;
      }

      .amount-symbol {
        margin-top: 2px;
        font-size: 11px;
        color: #8c8c8c;
      }
    }

    .utilization {
      padding: 2px 6px;
      margin-top: 4px;
      font-size: 11px;
      color: #666;
      background-color: #f5f5f5;
      border-radius: 10px;
    }
  }

  // 主资产标签样式
  .primary-tag {
    padding: 4px 8px;
    font-size: 12px;
    font-weight: 500;
    border-radius: 4px;

    &.primary-asset {
      color: #722ed1;
      background-color: #f9f0ff;
      border: 1px solid #d3adf7;
    }

    &.normal-asset {
      color: #8c8c8c;
      background-color: #f5f5f5;
      border: 1px solid #d9d9d9;
    }
  }

  // 特殊金额状态样式
  .amount-positive {
    &.available-positive {
      padding-left: 8px;
      background: linear-gradient(135deg, #f6ffed 0%, #f6ffed 100%);
      border-left: 3px solid #52c41a;
    }

    &.profit-positive {
      padding-left: 8px;
      background: linear-gradient(135deg, #e6f7ff 0%, #e6f7ff 100%);
      border-left: 3px solid #1890ff;
    }
  }

  .amount-negative {
    &.profit-negative {
      padding-left: 8px;
      background: linear-gradient(135deg, #fff2f0 0%, #fff2f0 100%);
      border-left: 3px solid #ff4d4f;
    }
  }

  // 高亮重要数据
  .amount-container:hover {
    background-color: #fafafa;
    border-radius: 4px;
    transition: all 0.3s ease;
  }

  .total-amount-container:hover {
    background-color: #f0f9ff;
    border-radius: 4px;
    transition: all 0.3s ease;
  }

  /* 已有 .flex 定义，此处不再重复定义，若需换行可在需要处单独添加样式类 */

  // 响应式设计
  @media (width <= 768px) {
    .user-info,
    .asset-info {
      flex-direction: column;
      gap: 4px;
      align-items: flex-start;
    }

    .amount-container {
      align-items: flex-start;
    }

    .total-amount-container {
      align-items: flex-start;
    }
  }
</style>
