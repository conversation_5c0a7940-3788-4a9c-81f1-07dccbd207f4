import { SystemAssetService } from '@/api/systemAsset'
import type {
  CrudTableConfig,
  ExportConfig,
  PermissionConfig,
  SecurityConfig,
  PerformanceConfig,
  ResponsiveConfig
} from '@/components/core/tables/ArtCrudTable.vue'
import { filterEmptyParams } from '@/utils'

// 包装API服务
const createApiService = () => ({
  getList: (params: any) => SystemAssetService.getList(filterEmptyParams(params)),
  delete: async (id: string | number) => {
    await SystemAssetService.remove(Number(id))
    return { code: 0 }
  },
  batchDelete: async (ids: Array<string | number>) => {
    await SystemAssetService.batchRemove(ids.map((v) => Number(v)))
    return { code: 0 }
  },
  update: async (id: string | number, data: any) => {
    await SystemAssetService.update(Number(id), data)
    return { code: 0 }
  },
  create: (data: any) => SystemAssetService.create(data)
})

// 选项
export const assetTypeOptions = [
  { label: '平台币', value: 1 },
  { label: '加密货币', value: 2 }
]

export const statusOptions = [
  { label: '启用', value: 1 },
  { label: '禁用', value: 2 }
]

// 导出配置
const exportConfig: ExportConfig = {
  enabled: true,
  filename: '资产数据导出',
  columns: [
    'id',
    'name',
    'subtitle_field',
    'name_native',
    'symbol',
    'type',
    'rate',
    'sort',
    'status',
    'decimals',
    'description_field',
    'created_at'
  ],
  formats: ['excel', 'csv'],
  maxRows: 10000
}

// 权限/响应/安全/性能
const permissionConfig: PermissionConfig = {
  create: true,
  update: true,
  delete: true,
  export: true
}

const responsiveConfig: ResponsiveConfig = {
  mobile: {
    hideColumns: ['subtitle_field', 'name_native', 'description_field', 'created_at'],
    cardView: false,
    breakpoint: 768
  }
}

const securityConfig: SecurityConfig = {
  enableXssProtection: true,
  allowedTags: ['p', 'br', 'strong', 'em'],
  allowedAttributes: { div: ['class'], span: ['class'] }
}

const performanceConfig: PerformanceConfig = {
  enableCache: true,
  cacheTime: 3 * 60 * 1000,
  debounceTime: 300,
  maxCacheSize: 100,
  lazyLoad: true,
  virtualScroll: { enabled: false, itemHeight: 60, buffer: 10 }
}

export const assetTableConfig: CrudTableConfig = {
  title: '资产管理',
  addButtonText: '新增资产',
  deleteButtonText: '删除资产',
  deleteConfirmText: '确定要删除该资产吗？',
  batchDeleteConfirmText: '确定要删除选中的 {count} 个资产吗？',
  emptyHeight: '500px',

  initialSearchState: {},
  searchFormItems: [
    { key: 'name', label: '名称', type: 'input' },
    { key: 'symbol', label: '标识符号', type: 'input' },
    { key: 'type', label: '类型', type: 'select', options: assetTypeOptions },
    { key: 'status', label: '状态', type: 'select', options: statusOptions },
    { key: 'created_at', label: '创建时间', type: 'datetimerange' }
  ],

  tableColumns: [
    { prop: 'id', label: 'ID', width: 80, fixed: 'left', sortable: true, align: 'center' },
    { prop: 'icon', label: '图标', width: 80, slot: 'icon', align: 'center', columnType: 'image' },
    { prop: 'name', label: '名称', minWidth: 140 },
    { prop: 'subtitle', label: '副标题', minWidth: 160 },
    { prop: 'name_native', label: '货币名称', minWidth: 140 },
    { prop: 'symbol', label: '标识符号', width: 120, align: 'center' },
    { prop: 'type', label: '类型', width: 120, slot: 'type', align: 'center' },
    { prop: 'rate', label: '汇率', width: 120, align: 'right' },
    { prop: 'sort', label: '排序', width: 90, align: 'center', sortable: true },
    { prop: 'decimals', label: '小数位数', width: 120, align: 'center' },
    { prop: 'description', label: '描述', minWidth: 160 },
    {
      prop: 'status',
      label: '状态',
      width: 100,
      slot: 'status',
      align: 'center',
      columnType: 'toggle'
    },
    { prop: 'created_at', label: '创建时间', width: 160, align: 'center', columnType: 'date' },
    { prop: 'actions', label: '操作', width: 220, fixed: 'right', slot: 'actions', align: 'center' }
  ],

  apiService: createApiService(),
  paginationKey: { current: 'page', size: 'pageSize' },

  dialog: {
    formItems: [
      { prop: 'icon', label: '图标', type: 'upload', span: 24 },
      {
        prop: 'name',
        label: '名称',
        type: 'input',
        rules: [{ required: true, message: '必填', trigger: 'blur' }],
        span: 12
      },
      { prop: 'subtitle_field', label: '副标题字段', type: 'input', span: 12 },
      { prop: 'name_native', label: '货币名称', type: 'input', span: 12 },
      {
        prop: 'symbol',
        label: '标识符号',
        type: 'input',
        rules: [{ required: true, message: '必填', trigger: 'blur' }],
        span: 12
      },
      {
        prop: 'type',
        label: '类型',
        type: 'select',
        options: assetTypeOptions,
        rules: [{ required: true, message: '必选', trigger: 'change' }],
        span: 12
      },
      { prop: 'rate', label: '汇率', type: 'number', span: 12, config: { precision: 6 } },
      { prop: 'sort', label: '排序', type: 'number', span: 12, config: { precision: 0 } },
      {
        prop: 'status',
        label: '状态',
        type: 'select',
        options: statusOptions,
        rules: [{ required: true, message: '必选', trigger: 'change' }],
        span: 12
      },
      { prop: 'decimals', label: '小数位数', type: 'number', span: 12, config: { precision: 0 } },
      { prop: 'description_field', label: '描述字段', type: 'input', span: 24 },
      // AssetData JSON 的简化输入
      {
        prop: 'data.is_deposit',
        label: '开启充值',
        type: 'select',
        options: [
          { label: '是', value: true },
          { label: '否', value: false }
        ],
        span: 12
      },
      {
        prop: 'data.is_withdraw',
        label: '开启提现',
        type: 'select',
        options: [
          { label: '是', value: true },
          { label: '否', value: false }
        ],
        span: 12
      },
      {
        prop: 'data.is_transfer',
        label: '开启转账',
        type: 'select',
        options: [
          { label: '是', value: true },
          { label: '否', value: false }
        ],
        span: 12
      },
      {
        prop: 'data.is_swap',
        label: '开启兑换',
        type: 'select',
        options: [
          { label: '是', value: true },
          { label: '否', value: false }
        ],
        span: 12
      }
    ]
  },

  export: exportConfig,
  permissions: permissionConfig,
  responsive: responsiveConfig,
  security: securityConfig,
  performance: performanceConfig,
  hasStatusSwitch: true,
  statusField: 'status',
  statusActiveValue: 1,
  statusInactiveValue: 2,
  allowBatchDelete: true
}
