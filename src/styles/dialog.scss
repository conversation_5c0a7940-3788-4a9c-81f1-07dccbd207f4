/**
 * 对话框滚动条闪烁问题解决方案
 * 解决 Element Plus Dialog 组件打开/关闭时页面滚动条消失导致的页面闪烁问题
 */

/* 方案1：确保页面始终有滚动条槽位 */
html {
  /* 始终显示垂直滚动条，避免内容宽度变化 */
  overflow-y: scroll;
}

body {
  /* 确保 body 也有稳定的滚动行为 */
  overflow-y: auto;
}

/* 方案2：现代浏览器的 scrollbar-gutter 属性支持 */
@supports (scrollbar-gutter: stable) {
  html {
    /* 为滚动条预留稳定的空间，即使没有滚动条也不会变化布局 */
    scrollbar-gutter: stable;
    overflow-y: auto; /* 配合 scrollbar-gutter 使用 */
  }
}

/* 对话框遮罩层优化 */
.art-dialog-modal {
  &.el-overlay {
    /* 确保遮罩层不会影响页面布局 */
    backdrop-filter: blur(1px);

    /* 平滑的背景过渡 */
    transition: all 0.3s ease;
  }
}

/* Element Plus Dialog 优化 */
.el-dialog {
  /* 对话框打开动画优化 */
  &.el-dialog--center {
    /* 居中对话框的额外样式 */
    .el-dialog__header {
      padding-bottom: 16px;
      text-align: center;
      border-bottom: 1px solid var(--el-border-color-light);
    }
  }

  /* 表单内容区域优化 */
  .el-dialog__body {
    /* 确保内容区域不会因为滚动条变化而闪烁 */
    min-height: 200px;

    /* 如果对话框内容过长，显示内部滚动条而不是页面滚动条 */
    max-height: 70vh;
    overflow-y: auto;
  }
}

/* 修复某些情况下的布局偏移问题 */
.el-popup-parent--hidden {
  /* Element Plus 添加的类，用于隐藏页面滚动 */

  /* 确保这个状态下页面宽度不变 */
  .main-container,
  .app-main {
    /* 如果你的项目有这些类，确保它们的宽度稳定 */
    width: calc(100% - var(--scrollbar-width, 0px));
  }
}

/* 滚动条宽度检测和补偿 */
:root {
  /* 动态计算滚动条宽度（如果需要精确控制） */
  --scrollbar-width: 17px; /* 大多数桌面浏览器的默认滚动条宽度 */
}

/* 针对 webkit 浏览器的滚动条样式优化 */
@media screen and (min-device-pixel-ratio: 0) {
  :root {
    --scrollbar-width: 17px;
  }

  /* 自定义滚动条样式，确保视觉一致性 */
  ::-webkit-scrollbar {
    width: var(--scrollbar-width);
    background-color: var(--el-fill-color-light);
  }

  ::-webkit-scrollbar-thumb {
    background-color: var(--el-border-color);
    border-radius: 4px;

    &:hover {
      background-color: var(--el-border-color-dark);
    }
  }
}

/* 响应式优化 */
@media (width <= 768px) {
  /* 移动端通常没有滚动条，不需要预留空间 */
  html {
    overflow-y: auto;
    scrollbar-gutter: auto;
  }

  .el-dialog {
    display: flex;
    flex-direction: column;

    /* 移动端对话框优化 */
    width: 95% !important;
    max-height: 85vh;
    margin: 8vh auto 2vh !important;

    .el-dialog__header {
      flex-shrink: 0;
      padding: 16px 20px 12px;
      border-bottom: 1px solid var(--el-border-color-light);

      .el-dialog__title {
        font-size: 16px;
        font-weight: 600;
        line-height: 1.5;
        word-break: break-all;
      }

      .el-dialog__headerbtn {
        top: 14px;
        right: 16px;
        width: 32px;
        height: 32px;

        .el-dialog__close {
          font-size: 18px;
        }
      }
    }

    .el-dialog__body {
      flex: 1;
      padding: 16px 16px 20px;
      overflow-y: auto;

      /* 使用-webkit-overflow-scrolling以在iOS上获得平滑滚动 */
      -webkit-overflow-scrolling: touch;

      /* 表单优化 */
      .el-form {
        .el-form-item {
          margin-bottom: 18px;

          .el-form-item__label {
            padding-right: 8px;
            font-size: 14px;
            line-height: 1.5;
            word-break: break-all;

            /* 移动端标签可能需要换行 */
            white-space: normal;
          }

          .el-form-item__content {
            /* 确保所有输入控件在移动端全宽 */
            .el-input,
            .el-select,
            .el-input-number,
            .el-textarea,
            .el-date-picker,
            .el-time-picker {
              width: 100% !important;

              .el-input__wrapper,
              .el-textarea__inner,
              .el-select__wrapper {
                /* 增加触摸目标大小 */
                min-height: 44px;

                /* 防止iOS自动缩放 */
                font-size: 16px;
              }
            }

            /* 数字输入框特殊处理 */
            .el-input-number {
              .el-input__wrapper {
                width: 100%;
              }

              .el-input-number__decrease,
              .el-input-number__increase {
                /* 增大移动端按钮点击区域 */
                width: 36px;
                height: 44px;
              }
            }

            /* 选择器优化 */
            .el-select {
              .el-select__wrapper {
                .el-select__suffix {
                  /* 优化下拉箭头点击区域 */
                  width: 36px;
                }
              }
            }
          }
        }

        /* 栅格系统优化 */
        .el-row {
          .el-col {
            padding-right: 8px;

            /* 移动端减少栅格间距 */
            padding-left: 8px;
          }
        }
      }
    }

    .el-dialog__footer {
      flex-shrink: 0;
      padding: 12px 16px 16px;
      border-top: 1px solid var(--el-border-color-light);

      /* 移动端按钮优化 */
      .dialog-footer {
        display: flex;
        flex-direction: column-reverse;
        gap: 12px;

        .el-button {
          width: 100%;

          /* 增加触摸目标大小 */
          height: 44px;
          margin: 0;
          font-size: 16px;

          /* 主要按钮突出显示 */
          &.el-button--primary {
            order: -1;
          }
        }
      }
    }
  }

  /* 对话框遮罩优化 */
  .el-overlay {
    /* 确保遮罩在移动端完全覆盖 */
    padding: 0;

    .el-overlay-dialog {
      display: flex;
      align-items: flex-start;
      justify-content: center;
      padding: 0 8px;
    }
  }
}

/* 平板端优化 (768px - 1024px) */
@media (width >= 768px) and (width <= 1024px) {
  .el-dialog {
    width: 85% !important;
    max-width: 600px;
    margin: 6vh auto !important;

    .el-dialog__body {
      max-height: 70vh;
      padding: 20px 24px;

      .el-form {
        .el-form-item {
          margin-bottom: 20px;

          .el-form-item__content {
            .el-input,
            .el-select,
            .el-input-number,
            .el-textarea {
              .el-input__wrapper,
              .el-textarea__inner {
                min-height: 40px;
              }
            }
          }
        }
      }
    }

    .el-dialog__footer {
      padding: 16px 24px 20px;

      .dialog-footer {
        .el-button {
          min-width: 88px;
          height: 40px;
        }
      }
    }
  }
}

/* 大屏幕优化 */
@media (width >= 1200px) {
  .el-dialog {
    .el-dialog__body {
      /* 大屏幕上可以显示更多内容 */
      max-height: 75vh;
    }
  }
}

/* 横屏手机优化 */
@media (width <= 768px) and (orientation: landscape) {
  .el-dialog {
    max-height: 95vh;
    margin: 2vh auto !important;

    .el-dialog__body {
      max-height: 70vh;
    }
  }
}

/* 超小屏幕设备优化 (小于 375px) */
@media (width <= 374px) {
  .el-dialog {
    width: 98% !important;
    margin: 4vh auto 1vh !important;

    .el-dialog__header {
      padding: 12px 16px 8px;

      .el-dialog__title {
        padding-right: 40px;
        font-size: 15px;
      }
    }

    .el-dialog__body {
      padding: 12px 12px 16px;

      .el-form {
        .el-form-item {
          margin-bottom: 16px;

          .el-form-item__label {
            font-size: 13px;
          }
        }
      }
    }

    .el-dialog__footer {
      padding: 8px 12px 12px;
    }
  }
}
