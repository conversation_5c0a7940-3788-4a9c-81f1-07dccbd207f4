import { UserDepositService } from '@/api/walletDeposit'
import type {
  CrudTableConfig,
  BatchAction,
  ExportConfig,
  PermissionConfig,
  SecurityConfig,
  PerformanceConfig,
  ResponsiveConfig
} from '@/components/core/tables/ArtCrudTable.vue'

// 过滤空值参数
const filterEmptyParams = (params: Record<string, any>) => {
  const filtered: Record<string, any> = {}
  Object.keys(params).forEach((key) => {
    const value = params[key]
    if (value !== '' && value !== null && value !== undefined) {
      filtered[key] = value
    }
  })
  return filtered
}

// 包装API服务
const createApiService = () => ({
  getList: (params: any) => {
    const filtered = filterEmptyParams(params)
    return UserDepositService.getList(filtered)
  },
  delete: async (id: string | number) => {
    await UserDepositService.remove(Number(id))
    return { code: 0 }
  },
  batchDelete: async (ids: Array<string | number>) => {
    await UserDepositService.batchRemove(ids.map((v) => Number(v)))
    return { code: 0 }
  },
  update: async (id: string | number, data: any) => {
    await UserDepositService.update(Number(id), data)
    return { code: 0 }
  },
  create: (data: any) => UserDepositService.create(data)
})

// 常量与选项
export const walletTypeOptions = [{ label: '充值', value: 1 }]
export const walletStatusOptions = [
  { label: '待处理', value: 1 },
  { label: '已完成', value: 2 },
  { label: '已取消', value: 3 },
  { label: '已失败', value: 4 }
]

export const getStatusText = (status: number) => {
  const map: Record<number, string> = { 1: '待处理', 2: '已完成', 3: '已取消', 4: '已失败' }
  return map[status] || '未知'
}

export const getStatusClass = (status: number) => {
  const map: Record<number, string> = {
    1: 'status-pending',
    2: 'status-success',
    3: 'status-cancel',
    4: 'status-failed'
  }
  return map[status] || 'status-unknown'
}

export const formatAmount = (amount: number | string, precision: number = 4) => {
  if (!amount && amount !== 0) return '0.0000'
  const num = typeof amount === 'string' ? parseFloat(amount) : amount
  return num.toFixed(precision)
}

export const calcArrivalAmount = (amount: number, fixedFee: number, rateFee: number) => {
  const totalFee = fixedFee + (amount * rateFee) / 100
  return Math.max(0, amount - totalFee)
}

// 批量操作
const batchActions: BatchAction[] = [
  {
    label: '批量完成',
    key: 'batchComplete',
    type: 'success',
    icon: 'CircleCheck',
    confirm: '确认完成选中的 {count} 条充值吗？',
    handler: async (rows) => {
      await UserDepositService.batchUpdateStatus(
        rows.map((r: any) => r.id as number),
        UserDepositService.STATUS_SUCCESS,
        '批量完成'
      )
    }
  },
  {
    label: '批量取消',
    key: 'batchCancel',
    type: 'warning',
    icon: 'Close',
    confirm: '确认取消选中的 {count} 条充值吗？',
    handler: async (rows) => {
      await UserDepositService.batchUpdateStatus(
        rows.map((r: any) => r.id as number),
        UserDepositService.STATUS_CANCEL,
        '批量取消'
      )
    }
  },
  {
    label: '批量设为失败',
    key: 'batchFail',
    type: 'danger',
    icon: 'CloseBold',
    confirm: '确认设为失败选中的 {count} 条充值吗？',
    handler: async (rows) => {
      await UserDepositService.batchUpdateStatus(
        rows.map((r: any) => r.id as number),
        UserDepositService.STATUS_FAILED,
        '批量设为失败'
      )
    }
  },
  {
    label: '导出数据',
    key: 'export',
    type: 'info',
    icon: 'Download',
    handler: async (rows) => {
      const params = rows.length ? { ids: rows.map((r: any) => r.id) } : {}
      await UserDepositService.exportList(params)
    }
  }
]

// 导出配置
const exportConfig: ExportConfig = {
  enabled: true,
  filename: '用户充值数据导出',
  columns: [
    'id',
    'user_id',
    'asset_id',
    'source_id',
    'order_sn',
    'amount',
    'arrival_amount',
    'fixed_fee',
    'rate_fee',
    'status',
    'proof',
    'created_at'
  ],
  formats: ['excel', 'csv'],
  maxRows: 10000
}

// 权限/响应/安全/性能
const permissionConfig: PermissionConfig = {
  create: true,
  update: true,
  delete: true,
  export: true
}

const responsiveConfig: ResponsiveConfig = {
  mobile: {
    hideColumns: ['source_id', 'proof', 'created_at'],
    cardView: false,
    breakpoint: 768
  }
}

const securityConfig: SecurityConfig = {
  enableXssProtection: true,
  allowedTags: ['p', 'br', 'strong', 'em'],
  allowedAttributes: { div: ['class'], span: ['class'] }
}

const performanceConfig: PerformanceConfig = {
  enableCache: true,
  cacheTime: 3 * 60 * 1000,
  debounceTime: 300,
  maxCacheSize: 100,
  lazyLoad: true,
  virtualScroll: { enabled: false, itemHeight: 60, buffer: 10 }
}

// 配置主体
export const depositTableConfig: CrudTableConfig = {
  title: '用户充值',
  addButtonText: '新增充值',
  deleteButtonText: '删除充值',
  deleteConfirmText: '确定要删除该充值记录吗？',
  batchDeleteConfirmText: '确定要删除选中的 {count} 条充值记录吗？',
  emptyHeight: '500px',

  initialSearchState: {},
  searchFormItems: [
    { key: 'user_id', label: '用户ID', type: 'input' },
    { key: 'asset_id', label: '资产ID', type: 'select', options: [] },
    { key: 'source_id', label: '来源ID', type: 'input' },
    { key: 'order_sn', label: '订单号', type: 'input' },
    { key: 'status', label: '状态', type: 'select', options: walletStatusOptions },
    { key: 'created_at', label: '创建时间', type: 'datetimerange' }
  ],

  tableColumns: [
    { prop: 'id', label: 'ID', width: 80, fixed: 'left', sortable: true, align: 'center' },
    { prop: 'user', label: '用户', minWidth: 150, slot: 'user' },
    { prop: 'asset', label: '资产', minWidth: 120, slot: 'asset' },
    { prop: 'source_id', label: '来源ID', width: 100, align: 'center' },
    { prop: 'order_sn', label: '订单号', minWidth: 160 },
    {
      prop: 'amount',
      label: '金额',
      minWidth: 120,
      slot: 'amount',
      align: 'right',
      sortable: true
    },
    {
      prop: 'arrival_amount',
      label: '到账金额',
      minWidth: 120,
      slot: 'arrivalAmount',
      align: 'right',
      sortable: true
    },
    { prop: 'fixed_fee', label: '固定手续费', minWidth: 120, slot: 'fixedFee', align: 'right' },
    { prop: 'rate_fee', label: '手续费(%)', width: 110, align: 'center' },
    { prop: 'status', label: '状态', width: 100, slot: 'status', align: 'center' },
    { prop: 'proof', label: '凭证', minWidth: 160, slot: 'proof' },
    { prop: 'created_at', label: '创建时间', width: 160, align: 'center' },
    { prop: 'actions', label: '操作', width: 220, fixed: 'right', slot: 'actions', align: 'center' }
  ],

  apiService: createApiService(),
  paginationKey: { current: 'page', size: 'page_size' },

  dialog: {
    formItems: [
      {
        prop: 'user_id',
        label: '用户ID',
        type: 'input',
        rules: [{ required: true, message: '必填', trigger: 'blur' }],
        span: 12
      },
      {
        prop: 'asset_id',
        label: '资产ID',
        type: 'select',
        options: [],
        rules: [{ required: true, message: '必选', trigger: 'change' }],
        span: 12
      },
      {
        prop: 'source_id',
        label: '来源ID',
        type: 'input',
        rules: [{ required: true, message: '必填', trigger: 'blur' }],
        span: 12
      },
      {
        prop: 'order_sn',
        label: '订单号',
        type: 'input',
        rules: [{ required: true, message: '必填', trigger: 'blur' }],
        span: 12
      },
      {
        prop: 'amount',
        label: '金额',
        type: 'number',
        rules: [{ required: true, message: '必填', trigger: 'blur' }],
        span: 12,
        config: { precision: 4 }
      },
      {
        prop: 'fixed_fee',
        label: '固定手续费',
        type: 'number',
        span: 12,
        config: { precision: 4 }
      },
      { prop: 'rate_fee', label: '手续费(%)', type: 'number', span: 12, config: { precision: 3 } },
      {
        prop: 'status',
        label: '状态',
        type: 'select',
        options: walletStatusOptions,
        rules: [{ required: true, message: '必选', trigger: 'change' }],
        span: 12
      },
      { prop: 'proof', label: '凭证', type: 'input', span: 24 },
      { prop: 'data', label: '数据', type: 'textarea', span: 24, config: { rows: 3 } }
    ]
  },

  batchActions,
  export: exportConfig,
  permissions: permissionConfig,
  responsive: responsiveConfig,
  security: securityConfig,
  performance: performanceConfig,
  hasStatusSwitch: false,
  allowBatchDelete: true
}
