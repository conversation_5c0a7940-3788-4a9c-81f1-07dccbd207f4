import { UserBillService } from '@/api/walletBill'
import type {
  CrudTableConfig,
  BatchAction,
  ExportConfig,
  PermissionConfig,
  SecurityConfig,
  PerformanceConfig,
  ResponsiveConfig
} from '@/components/core/tables/ArtCrudTable.vue'

// 过滤空值参数的工具函数
const filterEmptyParams = (params: Record<string, any>) => {
  const filtered: Record<string, any> = {}
  Object.keys(params).forEach((key) => {
    const value = params[key]
    // 只保留非空值：非空字符串、非null、非undefined、数字0、布尔值false
    if (value !== '' && value !== null && value !== undefined) {
      filtered[key] = value
    }
  })
  return filtered
}

// 包装API服务，添加参数过滤
const createBillApiService = () => ({
  getList: (params: any) => {
    // 过滤空值参数，解决URL中出现空值的问题
    const filteredParams = filterEmptyParams(params)
    console.log('UserBill API - 过滤后的请求参数:', filteredParams)
    return UserBillService.getUserBillList(filteredParams)
  },
  delete: (id: any) => UserBillService.deleteUserBill(id),
  batchDelete: (ids: any[]) => UserBillService.batchDeleteUserBills(ids),
  update: (id: any, data: any) => UserBillService.updateUserBill(id, data),
  create: (data: any) => UserBillService.createUserBill(data)
})

// 账单类型选项
export const billTypeOptions = [
  // 收入类型（正数）
  { label: '充值', value: 1 },
  { label: '系统奖励', value: 2 },
  { label: '注册奖励', value: 3 },
  { label: '邀请奖励', value: 4 },
  { label: '分销奖励', value: 5 },
  { label: '余额解冻', value: 6 },
  { label: '系统加款', value: 7 },
  { label: '提现拒绝', value: 8 },
  { label: '产品退款', value: 9 },
  { label: '产品收益', value: 10 },
  { label: '转账接收', value: 11 },
  { label: '闪兑接收', value: 12 },
  // 支出类型（负数）
  { label: '提现', value: -1 },
  { label: '系统扣款', value: -2 },
  { label: '购买产品', value: -3 },
  { label: '购买会员', value: -4 },
  { label: '余额冻结', value: -5 },
  { label: '转账发送', value: -6 },
  { label: '闪兑发送', value: -7 }
]

// 批量操作配置
const batchActions: BatchAction[] = [
  {
    label: '批量验证',
    key: 'batchVerify',
    type: 'primary',
    icon: 'CircleCheck',
    confirm: '确认验证选中的 {count} 条账单吗？',
    handler: async (selectedRows) => {
      console.log('批量验证账单:', selectedRows)
      await UserBillService.batchVerifyBills(selectedRows.map((row) => row.id as number))
    }
  },
  {
    label: '批量重算',
    key: 'batchRecalculate',
    type: 'warning',
    icon: 'Calculator',
    confirm: '确认重新计算选中的 {count} 条账单余额吗？',
    handler: async (selectedRows) => {
      console.log('批量重算账单:', selectedRows)
      await UserBillService.batchRecalculateBalance(selectedRows.map((row) => row.id as number))
    }
  },
  // {
  //   label: '批量审核',
  //   key: 'batchAudit',
  //   type: 'success',
  //   icon: 'DocumentChecked',
  //   confirm: '确认审核选中的 {count} 条账单吗？',
  //   handler: async (selectedRows) => {
  //     console.log('批量审核账单:', selectedRows)
  //     await UserBillService.batchAuditBills(
  //       selectedRows.map((row) => row.id as number),
  //       'approved',
  //       '批量审核通过'
  //     )
  //   }
  // },
  {
    label: '批量导出',
    key: 'batchExport',
    type: 'info',
    icon: 'Download',
    handler: async (selectedRows) => {
      console.log('批量导出账单:', selectedRows)
      const exportData = selectedRows.map((row) => ({
        id: row.id as number,
        user_id: row.user_id as number,
        asset_id: row.asset_id as number,
        source_id: row.source_id as number,
        type: row.type as number,
        name: row.name as string,
        amount: row.amount as number,
        fee: row.fee as number,
        balance: row.balance as number,
        created_at: row.created_at as string
      }))
      console.log('导出数据:', exportData)
    }
  }
]

// 账单类型文本转换
export const getBillTypeText = (type: number) => {
  const typeMap: Record<number, string> = {
    // 收入类型
    1: '充值',
    2: '系统奖励',
    3: '注册奖励',
    4: '邀请奖励',
    5: '分销奖励',
    6: '余额解冻',
    7: '系统加款',
    8: '提现拒绝',
    9: '产品退款',
    10: '产品收益',
    11: '转账接收',
    12: '闪兑接收',
    // 支出类型
    '-1': '提现',
    '-2': '系统扣款',
    '-3': '购买产品',
    '-4': '购买会员',
    '-5': '余额冻结',
    '-6': '转账发送',
    '-7': '闪兑发送'
  }
  return typeMap[type] || '未知类型'
}

// 判断是否为收入类型
export const isIncomeType = (type: number) => {
  return type > 0
}

// 判断是否为支出类型
export const isExpenseType = (type: number) => {
  return type < 0
}

// 获取类型标签样式类
export const getTypeTagClass = (type: number) => {
  if (isIncomeType(type)) {
    return 'income-type'
  } else if (isExpenseType(type)) {
    return 'expense-type'
  }
  return 'unknown-type'
}

// 金额格式化
export const formatAmount = (
  amount: number | string,
  precision: number = 4,
  showSign: boolean = false
) => {
  if (!amount && amount !== 0) return '0.0000'
  const num = typeof amount === 'string' ? parseFloat(amount) : amount
  const formattedAmount = num.toFixed(precision)

  if (showSign) {
    return num > 0 ? `+${formattedAmount}` : formattedAmount
  }
  return formattedAmount
}

// 获取金额样式类
export const getAmountClass = (type: number, amount: number) => {
  if (isIncomeType(type)) {
    return 'amount-income'
  } else if (isExpenseType(type)) {
    return 'amount-expense'
  }
  return amount > 0 ? 'amount-positive' : amount < 0 ? 'amount-negative' : 'amount-zero'
}

// 计算净收益
export const calculateNetProfit = (amount: number, fee: number) => {
  return amount - fee
}

// 导出配置
const exportConfig: ExportConfig = {
  enabled: true,
  filename: '用户账单数据导出',
  columns: [
    'id',
    'user_id',
    'asset_id',
    'source_id',
    'type',
    'name',
    'amount',
    'fee',
    'balance',
    'created_at'
  ],
  formats: ['excel', 'csv'],
  maxRows: 50000
}

// 权限配置
const permissionConfig: PermissionConfig = {
  create: () => {
    return true
  },
  update: () => {
    return true
  },
  delete: () => {
    // 账单记录需要谨慎删除，通常只允许管理员删除
    return true
  },
  export: () => true,
  batchDelete: () => true
}

// 响应式配置
const responsiveConfig: ResponsiveConfig = {
  mobile: {
    hideColumns: ['source_id', 'fee', 'balance', 'created_at'],
    cardView: false,
    breakpoint: 768
  }
}

// 安全配置
const securityConfig: SecurityConfig = {
  enableXssProtection: true,
  allowedTags: ['p', 'br', 'strong', 'em'],
  allowedAttributes: {
    div: ['class'],
    span: ['class']
  }
}

// 性能配置
const performanceConfig: PerformanceConfig = {
  enableCache: true,
  cacheTime: 2 * 60 * 1000, // 2分钟
  debounceTime: 300,
  maxCacheSize: 200,
  lazyLoad: true,
  virtualScroll: {
    enabled: true,
    itemHeight: 65,
    buffer: 15
  }
}

// 用户账单管理配置
export const billTableConfig: CrudTableConfig = {
  title: '用户账单',
  addButtonText: '新增账单',
  deleteButtonText: '删除账单',
  deleteConfirmText: '确定要删除该账单记录吗？',
  batchDeleteConfirmText: '确定要删除选中的 {count} 条账单记录吗？',
  emptyHeight: '500px',

  initialSearchState: {
    user_id: '',
    asset_id: '',
    source_id: '',
    type: '',
    name: '',
    min_amount: '',
    max_amount: '',
    min_fee: '',
    max_fee: '',
    income_only: '',
    expense_only: '',
    startTime: '',
    endTime: ''
  },

  searchFormItems: [
    {
      key: 'user_id',
      label: '用户ID',
      type: 'input',
      placeholder: '请输入用户ID'
    },
    {
      key: 'asset_id',
      label: '资产ID',
      type: 'input',
      placeholder: '请输入资产ID'
    },
    {
      key: 'source_id',
      label: '来源ID',
      type: 'input',
      placeholder: '请输入来源ID'
    },
    {
      key: 'type',
      label: '账单类型',
      type: 'select',
      placeholder: '请选择账单类型',
      options: billTypeOptions
    },
    {
      key: 'name',
      label: '账单名称',
      type: 'input',
      placeholder: '请输入账单名称'
    },
    {
      key: 'min_amount',
      label: '最小金额',
      type: 'input',
      placeholder: '请输入最小金额'
    },
    {
      key: 'max_amount',
      label: '最大金额',
      type: 'input',
      placeholder: '请输入最大金额'
    },
    {
      key: 'income_only',
      label: '仅收入',
      type: 'select',
      placeholder: '是否仅显示收入',
      options: [
        { label: '是', value: 'true' },
        { label: '否', value: 'false' }
      ]
    },
    {
      key: 'expense_only',
      label: '仅支出',
      type: 'select',
      placeholder: '是否仅显示支出',
      options: [
        { label: '是', value: 'true' },
        { label: '否', value: 'false' }
      ]
    },
    {
      key: 'startTime',
      label: '开始时间',
      type: 'date',
      placeholder: '请选择开始时间'
    },
    {
      key: 'endTime',
      label: '结束时间',
      type: 'date',
      placeholder: '请选择结束时间'
    }
  ],

  tableColumns: [
    { type: 'selection', width: 55 },
    { label: 'ID', prop: 'id', width: 80, sortable: true },
    {
      label: '用户信息',
      prop: 'user',
      minWidth: 150,
      useSlot: true,
      slotName: 'user'
    },
    {
      label: '资产信息',
      prop: 'asset',
      minWidth: 120,
      useSlot: true,
      slotName: 'asset'
    },
    {
      label: '来源ID',
      prop: 'source_id',
      width: 100,
      sortable: true
    },
    {
      label: '账单类型',
      prop: 'type',
      minWidth: 120,
      useSlot: true,
      slotName: 'billType'
    },
    {
      label: '账单名称',
      prop: 'name',
      minWidth: 150,
      showOverflowTooltip: true,
      columnType: 'copy'
    },
    {
      label: '金额',
      prop: 'amount',
      minWidth: 130,
      useSlot: true,
      slotName: 'amount',
      sortable: true
    },
    {
      label: '手续费',
      prop: 'fee',
      minWidth: 110,
      useSlot: true,
      slotName: 'fee',
      sortable: true
    },
    {
      label: '余额',
      prop: 'balance',
      minWidth: 130,
      useSlot: true,
      slotName: 'balance',
      sortable: true
    },
    {
      label: '净收益',
      prop: 'net_profit',
      minWidth: 130,
      useSlot: true,
      slotName: 'netProfit',
      sortable: false
    },
    {
      label: '创建时间',
      prop: 'created_at',
      minWidth: 160,
      sortable: true,
      columnType: 'date'
    },
    {
      label: '操作',
      width: 250,
      fixed: 'right',
      useSlot: true,
      slotName: 'actions'
    }
  ],

  apiService: createBillApiService(),

  // 分页参数映射
  paginationKey: {
    current: 'page',
    size: 'pageSize'
  },

  dialog: {
    title: {
      add: '添加用户账单',
      edit: '编辑用户账单'
    },
    width: '800px',
    formItems: [
      {
        label: '用户ID',
        prop: 'user_id',
        type: 'input',
        required: true,
        config: {
          placeholder: '请输入用户ID'
        },
        rules: [{ required: true, message: '请输入用户ID', trigger: 'blur' }],
        span: 12
      },
      {
        label: '资产ID',
        prop: 'asset_id',
        type: 'input',
        required: true,
        config: {
          placeholder: '请输入资产ID'
        },
        rules: [{ required: true, message: '请输入资产ID', trigger: 'blur' }],
        span: 12
      },
      {
        label: '来源ID',
        prop: 'source_id',
        type: 'input',
        required: true,
        config: {
          placeholder: '请输入来源ID'
        },
        rules: [{ required: true, message: '请输入来源ID', trigger: 'blur' }],
        span: 12
      },
      {
        label: '账单类型',
        prop: 'type',
        type: 'select',
        required: true,
        options: billTypeOptions,
        rules: [{ required: true, message: '请选择账单类型', trigger: 'change' }],
        span: 12
      },
      {
        label: '账单名称',
        prop: 'name',
        type: 'input',
        required: true,
        config: {
          placeholder: '请输入账单名称',
          maxlength: 100
        },
        rules: [
          { required: true, message: '请输入账单名称', trigger: 'blur' },
          { max: 100, message: '账单名称长度不能超过 100 个字符', trigger: 'blur' }
        ],
        span: 24
      },
      {
        label: '金额',
        prop: 'amount',
        type: 'input',
        required: true,
        config: {
          placeholder: '请输入金额',
          type: 'number',
          step: 0.0001,
          precision: 4
        },
        rules: [
          { required: true, message: '请输入金额', trigger: 'blur' },
          { pattern: /^-?\d+(\.\d{1,4})?$/, message: '请输入有效的金额', trigger: 'blur' }
        ],
        span: 8
      },
      {
        label: '手续费',
        prop: 'fee',
        type: 'input',
        config: {
          placeholder: '请输入手续费',
          type: 'number',
          min: 0,
          step: 0.0001,
          precision: 4
        },
        rules: [{ pattern: /^\d+(\.\d{1,4})?$/, message: '请输入有效的手续费', trigger: 'blur' }],
        span: 8
      },
      {
        label: '余额',
        prop: 'balance',
        type: 'input',
        required: true,
        config: {
          placeholder: '请输入余额',
          type: 'number',
          step: 0.0001,
          precision: 4
        },
        rules: [
          { required: true, message: '请输入余额', trigger: 'blur' },
          { pattern: /^-?\d+(\.\d{1,4})?$/, message: '请输入有效的余额', trigger: 'blur' }
        ],
        span: 8
      }
    ],
    initialFormData: {
      user_id: '',
      asset_id: '',
      source_id: '',
      type: '',
      name: '',
      amount: '0.0000',
      fee: '0.0000',
      balance: '0.0000'
    }
  },

  // 功能配置
  batchActions,
  export: exportConfig,
  permissions: permissionConfig,
  responsive: responsiveConfig,
  security: securityConfig,
  performance: performanceConfig,

  // 操作权限
  allowBatchDelete: true
}
