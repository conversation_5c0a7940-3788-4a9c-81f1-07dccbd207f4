// Debug script for ArtCrudTable edit button issue
// Add this to your component to debug the issue

// 1. Check if the template ref is working
console.log('crudTableRef:', crudTableRef.value)

// 2. Check if showDialog method exists
console.log('showDialog method:', crudTableRef.value?.showDialog)

// 3. Check dialog configuration
console.log('Dialog config:', config.dialog)

// 4. Check permissions
console.log('Update permission:', config.permissions?.update)

// 5. Add debug to the click handler
const debugEditClick = (row) => {
  console.log('Edit button clicked for row:', row)
  console.log('crudTableRef exists:', !!crudTableRef.value)
  console.log('showDialog method exists:', !!crudTableRef.value?.showDialog)
  
  if (crudTableRef.value?.showDialog) {
    console.log('Calling showDialog...')
    crudTableRef.value.showDialog('edit', row)
  } else {
    console.error('showDialog method not found!')
  }
}

// Use this in your template:
// <ArtButtonTable type="edit" @click="debugEditClick(row)" />
