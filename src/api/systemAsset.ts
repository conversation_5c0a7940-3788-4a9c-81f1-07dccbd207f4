import request from '@/utils/http'

/**
 * 资产管理服务
 * 类型：1 平台币 2 加密货币
 * 状态：1 启用 2 禁用
 */
export class SystemAssetService {
  private static baseUrl = '/system/asset'

  static getList(params?: any) {
    return request.get<any>({ url: this.baseUrl, params })
  }

  static create(data: any) {
    return request.post<any>({ url: this.baseUrl, data })
  }

  static update(id: number, data: any) {
    return request.put<any>({ url: `${this.baseUrl}/${id}`, data })
  }

  static remove(id: number) {
    return request.del<any>({ url: `${this.baseUrl}/${id}` })
  }

  static batchRemove(ids: number[]) {
    return request.del<any>({ url: `${this.baseUrl}/batch`, data: { ids } })
  }
}
