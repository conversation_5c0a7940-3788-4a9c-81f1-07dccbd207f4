import { SystemLevelService } from '@/api/systemLevel'
import type {
  CrudTableConfig,
  BatchAction,
  ExportConfig,
  PermissionConfig,
  SecurityConfig,
  PerformanceConfig,
  ResponsiveConfig
} from '@/components/core/tables/ArtCrudTable.vue'

// 过滤空值参数
const filterEmptyParams = (params: Record<string, any>) => {
  const filtered: Record<string, any> = {}
  Object.keys(params).forEach((key) => {
    const value = params[key]
    if (value !== '' && value !== null && value !== undefined) {
      filtered[key] = value
    }
  })
  return filtered
}

// 包装API服务
const createApiService = () => ({
  getList: (params: any) => SystemLevelService.getList(filterEmptyParams(params)),
  delete: async (id: string | number) => {
    await SystemLevelService.remove(Number(id))
    return { code: 0 }
  },
  batchDelete: async (ids: Array<string | number>) => {
    await SystemLevelService.batchRemove(ids.map((v) => Number(v)))
    return { code: 0 }
  },
  update: async (id: string | number, data: any) => {
    await SystemLevelService.update(Number(id), data)
    return { code: 0 }
  },
  create: (data: any) => SystemLevelService.create(data)
})

// 选项
export const levelTypeOptions = [{ label: '会员等级', value: 1 }]

export const statusOptions = [
  { label: '启用', value: 1 },
  { label: '禁用', value: 0 }
]

export const getStatusText = (status: number) =>
  status === 1 ? '启用' : status === 0 ? '禁用' : '未知'
export const getStatusClass = (status: number) =>
  status === 1 ? 'status-enabled' : status === 0 ? 'status-disabled' : 'status-unknown'

// 批量操作
const batchActions: BatchAction[] = [
  {
    label: '批量启用',
    key: 'batchEnable',
    type: 'success',
    icon: 'Open',
    confirm: '确认启用选中的 {count} 个等级吗？',
    handler: async (rows) => {
      await Promise.all(rows.map((r: any) => SystemLevelService.enable(r.id)))
    }
  },
  {
    label: '批量禁用',
    key: 'batchDisable',
    type: 'warning',
    icon: 'Close',
    confirm: '确认禁用选中的 {count} 个等级吗？',
    handler: async (rows) => {
      await Promise.all(rows.map((r: any) => SystemLevelService.disable(r.id)))
    }
  },
  {
    label: '导出数据',
    key: 'export',
    type: 'info',
    icon: 'Download',
    handler: async (rows) => {
      const params = rows.length ? { ids: rows.map((r: any) => r.id) } : {}
      await SystemLevelService.exportList(params)
    }
  }
]

// 导出配置
const exportConfig: ExportConfig = {
  enabled: true,
  filename: '等级数据导出',
  columns: [
    'id',
    'name_field',
    'symbol',
    'type',
    'sort',
    'amount',
    'discount',
    'days',
    'status',
    'description_field',
    'created_at'
  ],
  formats: ['excel', 'csv'],
  maxRows: 10000
}

// 权限/响应/安全/性能
const permissionConfig: PermissionConfig = {
  create: true,
  update: true,
  delete: true,
  export: true
}

const responsiveConfig: ResponsiveConfig = {
  mobile: {
    hideColumns: ['description_field', 'created_at'],
    cardView: false,
    breakpoint: 768
  }
}

const securityConfig: SecurityConfig = {
  enableXssProtection: true,
  allowedTags: ['p', 'br', 'strong', 'em'],
  allowedAttributes: { div: ['class'], span: ['class'] }
}

const performanceConfig: PerformanceConfig = {
  enableCache: true,
  cacheTime: 3 * 60 * 1000,
  debounceTime: 300,
  maxCacheSize: 100,
  lazyLoad: true,
  virtualScroll: { enabled: false, itemHeight: 60, buffer: 10 }
}

export const levelTableConfig: CrudTableConfig = {
  title: '等级管理',
  addButtonText: '新增等级',
  deleteButtonText: '删除等级',
  deleteConfirmText: '确定要删除该等级吗？',
  batchDeleteConfirmText: '确定要删除选中的 {count} 个等级吗？',
  emptyHeight: '500px',

  initialSearchState: {},
  searchFormItems: [
    { key: 'name', label: '名称', type: 'input' },
    { key: 'symbol', label: '标识', type: 'input' },
    { key: 'type', label: '类型', type: 'select', options: levelTypeOptions },
    { key: 'status', label: '状态', type: 'select', options: statusOptions },
    { key: 'created_at', label: '创建时间', type: 'datetimerange' }
  ],

  tableColumns: [
    { prop: 'id', label: 'ID', width: 80, fixed: 'left', sortable: true, align: 'center' },
    { prop: 'icon', label: '图标', width: 80, slot: 'icon', align: 'center' },
    { prop: 'name_field', label: '名称字段', minWidth: 160 },
    { prop: 'symbol', label: '标识', width: 120, align: 'center' },
    { prop: 'type', label: '类型', width: 120, slot: 'type', align: 'center' },
    { prop: 'sort', label: '排序', width: 90, align: 'center', sortable: true },
    { prop: 'amount', label: '金额', width: 120, align: 'right' },
    { prop: 'discount', label: '折扣率', width: 120, align: 'right' },
    { prop: 'days', label: '天数', width: 100, align: 'center' },
    { prop: 'status', label: '状态', width: 100, slot: 'status', align: 'center' },
    { prop: 'description_field', label: '描述字段', minWidth: 160 },
    { prop: 'created_at', label: '创建时间', width: 160, align: 'center' },
    { prop: 'actions', label: '操作', width: 220, fixed: 'right', slot: 'actions', align: 'center' }
  ],

  apiService: createApiService(),
  paginationKey: { current: 'page', size: 'page_size' },

  dialog: {
    formItems: [
      { prop: 'icon', label: '图标', type: 'upload', span: 24 },
      {
        prop: 'name_field',
        label: '名称字段',
        type: 'input',
        rules: [{ required: true, message: '必填', trigger: 'blur' }],
        span: 12
      },
      {
        prop: 'symbol',
        label: '标识',
        type: 'input',
        rules: [{ required: true, message: '必填', trigger: 'blur' }],
        span: 12
      },
      {
        prop: 'type',
        label: '类型',
        type: 'select',
        options: levelTypeOptions,
        rules: [{ required: true, message: '必选', trigger: 'change' }],
        span: 12
      },
      { prop: 'sort', label: '排序', type: 'number', span: 12, config: { precision: 0 } },
      { prop: 'amount', label: '金额', type: 'number', span: 12, config: { precision: 2 } },
      { prop: 'discount', label: '折扣率', type: 'number', span: 12, config: { precision: 2 } },
      { prop: 'days', label: '天数', type: 'number', span: 12, config: { precision: 0 } },
      {
        prop: 'status',
        label: '状态',
        type: 'select',
        options: statusOptions,
        rules: [{ required: true, message: '必选', trigger: 'change' }],
        span: 12
      },
      { prop: 'description_field', label: '描述字段', type: 'input', span: 24 },
      { prop: 'name', label: '名称(当前语言)', type: 'input', span: 24 },
      {
        prop: 'description',
        label: '描述(当前语言)',
        type: 'textarea',
        span: 24,
        config: { rows: 3 }
      }
    ]
  },

  batchActions,
  export: exportConfig,
  permissions: permissionConfig,
  responsive: responsiveConfig,
  security: securityConfig,
  performance: performanceConfig,
  hasStatusSwitch: true,
  statusField: 'status',
  statusActiveValue: 1,
  statusInactiveValue: 0,
  allowBatchDelete: true
}
