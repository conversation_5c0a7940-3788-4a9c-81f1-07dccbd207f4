import { UserService } from '@/api/usersApi'
import type {
  CrudTableConfig,
  BatchAction,
  ExportConfig,
  PermissionConfig,
  SecurityConfig,
  PerformanceConfig,
  ResponsiveConfig
} from '@/components/core/tables/ArtCrudTable.vue'

// 过滤空值参数的工具函数
const filterEmptyParams = (params: Record<string, any>) => {
  const filtered: Record<string, any> = {}
  Object.keys(params).forEach((key) => {
    const value = params[key]
    // 只保留非空值：非空字符串、非null、非undefined、数字0、布尔值false
    if (value !== '' && value !== null && value !== undefined) {
      filtered[key] = value
    }
  })
  return filtered
}

// 包装API服务，添加参数过滤
const createCertificationApiService = () => ({
  getList: (params: any) => {
    // 过滤空值参数，解决URL中出现空值的问题
    const filteredParams = filterEmptyParams(params)
    console.log('Certification API - 过滤后的请求参数:', filteredParams)
    return UserService.getUserCertificationList(filteredParams)
  },
  delete: (id: any) => UserService.deleteCertification(id),
  batchDelete: (ids: any[]) => UserService.batchDeleteCertifications(ids),
  update: (id: any, data: any) => UserService.updateCertification(id, data),
  create: (data: any) => UserService.createCertification(data)
})

// 批量操作配置
const batchActions: BatchAction[] = [
  {
    label: '批量通过',
    key: 'batchApprove',
    type: 'success',
    icon: 'Check',
    confirm: '确认通过选中的 {count} 个认证申请吗？',
    handler: async (selectedRows) => {
      console.log('批量通过认证:', selectedRows)
      await Promise.all(
        selectedRows.map((row) => UserService.reviewUserCertification(row.id as number, 2))
      )
    }
  },
  {
    label: '批量拒绝',
    key: 'batchReject',
    type: 'danger',
    icon: 'Close',
    confirm: '确认拒绝选中的 {count} 个认证申请吗？',
    handler: async (selectedRows) => {
      console.log('批量拒绝认证:', selectedRows)
      // 这里可以实现批量拒绝的逻辑，可能需要输入拒绝原因
      await Promise.all(
        selectedRows.map((row) =>
          UserService.reviewUserCertification(row.id as number, 3, '批量拒绝')
        )
      )
    }
  },
  {
    label: '批量导出',
    key: 'batchExport',
    type: 'primary',
    icon: 'Download',
    handler: async (selectedRows) => {
      console.log('批量导出认证:', selectedRows)
      const exportData = selectedRows.map((row) => ({
        id: row.id as number,
        user_id: row.user_id as number,
        real_name: row.real_name as string,
        id_number: row.id_number as string,
        type: getTypeText(row.type as number),
        status: getStatusText(row.status as number),
        address: row.address as string,
        reason: row.reason as string,
        created_at: row.created_at as string
      }))
      console.log('导出数据:', exportData)
    }
  }
]

// 认证类型文本转换
export const getTypeText = (type: number) => {
  const typeMap: Record<number, string> = {
    1: '身份证'
  }
  return typeMap[type] || '身份证'
}

// 认证状态文本转换
export const getStatusText = (status: number) => {
  const statusMap: Record<number, string> = {
    1: '待审核',
    2: '已通过',
    3: '已拒绝'
  }
  return statusMap[status] || '待审核'
}

// 认证模式文本转换
export const getModeText = (mode: number) => {
  const modeMap: Record<number, string> = {
    1: '身份认证'
  }
  return modeMap[mode] || '身份认证'
}

// 导出配置
const exportConfig: ExportConfig = {
  enabled: true,
  filename: '用户认证数据导出',
  columns: [
    'id',
    'user_id',
    'real_name',
    'id_number',
    'type',
    'status',
    'address',
    'reason',
    'created_at'
  ],
  formats: ['excel', 'csv'],
  maxRows: 1000
}

// 权限配置
const permissionConfig: PermissionConfig = {
  create: () => {
    return true
  },
  update: (row) => {
    // 已通过的认证不允许编辑
    return (row.status as number) !== 2
  },
  delete: (row) => {
    // 已通过的认证不允许删除
    return (row.status as number) !== 2
  },
  export: () => true,
  batchDelete: () => true
}

// 响应式配置
const responsiveConfig: ResponsiveConfig = {
  mobile: {
    hideColumns: ['address', 'reason', 'created_at', 'user_id'],
    cardView: false,
    breakpoint: 768
  }
}

// 安全配置
const securityConfig: SecurityConfig = {
  enableXssProtection: true,
  allowedTags: ['p', 'br', 'strong', 'em'],
  allowedAttributes: {
    div: ['class'],
    span: ['class']
  }
}

// 性能配置
const performanceConfig: PerformanceConfig = {
  enableCache: true,
  cacheTime: 3 * 60 * 1000, // 3分钟
  debounceTime: 500,
  maxCacheSize: 50,
  lazyLoad: true,
  virtualScroll: {
    enabled: true,
    itemHeight: 60,
    buffer: 10
  }
}

// 用户认证管理配置
export const certificationTableConfig: CrudTableConfig = {
  title: '用户认证',
  addButtonText: '新增认证',
  deleteButtonText: '删除认证',
  deleteConfirmText: '确定要删除该认证记录吗？',
  batchDeleteConfirmText: '确定要删除选中的 {count} 条认证记录吗？',
  emptyHeight: '500px',

  initialSearchState: {
    user_id: '',
    real_name: '',
    id_number: '',
    type: '',
    status: '',
    startTime: '',
    endTime: ''
  },

  searchFormItems: [
    {
      key: 'user_id',
      label: '用户ID',
      type: 'input',
      placeholder: '请输入用户ID'
    },
    {
      key: 'real_name',
      label: '证件姓名',
      type: 'input',
      placeholder: '请输入证件姓名'
    },
    {
      key: 'id_number',
      label: '证件号码',
      type: 'input',
      placeholder: '请输入证件号码'
    },
    {
      key: 'type',
      label: '证件类型',
      type: 'select',
      placeholder: '请选择证件类型',
      options: [{ label: '身份证', value: '1' }]
    },
    {
      key: 'status',
      label: '审核状态',
      type: 'select',
      placeholder: '请选择审核状态',
      options: [
        { label: '待审核', value: '1' },
        { label: '已通过', value: '2' },
        { label: '已拒绝', value: '3' }
      ]
    },
    {
      key: 'startTime',
      label: '开始时间',
      type: 'date',
      placeholder: '请选择开始时间'
    },
    {
      key: 'endTime',
      label: '结束时间',
      type: 'date',
      placeholder: '请选择结束时间'
    }
  ],

  tableColumns: [
    { type: 'selection', width: 55 },
    { label: 'ID', prop: 'id', width: 80, sortable: true },
    {
      label: '用户ID',
      prop: 'user_id',
      width: 100,
      sortable: true
    },
    {
      label: '用户信息',
      prop: 'user',
      minWidth: 150,
      useSlot: true,
      slotName: 'user'
    },
    {
      label: '证件姓名',
      prop: 'real_name',
      minWidth: 120,
      columnType: 'copy'
    },
    {
      label: '证件号码',
      prop: 'id_number',
      minWidth: 180,
      columnType: 'copy',
      showOverflowTooltip: true
    },
    {
      label: '证件类型',
      prop: 'type',
      width: 100,
      useSlot: true,
      slotName: 'type'
    },
    {
      label: '认证模式',
      prop: 'mode',
      width: 100,
      useSlot: true,
      slotName: 'mode'
    },
    {
      label: '证件照',
      prop: 'photos',
      width: 120,
      useSlot: true,
      slotName: 'photos'
    },
    {
      label: '详细地址',
      prop: 'address',
      minWidth: 200,
      showOverflowTooltip: true
    },
    {
      label: '审核状态',
      prop: 'status',
      width: 100,
      useSlot: true,
      slotName: 'status'
    },
    {
      label: '拒绝原因',
      prop: 'reason',
      minWidth: 200,
      showOverflowTooltip: true
    },
    {
      label: '申请时间',
      prop: 'created_at',
      minWidth: 160,
      sortable: true,
      columnType: 'date'
    },
    {
      label: '操作',
      width: 200,
      fixed: 'right',
      useSlot: true,
      slotName: 'actions'
    }
  ],

  apiService: createCertificationApiService(),

  // 分页参数映射
  paginationKey: {
    current: 'page',
    size: 'pageSize'
  },

  dialog: {
    title: {
      add: '添加用户认证',
      edit: '编辑用户认证'
    },
    width: '900px',
    formItems: [
      {
        label: '用户ID',
        prop: 'user_id',
        type: 'input',
        required: true,
        config: {
          placeholder: '请输入用户ID'
        },
        rules: [{ required: true, message: '请输入用户ID', trigger: 'blur' }],
        span: 12
      },
      {
        label: '证件类型',
        prop: 'type',
        type: 'select',
        required: true,
        options: [{ label: '身份证', value: 1 }],
        span: 12
      },
      {
        label: '证件姓名',
        prop: 'real_name',
        type: 'input',
        required: true,
        config: {
          placeholder: '请输入证件姓名',
          maxlength: 50
        },
        rules: [
          { required: true, message: '请输入证件姓名', trigger: 'blur' },
          { max: 50, message: '证件姓名长度不能超过 50 个字符', trigger: 'blur' }
        ],
        span: 12
      },
      {
        label: '证件号码',
        prop: 'id_number',
        type: 'input',
        required: true,
        config: {
          placeholder: '请输入证件号码',
          maxlength: 50
        },
        rules: [
          { required: true, message: '请输入证件号码', trigger: 'blur' },
          { max: 50, message: '证件号码长度不能超过 50 个字符', trigger: 'blur' },
          {
            pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,
            message: '请输入正确的身份证号格式',
            trigger: 'blur'
          }
        ],
        span: 12
      },
      {
        label: '详细地址',
        prop: 'address',
        type: 'input',
        config: {
          placeholder: '请输入详细地址',
          maxlength: 255
        },
        rules: [{ max: 255, message: '详细地址长度不能超过 255 个字符', trigger: 'blur' }],
        span: 24
      },
      {
        label: '证件照正面',
        prop: 'photo1',
        type: 'upload',
        required: true,
        config: {
          accept: 'image/*',
          maxSize: 5,
          triggerText: '选择证件照正面',
          tip: '支持 JPG、PNG 格式，文件大小不超过 5MB'
        },
        span: 12
      },
      {
        label: '证件照反面',
        prop: 'photo2',
        type: 'upload',
        required: true,
        config: {
          accept: 'image/*',
          maxSize: 5,
          triggerText: '选择证件照反面',
          tip: '支持 JPG、PNG 格式，文件大小不超过 5MB'
        },
        span: 12
      },
      {
        label: '手持证件照',
        prop: 'photo3',
        type: 'upload',
        config: {
          accept: 'image/*',
          maxSize: 5,
          triggerText: '选择手持证件照',
          tip: '支持 JPG、PNG 格式，文件大小不超过 5MB'
        },
        span: 12
      },
      {
        label: '审核状态',
        prop: 'status',
        type: 'select',
        required: true,
        options: [
          { label: '待审核', value: 1 },
          { label: '已通过', value: 2 },
          { label: '已拒绝', value: 3 }
        ],
        span: 12,
        show: (formData, type) => type === 'edit'
      },
      {
        label: '拒绝原因',
        prop: 'reason',
        type: 'textarea',
        config: {
          placeholder: '请输入拒绝原因',
          maxlength: 1024,
          rows: 4
        },
        rules: [{ max: 1024, message: '拒绝原因长度不能超过 1024 个字符', trigger: 'blur' }],
        span: 24,
        show: (formData, type) => type === 'edit' && formData.status === 3
      }
    ],
    initialFormData: {
      user_id: '',
      type: 1,
      real_name: '',
      id_number: '',
      photo1: '',
      photo2: '',
      photo3: '',
      address: '',
      status: 1,
      reason: ''
    }
  },

  // 功能配置
  batchActions,
  export: exportConfig,
  permissions: permissionConfig,
  responsive: responsiveConfig,
  security: securityConfig,
  performance: performanceConfig,

  // 状态切换配置
  hasStatusSwitch: false, // 认证状态不适合简单的开关切换

  // 操作权限
  allowBatchDelete: true
}
