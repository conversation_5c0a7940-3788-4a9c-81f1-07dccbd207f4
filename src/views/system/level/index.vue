<script setup lang="ts">
  import { ref } from 'vue'
  import ArtCrudTable from '@/components/core/tables/ArtCrudTable.vue'
  import ArtButtonTable from '@/components/core/forms/art-button-table/index.vue'
  import { levelTableConfig, getStatusClass, getStatusText, levelTypeOptions } from './level'
  import { SystemLevelService } from '@/api/systemLevel'

  defineOptions({ name: 'SystemLevel' })

  const crudRef = ref()

  const setEnabled = (row: any, enabled: boolean) => {
    const fn = enabled ? SystemLevelService.enable : SystemLevelService.disable
    fn(row.id).then(() => crudRef.value?.refreshData())
  }
</script>

<template>
  <ArtCrudTable ref="crudRef" :config="levelTableConfig">
    <!-- 图标 -->
    <template #icon="{ row }">
      <div class="icon-wrap">
        <img v-if="row.icon" :src="row.icon" alt="icon" />
        <span v-else class="icon-empty">无</span>
      </div>
    </template>

    <!-- 类型 -->
    <template #type="{ row }">
      <span class="type-tag">{{
        levelTypeOptions.find((o) => o.value === row.type)?.label || '未知'
      }}</span>
    </template>

    <!-- 状态 -->
    <template #status="{ row }">
      <span :class="['status-tag', getStatusClass(row.status)]">{{
        getStatusText(row.status)
      }}</span>
    </template>

    <!-- 操作 -->
    <template #actions="{ row }">
      <div class="actions">
        <ArtButtonTable type="edit" @click="crudRef?.showDialog('edit', row)" />
        <ArtButtonTable type="delete" @click="crudRef?.deleteItem(row.id)" />
        <el-dropdown trigger="click">
          <ArtButtonTable type="more" />
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item divided @click="setEnabled(row, true)">
                <el-icon><Open /></el-icon> 启用
              </el-dropdown-item>
              <el-dropdown-item @click="setEnabled(row, false)">
                <el-icon><Close /></el-icon> 禁用
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </template>
  </ArtCrudTable>
</template>

<style scoped lang="scss">
  .icon-wrap {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    overflow: hidden;
    border: 1px solid #eee;
    border-radius: 4px;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .icon-empty {
      font-size: 12px;
      color: #bbb;
    }
  }

  .type-tag {
    padding: 2px 6px;
    font-size: 12px;
    color: #1890ff;
    background: #e6f7ff;
    border: 1px solid #91d5ff;
    border-radius: 4px;
  }

  .status-tag {
    padding: 2px 6px;
    font-size: 12px;
    font-weight: 500;
    border-radius: 4px;
  }

  .status-enabled {
    color: #52c41a;
    background: #f6ffed;
    border: 1px solid #b7eb8f;
  }

  .status-disabled {
    color: #8c8c8c;
    background: #f5f5f5;
    border: 1px solid #d9d9d9;
  }

  .actions {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }
</style>
