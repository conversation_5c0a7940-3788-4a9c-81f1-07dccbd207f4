<script setup lang="ts">
  import { ref } from 'vue'
  import ArtCrudTable from '@/components/core/tables/ArtCrudTable.vue'
  import ArtButtonTable from '@/components/core/forms/art-button-table/index.vue'
  import { levelTableConfig, getTypeText, getStatusText, getExpiredStatus } from './level'

  defineOptions({ name: 'UserLevel' })

  const crudTableRef = ref()
  const componentKey = ref(Date.now())

  // 用户信息渲染
  const renderUser = (row: any) => {
    if (row.user) {
      return {
        id: row.user.id,
        username: row.user.username,
        nickname: row.user.nickname,
        avatar: row.user.avatar
      }
    }
    return { id: row.user_id, username: '未知用户', nickname: '', avatar: '' }
  }

  // 等级信息渲染
  const renderLevel = (row: any) => {
    if (row.level) {
      return {
        id: row.level.id,
        name: row.level.name,
        description: row.level.description,
        icon: row.level.icon
      }
    }
    return { id: row.level_id, name: '未知等级', description: '', icon: '' }
  }

  // 等级类型渲染
  const renderType = (row: any) => {
    const typeText = getTypeText(row.type)
    let typeClass = ''
    switch (row.type) {
      case 1:
        typeClass = 'type-buy'
        break
      case 2:
        typeClass = 'type-deposit'
        break
      case 3:
        typeClass = 'type-gift'
        break
    }
    return { text: typeText, class: typeClass }
  }

  // 状态渲染
  const renderStatus = (row: any) => {
    const statusText = getStatusText(row.status)
    const statusClass = row.status === 1 ? 'status-active' : 'status-disabled'
    return { text: statusText, class: statusClass }
  }

  // 过期时间渲染
  const renderExpiredAt = (row: any) => {
    if (!row.expired_at) return null
    const expiredStatus = getExpiredStatus(row.expired_at)
    let statusClass = 'expired-normal'
    let statusText = ''

    if (expiredStatus.isExpired) {
      statusClass = 'expired-overdue'
      statusText = '已过期'
    } else if (expiredStatus.isExpiringSoon) {
      statusClass = 'expired-warning'
      statusText = `${expiredStatus.remainingDays}天后过期`
    } else {
      statusClass = 'expired-normal'
      statusText = `还有${expiredStatus.remainingDays}天`
    }

    return {
      date: row.expired_at,
      status: statusText,
      class: statusClass
    }
  }

  // 等级数据渲染
  const renderLevelData = (row: any) => {
    if (!row.data) return null
    try {
      const data = typeof row.data === 'string' ? JSON.parse(row.data) : row.data
      return {
        benefits: data.benefits || [],
        privileges: data.privileges || [],
        discount: data.discount || 0,
        points: data.points || 0
      }
    } catch {
      return { error: '数据格式错误' }
    }
  }

  // 查看等级详情
  const viewLevelDetail = (row: any) => {
    console.log('查看等级详情:', row)
    // 这里可以实现跳转到等级详情页面的逻辑
  }

  // 延期等级
  const extendLevel = (row: any, days: number = 30) => {
    console.log('延期等级:', { id: row.id, days })
    // 调用API延期等级
    crudTableRef.value?.refreshData()
  }

  // 激活等级
  const activateLevel = (row: any) => {
    console.log('激活等级:', row.id)
    // 调用API激活等级
    crudTableRef.value?.refreshData()
  }

  // 禁用等级
  const disableLevel = (row: any) => {
    console.log('禁用等级:', row.id)
    // 调用API禁用等级
    crudTableRef.value?.refreshData()
  }
</script>

<template>
  <ArtCrudTable ref="crudTableRef" :key="componentKey" :config="levelTableConfig">
    <!-- 用户信息列 -->
    <template #user="{ row }">
      <div class="user-info">
        <div class="user-avatar" v-if="renderUser(row).avatar">
          <img :src="renderUser(row).avatar" :alt="renderUser(row).username" class="avatar-img" />
        </div>
        <div class="user-details">
          <div class="user-id">ID: {{ renderUser(row).id }}</div>
          <div class="user-name">{{ renderUser(row).username }}</div>
          <div v-if="renderUser(row).nickname" class="user-nickname">
            ({{ renderUser(row).nickname }})
          </div>
        </div>
      </div>
    </template>

    <!-- 等级信息列 -->
    <template #level="{ row }">
      <div class="level-info">
        <div class="level-icon" v-if="renderLevel(row).icon">
          <img :src="renderLevel(row).icon" :alt="renderLevel(row).name" class="level-icon-img" />
        </div>
        <div class="level-details">
          <div class="level-id">ID: {{ renderLevel(row).id }}</div>
          <div class="level-name">{{ renderLevel(row).name }}</div>
          <div v-if="renderLevel(row).description" class="level-desc">
            {{ renderLevel(row).description }}
          </div>
        </div>
      </div>
    </template>

    <!-- 等级类型列 -->
    <template #type="{ row }">
      <span :class="['type-tag', renderType(row).class]">
        {{ renderType(row).text }}
      </span>
    </template>

    <!-- 状态列 -->
    <template #status="{ row }">
      <span :class="['status-tag', renderStatus(row).class]">
        {{ renderStatus(row).text }}
      </span>
    </template>

    <!-- 过期时间列 -->
    <template #expiredAt="{ row }">
      <div class="expired-info" v-if="renderExpiredAt(row)">
        <div class="expired-date">{{ renderExpiredAt(row)?.date }}</div>
        <div :class="['expired-status', renderExpiredAt(row)?.class]">
          {{ renderExpiredAt(row)?.status }}
        </div>
      </div>
      <span v-else class="text-gray">未设置</span>
    </template>

    <!-- 等级数据列 -->
    <template #levelData="{ row }">
      <div class="level-data" v-if="renderLevelData(row) && !renderLevelData(row)?.error">
        <div class="data-item" v-if="renderLevelData(row)?.discount">
          <span class="data-label">折扣:</span>
          <span class="data-value">{{ renderLevelData(row)?.discount }}%</span>
        </div>
        <div class="data-item" v-if="renderLevelData(row)?.points">
          <span class="data-label">积分:</span>
          <span class="data-value">{{ renderLevelData(row)?.points }}</span>
        </div>
        <div class="data-item" v-if="renderLevelData(row)?.benefits?.length">
          <span class="data-label">权益:</span>
          <span class="data-value">{{ renderLevelData(row)?.benefits?.length }}项</span>
        </div>
      </div>
      <span v-else-if="renderLevelData(row)?.error" class="text-error">
        {{ renderLevelData(row)?.error }}
      </span>
      <span v-else class="text-gray">无数据</span>
    </template>

    <!-- 操作列 -->
    <template #actions="{ row }">
      <div class="flex gap-2">
        <ArtButtonTable type="edit" @click="crudTableRef?.showDialog('edit', row)" />
        <ArtButtonTable
          type="delete"
          @click="crudTableRef?.deleteItem(row.id)"
          :disabled="row.status === 1"
        />
        <ArtButtonTable type="view" @click="viewLevelDetail(row)" />

        <!-- 状态操作按钮 -->
        <ArtButtonTable v-if="row.status === 0" type="add" @click="activateLevel(row)" />
        <ArtButtonTable v-if="row.status === 1" type="more" @click="disableLevel(row)" />

        <!-- 延期按钮 -->
        <ArtButtonTable
          v-if="row.status === 1 && renderExpiredAt(row)"
          type="more"
          @click="extendLevel(row)"
        />
      </div>
    </template>
  </ArtCrudTable>
</template>

<style lang="scss" scoped>
  .flex {
    display: flex;
  }

  .gap-2 {
    gap: 8px;
  }

  .text-gray {
    color: #999;
  }

  .text-error {
    color: #f56565;
  }

  // 用户信息样式
  .user-info {
    display: flex;
    gap: 8px;
    align-items: center;

    .user-avatar {
      .avatar-img {
        width: 32px;
        height: 32px;
        object-fit: cover;
        border: 1px solid #e0e0e0;
        border-radius: 50%;
      }
    }

    .user-details {
      .user-id {
        margin-bottom: 2px;
        font-size: 12px;
        color: #999;
      }

      .user-name {
        font-weight: 500;
        color: #333;
      }

      .user-nickname {
        margin-top: 2px;
        font-size: 12px;
        color: #666;
      }
    }
  }

  // 等级信息样式
  .level-info {
    display: flex;
    gap: 8px;
    align-items: center;

    .level-icon {
      .level-icon-img {
        width: 24px;
        height: 24px;
        object-fit: cover;
        border-radius: 4px;
      }
    }

    .level-details {
      .level-id {
        margin-bottom: 2px;
        font-size: 12px;
        color: #999;
      }

      .level-name {
        font-weight: 500;
        color: #333;
      }

      .level-desc {
        margin-top: 2px;
        font-size: 12px;
        color: #666;
      }
    }
  }

  // 等级类型标签样式
  .type-tag {
    padding: 4px 8px;
    font-size: 12px;
    font-weight: 500;
    border-radius: 4px;

    &.type-buy {
      color: #1976d2;
      background-color: #e3f2fd;
      border: 1px solid #bbdefb;
    }

    &.type-deposit {
      color: #388e3c;
      background-color: #e8f5e8;
      border: 1px solid #a5d6a7;
    }

    &.type-gift {
      color: #c2185b;
      background-color: #fce4ec;
      border: 1px solid #f48fb1;
    }
  }

  // 状态标签样式
  .status-tag {
    padding: 4px 8px;
    font-size: 12px;
    font-weight: 500;
    border-radius: 4px;

    &.status-active {
      color: #2e7d32;
      background-color: #e8f5e8;
      border: 1px solid #a5d6a7;
    }

    &.status-disabled {
      color: #757575;
      background-color: #f5f5f5;
      border: 1px solid #e0e0e0;
    }
  }

  // 过期信息样式
  .expired-info {
    .expired-date {
      margin-bottom: 2px;
      font-size: 13px;
      color: #333;
    }

    .expired-status {
      padding: 2px 6px;
      font-size: 12px;
      font-weight: 500;
      border-radius: 3px;

      &.expired-normal {
        color: #2e7d32;
        background-color: #e8f5e8;
      }

      &.expired-warning {
        color: #f57c00;
        background-color: #fff3e0;
      }

      &.expired-overdue {
        color: #d32f2f;
        background-color: #ffebee;
      }
    }
  }

  // 等级数据样式
  .level-data {
    .data-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 2px;
      font-size: 12px;

      .data-label {
        color: #666;
      }

      .data-value {
        font-weight: 500;
        color: #333;
      }
    }
  }
</style>
