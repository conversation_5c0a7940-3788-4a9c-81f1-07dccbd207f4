<script setup lang="ts">
  import { ref } from 'vue'
  import ArtCrudTable from '@/components/core/tables/ArtCrudTable.vue'
  import ArtButtonTable from '@/components/core/forms/art-button-table/index.vue'
  import { accountTableConfig, getTypeText, getStatusText, parseAccountData } from './account'

  defineOptions({ name: 'UserAccount' })

  const crudTableRef = ref()
  const componentKey = ref(Date.now())

  // 管理员信息渲染
  const renderManager = (row: any) => {
    if (row.manager) {
      return {
        id: row.manager.id,
        name: row.manager.name,
        email: row.manager.email
      }
    }
    return { id: row.manager_id, name: '未知管理员', email: '' }
  }

  // 用户信息渲染
  const renderUser = (row: any) => {
    if (row.user) {
      return {
        id: row.user.id,
        username: row.user.username,
        nickname: row.user.nickname,
        avatar: row.user.avatar
      }
    }
    return { id: row.user_id, username: '未知用户', nickname: '', avatar: '' }
  }

  // 资产信息渲染
  const renderAsset = (row: any) => {
    if (row.asset) {
      return {
        id: row.asset.id,
        name: row.asset.name,
        symbol: row.asset.symbol,
        icon: row.asset.icon
      }
    }
    return { id: row.asset_id, name: '未知资产', symbol: '', icon: '' }
  }

  // 支付方式渲染
  const renderPayment = (row: any) => {
    if (row.payment) {
      return {
        id: row.payment.id,
        name: row.payment.name,
        type: row.payment.type
      }
    }
    return { id: row.payment_id, name: '未知支付', type: '' }
  }

  // 账户类型渲染
  const renderType = (row: any) => {
    const typeText = getTypeText(row.type)
    let typeClass = ''
    switch (row.type) {
      case 1:
        typeClass = 'type-bank'
        break
      case 2:
        typeClass = 'type-crypto'
        break
      case 3:
        typeClass = 'type-payment'
        break
    }
    return { text: typeText, class: typeClass }
  }

  // 状态渲染
  const renderStatus = (row: any) => {
    const statusText = getStatusText(row.status)
    const statusClass = row.status === 1 ? 'status-enabled' : 'status-disabled'
    return { text: statusText, class: statusClass }
  }

  // 账户数据渲染
  const renderAccountData = (row: any) => {
    if (!row.data) return null
    return parseAccountData(row.data, row.type)
  }

  // 查看账户详情
  const viewAccountDetail = (row: any) => {
    console.log('查看账户详情:', row)
    // 这里可以实现跳转到账户详情页面的逻辑
  }

  // 验证账户
  const verifyAccount = (row: any) => {
    console.log('验证账户:', row.id)
    // 调用API验证账户
    crudTableRef.value?.refreshData()
  }

  // 启用账户
  const enableAccount = (row: any) => {
    console.log('启用账户:', row.id)
    // 调用API启用账户
    crudTableRef.value?.refreshData()
  }

  // 禁用账户
  const disableAccount = (row: any) => {
    console.log('禁用账户:', row.id)
    // 调用API禁用账户
    crudTableRef.value?.refreshData()
  }
</script>

<template>
  <ArtCrudTable ref="crudTableRef" :key="componentKey" :config="accountTableConfig">
    <!-- 管理员列 -->
    <template #manager="{ row }">
      <div class="manager-info">
        <div class="manager-id">ID: {{ renderManager(row).id }}</div>
        <div class="manager-name">{{ renderManager(row).name }}</div>
        <div v-if="renderManager(row).email" class="manager-email">
          {{ renderManager(row).email }}
        </div>
      </div>
    </template>

    <!-- 用户信息列 -->
    <template #user="{ row }">
      <div class="user-info">
        <div class="user-avatar" v-if="renderUser(row).avatar">
          <img :src="renderUser(row).avatar" :alt="renderUser(row).username" class="avatar-img" />
        </div>
        <div class="user-details">
          <div class="user-id">ID: {{ renderUser(row).id }}</div>
          <div class="user-name">{{ renderUser(row).username }}</div>
          <div v-if="renderUser(row).nickname" class="user-nickname">
            ({{ renderUser(row).nickname }})
          </div>
        </div>
      </div>
    </template>

    <!-- 资产信息列 -->
    <template #asset="{ row }">
      <div class="asset-info">
        <div class="asset-icon" v-if="renderAsset(row).icon">
          <img :src="renderAsset(row).icon" :alt="renderAsset(row).symbol" class="asset-icon-img" />
        </div>
        <div class="asset-details">
          <div class="asset-symbol">{{ renderAsset(row).symbol }}</div>
          <div class="asset-name">{{ renderAsset(row).name }}</div>
        </div>
      </div>
    </template>

    <!-- 支付方式列 -->
    <template #payment="{ row }">
      <div class="payment-info">
        <div class="payment-id">ID: {{ renderPayment(row).id }}</div>
        <div class="payment-name">{{ renderPayment(row).name }}</div>
        <div v-if="renderPayment(row).type" class="payment-type">
          类型: {{ renderPayment(row).type }}
        </div>
      </div>
    </template>

    <!-- 账户类型列 -->
    <template #type="{ row }">
      <span :class="['type-tag', renderType(row).class]">
        {{ renderType(row).text }}
      </span>
    </template>

    <!-- 状态列 -->
    <template #status="{ row }">
      <span :class="['status-tag', renderStatus(row).class]">
        {{ renderStatus(row).text }}
      </span>
    </template>

    <!-- 账户数据列 -->
    <template #accountData="{ row }">
      <div class="account-data" v-if="renderAccountData(row) && !renderAccountData(row)?.error">
        <!-- 银行卡数据 -->
        <div v-if="row.type === 1 && renderAccountData(row)" class="bank-data">
          <div class="data-item" v-if="renderAccountData(row)?.bank_name">
            <span class="data-label">银行:</span>
            <span class="data-value">{{ renderAccountData(row)?.bank_name }}</span>
          </div>
          <div class="data-item" v-if="renderAccountData(row)?.account_number">
            <span class="data-label">卡号:</span>
            <span class="data-value">{{ renderAccountData(row)?.account_number }}</span>
          </div>
          <div class="data-item" v-if="renderAccountData(row)?.account_holder">
            <span class="data-label">户名:</span>
            <span class="data-value">{{ renderAccountData(row)?.account_holder }}</span>
          </div>
        </div>

        <!-- 加密货币数据 -->
        <div v-else-if="row.type === 2 && renderAccountData(row)" class="crypto-data">
          <div class="data-item" v-if="renderAccountData(row)?.address">
            <span class="data-label">地址:</span>
            <span class="data-value">{{ renderAccountData(row)?.address }}</span>
          </div>
          <div class="data-item" v-if="renderAccountData(row)?.network">
            <span class="data-label">网络:</span>
            <span class="data-value">{{ renderAccountData(row)?.network }}</span>
          </div>
          <div class="data-item" v-if="renderAccountData(row)?.memo">
            <span class="data-label">备注:</span>
            <span class="data-value">{{ renderAccountData(row)?.memo }}</span>
          </div>
        </div>

        <!-- 三方支付数据 -->
        <div v-else-if="row.type === 3 && renderAccountData(row)" class="payment-data">
          <div class="data-item" v-if="renderAccountData(row)?.platform">
            <span class="data-label">平台:</span>
            <span class="data-value">{{ renderAccountData(row)?.platform }}</span>
          </div>
          <div class="data-item" v-if="renderAccountData(row)?.account_id">
            <span class="data-label">账号:</span>
            <span class="data-value">{{ renderAccountData(row)?.account_id }}</span>
          </div>
          <div class="data-item" v-if="renderAccountData(row)?.email">
            <span class="data-label">邮箱:</span>
            <span class="data-value">{{ renderAccountData(row)?.email }}</span>
          </div>
        </div>
      </div>
      <span v-else-if="renderAccountData(row)?.error" class="text-error">
        {{ renderAccountData(row)?.error }}
      </span>
      <span v-else class="text-gray">无数据</span>
    </template>

    <!-- 操作列 -->
    <template #actions="{ row }">
      <div class="flex gap-2">
        <ArtButtonTable type="edit" @click="crudTableRef?.showDialog('edit', row)" />
        <ArtButtonTable
          type="delete"
          @click="crudTableRef?.deleteItem(row.id)"
          :disabled="row.status === 1"
        />
        <ArtButtonTable type="view" @click="viewAccountDetail(row)" />

        <!-- 状态操作按钮 -->
        <ArtButtonTable v-if="row.status === 0" type="add" @click="enableAccount(row)" />
        <ArtButtonTable v-if="row.status === 1" type="more" @click="disableAccount(row)" />

        <!-- 验证和冻结按钮 -->
        <ArtButtonTable type="more" @click="verifyAccount(row)" />
      </div>
    </template>
  </ArtCrudTable>
</template>

<style lang="scss" scoped>
  .flex {
    display: flex;
  }

  .gap-2 {
    gap: 8px;
  }

  .text-gray {
    color: #999;
  }

  .text-error {
    color: #f56565;
  }

  // 管理员信息样式
  .manager-info {
    .manager-id {
      margin-bottom: 2px;
      font-size: 12px;
      color: #999;
    }

    .manager-name {
      font-weight: 500;
      color: #333;
    }

    .manager-email {
      margin-top: 2px;
      font-size: 12px;
      color: #666;
    }
  }

  // 用户信息样式
  .user-info {
    display: flex;
    gap: 8px;
    align-items: center;

    .user-avatar {
      .avatar-img {
        width: 32px;
        height: 32px;
        object-fit: cover;
        border: 1px solid #e0e0e0;
        border-radius: 50%;
      }
    }

    .user-details {
      .user-id {
        margin-bottom: 2px;
        font-size: 12px;
        color: #999;
      }

      .user-name {
        font-weight: 500;
        color: #333;
      }

      .user-nickname {
        margin-top: 2px;
        font-size: 12px;
        color: #666;
      }
    }
  }

  // 资产信息样式
  .asset-info {
    display: flex;
    gap: 8px;
    align-items: center;

    .asset-icon {
      .asset-icon-img {
        width: 24px;
        height: 24px;
        object-fit: cover;
        border-radius: 50%;
      }
    }

    .asset-details {
      .asset-symbol {
        font-size: 14px;
        font-weight: 500;
        color: #333;
      }

      .asset-name {
        margin-top: 2px;
        font-size: 12px;
        color: #666;
      }
    }
  }

  // 支付方式信息样式
  .payment-info {
    .payment-id {
      margin-bottom: 2px;
      font-size: 12px;
      color: #999;
    }

    .payment-name {
      font-weight: 500;
      color: #333;
    }

    .payment-type {
      margin-top: 2px;
      font-size: 12px;
      color: #666;
    }
  }

  // 账户类型标签样式
  .type-tag {
    padding: 4px 8px;
    font-size: 12px;
    font-weight: 500;
    border-radius: 4px;

    &.type-bank {
      color: #1976d2;
      background-color: #e3f2fd;
      border: 1px solid #bbdefb;
    }

    &.type-crypto {
      color: #f57c00;
      background-color: #fff3e0;
      border: 1px solid #ffcc02;
    }

    &.type-payment {
      color: #388e3c;
      background-color: #e8f5e8;
      border: 1px solid #a5d6a7;
    }
  }

  // 状态标签样式
  .status-tag {
    padding: 4px 8px;
    font-size: 12px;
    font-weight: 500;
    border-radius: 4px;

    &.status-enabled {
      color: #2e7d32;
      background-color: #e8f5e8;
      border: 1px solid #a5d6a7;
    }

    &.status-disabled {
      color: #757575;
      background-color: #f5f5f5;
      border: 1px solid #e0e0e0;
    }
  }

  // 账户数据样式
  .account-data {
    .data-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 4px;
      font-size: 12px;

      .data-label {
        min-width: 40px;
        color: #666;
      }

      .data-value {
        flex: 1;
        font-weight: 500;
        color: #333;
        text-align: right;
        word-break: break-all;
      }
    }
  }

  // 银行数据样式
  .bank-data {
    padding-left: 8px;
    border-left: 3px solid #1976d2;
  }

  // 加密货币数据样式
  .crypto-data {
    padding-left: 8px;
    border-left: 3px solid #f57c00;
  }

  // 三方支付数据样式
  .payment-data {
    padding-left: 8px;
    border-left: 3px solid #388e3c;
  }
</style>
