import { UserService } from '@/api/usersApi'
import type {
  CrudTableConfig,
  BatchAction,
  ExportConfig,
  PermissionConfig,
  SecurityConfig,
  PerformanceConfig,
  ResponsiveConfig
} from '@/components/core/tables/ArtCrudTable.vue'

// 过滤空值参数的工具函数
const filterEmptyParams = (params: Record<string, any>) => {
  const filtered: Record<string, any> = {}
  Object.keys(params).forEach((key) => {
    const value = params[key]
    // 只保留非空值：非空字符串、非null、非undefined、数字0、布尔值false
    if (value !== '' && value !== null && value !== undefined) {
      filtered[key] = value
    }
  })
  return filtered
}

// 包装API服务，添加参数过滤
const createLevelApiService = () => ({
  getList: (params: any) => {
    // 过滤空值参数，解决URL中出现空值的问题
    const filteredParams = filterEmptyParams(params)
    console.log('UserLevel API - 过滤后的请求参数:', filteredParams)
    return UserService.getUserLevelList(filteredParams)
  },
  delete: (id: any) => UserService.deleteUserLevel(id),
  batchDelete: (ids: any[]) => UserService.batchDeleteUserLevels(ids),
  update: (id: any, data: any) => UserService.updateUserLevel(id, data),
  create: (data: any) => UserService.createUserLevel(data)
})

// 批量操作配置
const batchActions: BatchAction[] = [
  {
    label: '批量激活',
    key: 'batchActivate',
    type: 'success',
    icon: 'Check',
    confirm: '确认激活选中的 {count} 个用户等级吗？',
    handler: async (selectedRows) => {
      console.log('批量激活用户等级:', selectedRows)
      await Promise.all(
        selectedRows.map((row) => UserService.updateUserLevel(row.id as number, { status: 1 }))
      )
    }
  },
  {
    label: '批量禁用',
    key: 'batchDisable',
    type: 'warning',
    icon: 'Close',
    confirm: '确认禁用选中的 {count} 个用户等级吗？',
    handler: async (selectedRows) => {
      console.log('批量禁用用户等级:', selectedRows)
      await Promise.all(
        selectedRows.map((row) => UserService.updateUserLevel(row.id as number, { status: 0 }))
      )
    }
  },
  {
    label: '批量延期',
    key: 'batchExtend',
    type: 'primary',
    icon: 'Clock',
    confirm: '确认延期选中的 {count} 个用户等级30天吗？',
    handler: async (selectedRows) => {
      console.log('批量延期用户等级:', selectedRows)
      const extendDate = new Date()
      extendDate.setDate(extendDate.getDate() + 30)
      await Promise.all(
        selectedRows.map((row) =>
          UserService.updateUserLevel(row.id as number, { expired_at: extendDate.toISOString() })
        )
      )
    }
  },
  {
    label: '批量导出',
    key: 'batchExport',
    type: 'info',
    icon: 'Download',
    handler: async (selectedRows) => {
      console.log('批量导出用户等级:', selectedRows)
      const exportData = selectedRows.map((row) => ({
        id: row.id as number,
        user_id: row.user_id as number,
        level_id: row.level_id as number,
        type: getTypeText(row.type as number),
        status: getStatusText(row.status as number),
        expired_at: row.expired_at as string,
        created_at: row.created_at as string
      }))
      console.log('导出数据:', exportData)
    }
  }
]

// 等级类型文本转换
export const getTypeText = (type: number) => {
  const typeMap: Record<number, string> = {
    1: '购买',
    2: '充值',
    3: '赠送'
  }
  return typeMap[type] || '购买'
}

// 等级状态文本转换
export const getStatusText = (status: number) => {
  const statusMap: Record<number, string> = {
    1: '激活',
    0: '禁用'
  }
  return statusMap[status] || '激活'
}

// 过期状态检查
export const getExpiredStatus = (expiredAt: string) => {
  const now = new Date()
  const expiredDate = new Date(expiredAt)
  return {
    isExpired: expiredDate < now,
    isExpiringSoon: expiredDate.getTime() - now.getTime() <= 7 * 24 * 60 * 60 * 1000, // 7天内过期
    remainingDays: Math.ceil((expiredDate.getTime() - now.getTime()) / (24 * 60 * 60 * 1000))
  }
}

// 导出配置
const exportConfig: ExportConfig = {
  enabled: true,
  filename: '用户等级数据导出',
  columns: ['id', 'user_id', 'level_id', 'type', 'status', 'expired_at', 'created_at'],
  formats: ['excel', 'csv'],
  maxRows: 1000
}

// 权限配置
const permissionConfig: PermissionConfig = {
  create: () => {
    return true
  },
  update: () => {
    // 已禁用的等级可以编辑
    return true
  },
  delete: () => {
    // 激活状态的等级需要谨慎删除
    return true
  },
  export: () => true,
  batchDelete: () => true
}

// 响应式配置
const responsiveConfig: ResponsiveConfig = {
  mobile: {
    hideColumns: ['data', 'created_at', 'updated_at'],
    cardView: false,
    breakpoint: 768
  }
}

// 安全配置
const securityConfig: SecurityConfig = {
  enableXssProtection: true,
  allowedTags: ['p', 'br', 'strong', 'em'],
  allowedAttributes: {
    div: ['class'],
    span: ['class']
  }
}

// 性能配置
const performanceConfig: PerformanceConfig = {
  enableCache: true,
  cacheTime: 5 * 60 * 1000, // 5分钟
  debounceTime: 500,
  maxCacheSize: 50,
  lazyLoad: true,
  virtualScroll: {
    enabled: true,
    itemHeight: 60,
    buffer: 10
  }
}

// 用户等级管理配置
export const levelTableConfig: CrudTableConfig = {
  title: '用户等级',
  addButtonText: '新增用户等级',
  deleteButtonText: '删除用户等级',
  deleteConfirmText: '确定要删除该用户等级吗？',
  batchDeleteConfirmText: '确定要删除选中的 {count} 条用户等级吗？',
  emptyHeight: '500px',

  initialSearchState: {
    user_id: '',
    level_id: '',
    type: '',
    status: '',
    expired_status: '',
    startTime: '',
    endTime: ''
  },

  searchFormItems: [
    {
      key: 'user_id',
      label: '用户ID',
      type: 'input',
      placeholder: '请输入用户ID'
    },
    {
      key: 'level_id',
      label: '等级ID',
      type: 'input',
      placeholder: '请输入等级ID'
    },
    {
      key: 'type',
      label: '等级类型',
      type: 'select',
      placeholder: '请选择等级类型',
      options: [
        { label: '购买', value: '1' },
        { label: '充值', value: '2' },
        { label: '赠送', value: '3' }
      ]
    },
    {
      key: 'status',
      label: '状态',
      type: 'select',
      placeholder: '请选择状态',
      options: [
        { label: '激活', value: '1' },
        { label: '禁用', value: '0' }
      ]
    },
    {
      key: 'expired_status',
      label: '过期状态',
      type: 'select',
      placeholder: '请选择过期状态',
      options: [
        { label: '正常', value: 'normal' },
        { label: '即将过期', value: 'expiring' },
        { label: '已过期', value: 'expired' }
      ]
    },
    {
      key: 'startTime',
      label: '开始时间',
      type: 'date',
      placeholder: '请选择开始时间'
    },
    {
      key: 'endTime',
      label: '结束时间',
      type: 'date',
      placeholder: '请选择结束时间'
    }
  ],

  tableColumns: [
    { type: 'selection', width: 55 },
    { label: 'ID', prop: 'id', width: 80, sortable: true },
    {
      label: '用户ID',
      prop: 'user_id',
      width: 100,
      sortable: true,
      columnType: 'copy'
    },
    {
      label: '用户信息',
      prop: 'user',
      minWidth: 150,
      useSlot: true,
      slotName: 'user'
    },
    {
      label: '等级ID',
      prop: 'level_id',
      width: 100,
      sortable: true,
      columnType: 'copy'
    },
    {
      label: '等级信息',
      prop: 'level',
      minWidth: 150,
      useSlot: true,
      slotName: 'level'
    },
    {
      label: '等级类型',
      prop: 'type',
      width: 100,
      useSlot: true,
      slotName: 'type'
    },
    {
      label: '状态',
      prop: 'status',
      width: 80,
      useSlot: true,
      slotName: 'status'
    },
    {
      label: '过期时间',
      prop: 'expired_at',
      minWidth: 160,
      sortable: true,
      columnType: 'date',
      useSlot: true,
      slotName: 'expiredAt'
    },
    {
      label: '等级数据',
      prop: 'data',
      minWidth: 200,
      useSlot: true,
      slotName: 'levelData'
    },
    {
      label: '创建时间',
      prop: 'created_at',
      minWidth: 160,
      sortable: true,
      columnType: 'date'
    },
    {
      label: '操作',
      width: 250,
      fixed: 'right',
      useSlot: true,
      slotName: 'actions'
    }
  ],

  apiService: createLevelApiService(),

  // 分页参数映射
  paginationKey: {
    current: 'page',
    size: 'pageSize'
  },

  dialog: {
    title: {
      add: '添加用户等级',
      edit: '编辑用户等级'
    },
    width: '800px',
    formItems: [
      {
        label: '用户ID',
        prop: 'user_id',
        type: 'input',
        required: true,
        config: {
          placeholder: '请输入用户ID'
        },
        rules: [{ required: true, message: '请输入用户ID', trigger: 'blur' }],
        span: 12
      },
      {
        label: '等级ID',
        prop: 'level_id',
        type: 'input',
        required: true,
        config: {
          placeholder: '请输入等级ID'
        },
        rules: [{ required: true, message: '请输入等级ID', trigger: 'blur' }],
        span: 12
      },
      {
        label: '等级类型',
        prop: 'type',
        type: 'select',
        required: true,
        options: [
          { label: '购买', value: 1 },
          { label: '充值', value: 2 },
          { label: '赠送', value: 3 }
        ],
        span: 12
      },
      {
        label: '状态',
        prop: 'status',
        type: 'select',
        required: true,
        options: [
          { label: '激活', value: 1 },
          { label: '禁用', value: 0 }
        ],
        span: 12
      },
      {
        label: '过期时间',
        prop: 'expired_at',
        type: 'input',
        required: true,
        config: {
          placeholder: '请选择过期时间'
        },
        rules: [{ required: true, message: '请选择过期时间', trigger: 'blur' }],
        span: 24
      },
      {
        label: '等级数据',
        prop: 'data',
        type: 'textarea',
        config: {
          placeholder: '请输入等级数据（JSON格式）',
          rows: 6
        },
        span: 24
      }
    ],
    initialFormData: {
      user_id: '',
      level_id: '',
      type: 1,
      status: 1,
      expired_at: '',
      data: ''
    }
  },

  // 功能配置
  batchActions,
  export: exportConfig,
  permissions: permissionConfig,
  responsive: responsiveConfig,
  security: securityConfig,
  performance: performanceConfig,

  // 状态切换配置
  hasStatusSwitch: true,
  statusField: 'status',
  statusActiveValue: 1,
  statusInactiveValue: 0,

  // 操作权限
  allowBatchDelete: true
}
