<script setup lang="ts">
  import { ref } from 'vue'
  import ArtCrudTable from '@/components/core/tables/ArtCrudTable.vue'
  import ArtButtonTable from '@/components/core/forms/art-button-table/index.vue'
  import {
    swapTableConfig,
    getSwapTypeText,
    getSwapStatusText,
    getStatusTagClass,
    formatAmount,
    calculateRate,
    calculateTotalFee
  } from './swap'
  import { UserSwapService } from '@/api/walletSwap'

  defineOptions({ name: 'UserSwap' })

  const crudTableRef = ref()
  const componentKey = ref(Date.now())

  // 发送用户信息渲染
  const renderSendUser = (row: any) => {
    if (row.send_user) {
      return {
        id: row.send_user.id,
        username: row.send_user.username,
        nickname: row.send_user.nickname,
        avatar: row.send_user.avatar
      }
    }
    return { id: row.send_user_id, username: '未知用户', nickname: '', avatar: '' }
  }

  // 接收用户信息渲染
  const renderReceiveUser = (row: any) => {
    if (row.receive_user) {
      return {
        id: row.receive_user.id,
        username: row.receive_user.username,
        nickname: row.receive_user.nickname,
        avatar: row.receive_user.avatar
      }
    }
    return { id: row.receive_user_id, username: '未知用户', nickname: '', avatar: '' }
  }

  // 发送资产信息渲染
  const renderSendAsset = (row: any) => {
    if (row.send_asset) {
      return {
        id: row.send_asset.id,
        name: row.send_asset.name,
        symbol: row.send_asset.symbol,
        icon: row.send_asset.icon,
        precision: row.send_asset.precision || 4
      }
    }
    return { id: row.send_asset_id, name: '未知资产', symbol: 'UNKNOWN', icon: '', precision: 4 }
  }

  // 接收资产信息渲染
  const renderReceiveAsset = (row: any) => {
    if (row.receive_asset) {
      return {
        id: row.receive_asset.id,
        name: row.receive_asset.name,
        symbol: row.receive_asset.symbol,
        icon: row.receive_asset.icon,
        precision: row.receive_asset.precision || 4
      }
    }
    return { id: row.receive_asset_id, name: '未知资产', symbol: 'UNKNOWN', icon: '', precision: 4 }
  }

  // 发送金额渲染
  const renderSendAmount = (row: any) => {
    const amount = formatAmount(row.send_amount, renderSendAsset(row).precision)
    return {
      amount,
      symbol: renderSendAsset(row).symbol
    }
  }

  // 接收金额渲染
  const renderReceiveAmount = (row: any) => {
    const amount = formatAmount(row.receive_amount, renderReceiveAsset(row).precision)
    return {
      amount,
      symbol: renderReceiveAsset(row).symbol
    }
  }

  // 汇率渲染
  const renderRate = (row: any) => {
    const rate = formatAmount(row.rate, 4)
    const calculatedRate = calculateRate(row.send_amount, row.receive_amount)
    return {
      rate,
      calculatedRate,
      sendSymbol: renderSendAsset(row).symbol,
      receiveSymbol: renderReceiveAsset(row).symbol
    }
  }

  // 手续费渲染
  const renderFee = (row: any) => {
    const fixedFee = formatAmount(row.fixed_fee, renderSendAsset(row).precision)
    const rateFee = formatAmount(row.rate_fee, 3)
    const totalFee = calculateTotalFee(row.send_amount, row.fixed_fee, row.rate_fee)
    return {
      fixedFee,
      rateFee,
      totalFee: formatAmount(totalFee, renderSendAsset(row).precision),
      symbol: renderSendAsset(row).symbol
    }
  }

  // 闪兑状态渲染
  const renderStatus = (row: any) => {
    const statusText = getSwapStatusText(row.status)
    const statusClass = getStatusTagClass(row.status)
    return { text: statusText, class: statusClass }
  }

  // 查看闪兑详情
  const viewSwapDetail = (row: any) => {
    console.log('查看闪兑详情:', row)
    // 这里可以实现跳转到闪兑详情页面的逻辑
  }

  // 完成闪兑
  const completeSwap = (row: any) => {
    console.log('完成闪兑:', row.id)
    UserSwapService.completeSwap(row.id, '手动完成').then(() => {
      crudTableRef.value?.refreshData()
    })
  }

  // 取消闪兑
  const cancelSwap = (row: any) => {
    console.log('取消闪兑:', row.id)
    UserSwapService.cancelSwap(row.id, '手动取消').then(() => {
      crudTableRef.value?.refreshData()
    })
  }

  // 设为失败
  const failSwap = (row: any) => {
    console.log('设为失败:', row.id)
    UserSwapService.failSwap(row.id, '手动设为失败').then(() => {
      crudTableRef.value?.refreshData()
    })
  }

  // 查看用户闪兑历史
  const viewUserSwapHistory = (userId: number, type: 'send' | 'receive') => {
    console.log('查看用户闪兑历史:', userId, type)
    // 这里可以实现跳转到用户闪兑历史页面的逻辑
  }

  // 查看资产闪兑记录
  const viewAssetSwapHistory = (assetId: number) => {
    console.log('查看资产闪兑记录:', assetId)
    // 这里可以实现跳转到资产闪兑记录页面的逻辑
  }

  // 处理下拉菜单命令
  const handleDropdownCommand = (command: string, row: any) => {
    switch (command) {
      case 'complete':
        completeSwap(row)
        break
      case 'cancel':
        cancelSwap(row)
        break
      case 'fail':
        failSwap(row)
        break
      case 'send-user-history':
        viewUserSwapHistory(row.send_user_id, 'send')
        break
      case 'receive-user-history':
        viewUserSwapHistory(row.receive_user_id, 'receive')
        break
      case 'send-asset-history':
        viewAssetSwapHistory(row.send_asset_id)
        break
      case 'receive-asset-history':
        viewAssetSwapHistory(row.receive_asset_id)
        break
      default:
        console.log('未知命令:', command)
    }
  }
</script>

<template>
  <ArtCrudTable ref="crudTableRef" :key="componentKey" :config="swapTableConfig">
    <!-- 发送用户列 -->
    <template #sendUser="{ row }">
      <div class="user-info">
        <div class="user-avatar" v-if="renderSendUser(row).avatar">
          <img
            :src="renderSendUser(row).avatar"
            :alt="renderSendUser(row).username"
            class="avatar-img"
          />
        </div>
        <div class="user-details">
          <div class="user-id">ID: {{ renderSendUser(row).id }}</div>
          <div class="user-name">{{ renderSendUser(row).username }}</div>
          <div v-if="renderSendUser(row).nickname" class="user-nickname">
            ({{ renderSendUser(row).nickname }})
          </div>
        </div>
        <div class="user-badge send-badge">发送</div>
      </div>
    </template>

    <!-- 接收用户列 -->
    <template #receiveUser="{ row }">
      <div class="user-info">
        <div class="user-avatar" v-if="renderReceiveUser(row).avatar">
          <img
            :src="renderReceiveUser(row).avatar"
            :alt="renderReceiveUser(row).username"
            class="avatar-img"
          />
        </div>
        <div class="user-details">
          <div class="user-id">ID: {{ renderReceiveUser(row).id }}</div>
          <div class="user-name">{{ renderReceiveUser(row).username }}</div>
          <div v-if="renderReceiveUser(row).nickname" class="user-nickname">
            ({{ renderReceiveUser(row).nickname }})
          </div>
        </div>
        <div class="user-badge receive-badge">接收</div>
      </div>
    </template>

    <!-- 发送资产列 -->
    <template #sendAsset="{ row }">
      <div class="asset-info">
        <div class="asset-icon" v-if="renderSendAsset(row).icon">
          <img
            :src="renderSendAsset(row).icon"
            :alt="renderSendAsset(row).symbol"
            class="asset-icon-img"
          />
        </div>
        <div class="asset-details">
          <div class="asset-symbol">{{ renderSendAsset(row).symbol }}</div>
          <div class="asset-name">{{ renderSendAsset(row).name }}</div>
        </div>
        <div class="asset-badge send-asset-badge">发送</div>
      </div>
    </template>

    <!-- 接收资产列 -->
    <template #receiveAsset="{ row }">
      <div class="asset-info">
        <div class="asset-icon" v-if="renderReceiveAsset(row).icon">
          <img
            :src="renderReceiveAsset(row).icon"
            :alt="renderReceiveAsset(row).symbol"
            class="asset-icon-img"
          />
        </div>
        <div class="asset-details">
          <div class="asset-symbol">{{ renderReceiveAsset(row).symbol }}</div>
          <div class="asset-name">{{ renderReceiveAsset(row).name }}</div>
        </div>
        <div class="asset-badge receive-asset-badge">接收</div>
      </div>
    </template>

    <!-- 发送金额列 -->
    <template #sendAmount="{ row }">
      <div class="amount-container send-amount">
        <span class="amount-value">{{ renderSendAmount(row).amount }}</span>
        <span class="amount-symbol">{{ renderSendAmount(row).symbol }}</span>
        <div class="amount-direction">
          <el-icon class="send-icon"><ArrowLeft /></el-icon>
          <span class="direction-text">发送</span>
        </div>
      </div>
    </template>

    <!-- 接收金额列 -->
    <template #receiveAmount="{ row }">
      <div class="amount-container receive-amount">
        <span class="amount-value">{{ renderReceiveAmount(row).amount }}</span>
        <span class="amount-symbol">{{ renderReceiveAmount(row).symbol }}</span>
        <div class="amount-direction">
          <el-icon class="receive-icon"><ArrowRight /></el-icon>
          <span class="direction-text">接收</span>
        </div>
      </div>
    </template>

    <!-- 汇率列 -->
    <template #rate="{ row }">
      <div class="rate-container">
        <div class="rate-value">{{ renderRate(row).rate }}</div>
        <div class="rate-formula">
          1 {{ renderRate(row).sendSymbol }} = {{ renderRate(row).calculatedRate }}
          {{ renderRate(row).receiveSymbol }}
        </div>
        <div class="rate-badge">汇率</div>
      </div>
    </template>

    <!-- 手续费列 -->
    <template #fee="{ row }">
      <div class="fee-container">
        <div class="fee-item">
          <span class="fee-label">固定:</span>
          <span class="fee-value">{{ renderFee(row).fixedFee }}</span>
        </div>
        <div class="fee-item">
          <span class="fee-label">比例:</span>
          <span class="fee-value">{{ renderFee(row).rateFee }}%</span>
        </div>
        <div class="fee-total">
          <span class="fee-label">总计:</span>
          <span class="fee-total-value">{{ renderFee(row).totalFee }}</span>
          <span class="fee-symbol">{{ renderFee(row).symbol }}</span>
        </div>
      </div>
    </template>

    <!-- 闪兑类型列 -->
    <template #swapType="{ row }">
      <span class="type-tag">{{ getSwapTypeText(row.type) }}</span>
    </template>

    <!-- 状态列 -->
    <template #status="{ row }">
      <span :class="['status-tag', renderStatus(row).class]">
        {{ renderStatus(row).text }}
      </span>
    </template>

    <!-- 操作列 -->
    <template #actions="{ row }">
      <div class="flex gap-2">
        <ArtButtonTable type="edit" @click="crudTableRef?.showDialog('edit', row)" />
        <ArtButtonTable type="delete" @click="crudTableRef?.deleteItem(row.id)" />
        <ArtButtonTable type="view" @click="viewSwapDetail(row)" />

        <!-- 状态操作按钮 -->
        <ArtButtonTable v-if="row.status === 1" type="add" @click="completeSwap(row)" />
        <ArtButtonTable v-if="row.status === 1" type="delete" @click="cancelSwap(row)" />

        <!-- 更多操作下拉菜单 -->
        <el-dropdown trigger="click" @command="(command) => handleDropdownCommand(command, row)">
          <ArtButtonTable type="more" />
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item v-if="row.status === 1" command="complete">
                <el-icon><CircleCheck /></el-icon>
                完成闪兑
              </el-dropdown-item>
              <el-dropdown-item v-if="row.status === 1" command="cancel">
                <el-icon><Close /></el-icon>
                取消闪兑
              </el-dropdown-item>
              <el-dropdown-item v-if="row.status === 1" command="fail" divided>
                <el-icon><CloseBold /></el-icon>
                设为失败
              </el-dropdown-item>
              <el-dropdown-item command="send-user-history" divided>
                <el-icon><User /></el-icon>
                发送用户历史
              </el-dropdown-item>
              <el-dropdown-item command="receive-user-history">
                <el-icon><User /></el-icon>
                接收用户历史
              </el-dropdown-item>
              <el-dropdown-item command="send-asset-history" divided>
                <el-icon><Coin /></el-icon>
                发送资产记录
              </el-dropdown-item>
              <el-dropdown-item command="receive-asset-history">
                <el-icon><Coin /></el-icon>
                接收资产记录
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </template>
  </ArtCrudTable>
</template>

<style lang="scss" scoped>
  .flex {
    display: flex;
  }

  .gap-2 {
    gap: 8px;
  }

  // 用户信息样式
  .user-info {
    position: relative;
    display: flex;
    gap: 8px;
    align-items: center;

    .user-avatar {
      .avatar-img {
        width: 32px;
        height: 32px;
        object-fit: cover;
        border: 1px solid #e0e0e0;
        border-radius: 50%;
      }
    }

    .user-details {
      .user-id {
        margin-bottom: 2px;
        font-size: 12px;
        color: #999;
      }

      .user-name {
        font-weight: 500;
        color: #333;
      }

      .user-nickname {
        margin-top: 2px;
        font-size: 12px;
        color: #666;
      }
    }

    .user-badge {
      position: absolute;
      top: -4px;
      right: -4px;
      padding: 2px 6px;
      font-size: 10px;
      font-weight: 500;
      border-radius: 10px;

      &.send-badge {
        color: #ff4d4f;
        background-color: #fff2f0;
        border: 1px solid #ffb3b3;
      }

      &.receive-badge {
        color: #52c41a;
        background-color: #f6ffed;
        border: 1px solid #b7eb8f;
      }
    }
  }

  // 资产信息样式
  .asset-info {
    position: relative;
    display: flex;
    gap: 8px;
    align-items: center;

    .asset-icon {
      .asset-icon-img {
        width: 24px;
        height: 24px;
        object-fit: cover;
        border-radius: 50%;
      }
    }

    .asset-details {
      .asset-symbol {
        font-size: 14px;
        font-weight: 600;
        color: #333;
      }

      .asset-name {
        margin-top: 2px;
        font-size: 12px;
        color: #666;
      }
    }

    .asset-badge {
      position: absolute;
      top: -4px;
      right: -4px;
      padding: 2px 6px;
      font-size: 10px;
      font-weight: 500;
      border-radius: 10px;

      &.send-asset-badge {
        color: #f57c00;
        background-color: #fff3e0;
        border: 1px solid #ffcc02;
      }

      &.receive-asset-badge {
        color: #1890ff;
        background-color: #e6f7ff;
        border: 1px solid #91d5ff;
      }
    }
  }

  // 金额容器样式
  .amount-container {
    display: flex;
    flex-direction: column;
    align-items: flex-end;

    .amount-value {
      font-family: Monaco, Menlo, monospace;
      font-size: 14px;
      font-weight: 600;
    }

    .amount-symbol {
      margin-top: 2px;
      font-size: 11px;
      color: #8c8c8c;
    }

    .amount-direction {
      display: flex;
      gap: 4px;
      align-items: center;
      margin-top: 4px;

      .direction-text {
        font-size: 10px;
        font-weight: 500;
      }
    }

    &.send-amount {
      .amount-value {
        color: #ff4d4f;
      }

      .amount-direction {
        color: #ff4d4f;

        .send-icon {
          font-size: 12px;
        }
      }
    }

    &.receive-amount {
      .amount-value {
        color: #52c41a;
      }

      .amount-direction {
        color: #52c41a;

        .receive-icon {
          font-size: 12px;
        }
      }
    }
  }

  // 汇率容器样式
  .rate-container {
    display: flex;
    flex-direction: column;
    align-items: flex-end;

    .rate-value {
      font-family: Monaco, Menlo, monospace;
      font-size: 14px;
      font-weight: 600;
      color: #1890ff;
    }

    .rate-formula {
      margin-top: 2px;
      font-size: 11px;
      line-height: 1.2;
      color: #666;
      text-align: right;
    }

    .rate-badge {
      padding: 2px 6px;
      margin-top: 4px;
      font-size: 10px;
      font-weight: 500;
      color: #1890ff;
      background-color: #e6f7ff;
      border: 1px solid #91d5ff;
      border-radius: 10px;
    }
  }

  // 手续费容器样式
  .fee-container {
    display: flex;
    flex-direction: column;
    gap: 2px;

    .fee-item {
      display: flex;
      justify-content: space-between;
      font-size: 12px;

      .fee-label {
        min-width: 35px;
        color: #666;
      }

      .fee-value {
        font-family: Monaco, Menlo, monospace;
        font-weight: 500;
        color: #faad14;
      }
    }

    .fee-total {
      display: flex;
      gap: 4px;
      align-items: center;
      justify-content: space-between;
      padding-top: 4px;
      margin-top: 4px;
      font-size: 12px;
      border-top: 1px solid #f0f0f0;

      .fee-label {
        color: #666;
      }

      .fee-total-value {
        font-family: Monaco, Menlo, monospace;
        font-weight: 600;
        color: #faad14;
      }

      .fee-symbol {
        font-size: 10px;
        color: #8c8c8c;
      }
    }
  }

  // 类型标签样式
  .type-tag {
    padding: 4px 8px;
    font-size: 12px;
    font-weight: 500;
    color: #722ed1;
    background-color: #f9f0ff;
    border: 1px solid #d3adf7;
    border-radius: 4px;
  }

  // 状态标签样式
  .status-tag {
    padding: 4px 8px;
    font-size: 12px;
    font-weight: 500;
    border-radius: 4px;

    &.status-pending {
      color: #faad14;
      background-color: #fffbe6;
      border: 1px solid #ffe58f;
    }

    &.status-completed {
      color: #52c41a;
      background-color: #f6ffed;
      border: 1px solid #b7eb8f;
    }

    &.status-canceled {
      color: #8c8c8c;
      background-color: #f5f5f5;
      border: 1px solid #d9d9d9;
    }

    &.status-failed {
      color: #ff4d4f;
      background-color: #fff2f0;
      border: 1px solid #ffb3b3;
    }

    &.status-unknown {
      color: #8c8c8c;
      background-color: #f5f5f5;
      border: 1px solid #d9d9d9;
    }
  }

  // 悬停效果
  .amount-container:hover,
  .rate-container:hover,
  .fee-container:hover {
    background-color: #fafafa;
    border-radius: 4px;
    transition: all 0.3s ease;
  }

  // 特殊高亮
  .user-info:hover .user-badge,
  .asset-info:hover .asset-badge {
    transition: all 0.2s ease;
    transform: scale(1.1);
  }

  // 操作按钮区域

  /* 已有 .flex 定义，此处不再重复定义，若需换行可在需要处单独添加样式类 */

  // 响应式设计
  @media (width <= 768px) {
    .user-info,
    .asset-info {
      flex-direction: column;
      gap: 4px;
      align-items: flex-start;
    }

    .amount-container,
    .rate-container {
      align-items: flex-start;
    }

    .user-badge,
    .asset-badge {
      position: static;
      margin-top: 4px;
    }
  }
</style>
