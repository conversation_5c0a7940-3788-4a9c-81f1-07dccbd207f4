<script setup lang="ts">
  import { ref, computed, watch, h } from 'vue'
  import {
    ElDialog,
    ElForm,
    ElFormItem,
    ElInput,
    ElInputNumber,
    ElSelect,
    ElOption,
    ElRow,
    ElCol,
    ElButton,
    type FormInstance,
    type FormRules
  } from 'element-plus'
  import { useWindowSize } from '@vueuse/core'

  // @ts-expect-error - Vue component without type declaration
  import ArtUpload from '@/components/core/upload/index.vue'
  import ArtWangEditor from '@/components/core/forms/art-wang-editor/index.vue'

  defineOptions({ name: 'ArtCrudDialog' })

  // 对话框表单字段类型
  export interface DialogFormItem<T = Record<string, unknown>> {
    label: string
    prop: string
    type: 'input' | 'textarea' | 'number' | 'select' | 'upload' | 'richtext'
    required?: boolean
    rules?: any[]
    config?: {
      placeholder?: string
      maxlength?: number | string
      disabled?: boolean
      min?: number
      max?: number
      step?: number
      rows?: number
      clearable?: boolean
      height?: string
      mode?: 'default' | 'simple'
      toolbarKeys?: string[]
      excludeKeys?: string[]
      uploadConfig?: {
        maxFileSize?: number
        maxNumberOfFiles?: number
        server?: string
      }
      accept?: string
      maxSize?: number
      triggerText?: string
      tip?: string
      [key: string]: unknown
    }
    options?:
      | Array<{ label: string; value: any; disabled?: boolean }>
      | (() => Array<{ label: string; value: any; disabled?: boolean }>)
    span?: number
    show?: boolean | ((formData: T, type: 'add' | 'edit') => boolean)
  }

  // 对话框配置接口
  interface DialogConfig {
    title?: {
      add: string
      edit: string
    }
    width?: string
    formItems: DialogFormItem[]
    initialFormData?: Record<string, any>
    rules?: FormRules
  }

  interface Props {
    visible: boolean
    type: 'add' | 'edit'
    dialogConfig?: DialogConfig
    currentRow?: Record<string, unknown> | null
    loading?: boolean
    entityName?: string
  }

  interface Emits {
    (e: 'update:visible', value: boolean): void
    (e: 'submit', data: { formData: Record<string, unknown>; type: 'add' | 'edit' }): void
    (e: 'cancel'): void
  }

  const props = withDefaults(defineProps<Props>(), {
    loading: false,
    entityName: '数据'
  })

  const emits = defineEmits<Emits>()

  // ==================== 响应式状态 ====================

  const { width } = useWindowSize()
  const dialogFormRef = ref<FormInstance>()
  const dialogFormData = ref<Record<string, unknown>>({})
  const submitLoading = ref(false)

  // 计算属性
  const dialogVisible = computed({
    get: () => props.visible,
    set: (value) => emits('update:visible', value)
  })

  // 响应式判断
  const isMobile = computed(() => width.value < 768)
  const isTablet = computed(() => width.value >= 768 && width.value < 1024)

  // 响应式对话框宽度
  const responsiveDialogWidth = computed(() => {
    if (isMobile.value) return '95%'
    if (isTablet.value) return '80%'
    return props.dialogConfig?.width || '600px'
  })

  // 响应式表单标签宽度
  const responsiveLabelWidth = computed(() => {
    if (isMobile.value) return '80px'
    if (isTablet.value) return '100px'
    return '120px'
  })

  // 响应式栅格间距
  const responsiveGutter = computed(() => {
    if (isMobile.value) return 16
    return 20
  })

  // 响应式表单字段span
  const getResponsiveSpan = (item: DialogFormItem) => {
    if (isMobile.value) {
      // 移动端：大多数字段占满全宽，除了一些特殊的小字段
      if (item.type === 'number' && item.span === 12) return 24
      return 24
    }
    if (isTablet.value) {
      // 平板端：适当调整span
      if (item.span === 12) return 24
      return item.span || 24
    }
    // 桌面端：使用原始span
    return item.span || 12
  }

  const dialogTitle = computed(() => {
    if (!props.dialogConfig) return `${props.type === 'add' ? '新增' : '编辑'}${props.entityName}`

    return (
      props.dialogConfig.title?.[props.type] ||
      `${props.type === 'add' ? '新增' : '编辑'}${props.entityName}`
    )
  })

  // ==================== 工具函数 ====================

  // 初始化对话框表单数据
  const initDialogFormData = (): void => {
    if (!props.dialogConfig) return

    const initialData = props.dialogConfig.initialFormData || {}
    dialogFormData.value = { ...initialData }

    props.dialogConfig.formItems.forEach((item) => {
      const prop = item.prop as string
      if (!(prop in dialogFormData.value)) {
        if (item.type === 'number') {
          dialogFormData.value[prop] = 0
        } else if (item.type === 'select' && item.options) {
          const options = typeof item.options === 'function' ? item.options() : item.options
          dialogFormData.value[prop] = options[0]?.value || ''
        } else if (item.type === 'richtext') {
          dialogFormData.value[prop] = ''
        } else {
          dialogFormData.value[prop] = ''
        }
      }
    })

    // 如果是编辑模式，填充当前行数据
    if (props.type === 'edit' && props.currentRow) {
      Object.keys(dialogFormData.value).forEach((key) => {
        if (props.currentRow![key] !== undefined) {
          dialogFormData.value[key] = props.currentRow![key]
        }
      })
    }
  }

  // 获取对话框表单验证规则
  const getDialogFormRules = (): FormRules => {
    if (!props.dialogConfig) return {}

    const rules: FormRules = {}

    props.dialogConfig.formItems.forEach((item) => {
      const prop = item.prop as string
      if (item.rules) {
        rules[prop] = item.rules
      } else if (item.required) {
        rules[prop] = [
          {
            required: true,
            message: `请输入${item.label}`,
            trigger: item.type === 'select' ? 'change' : 'blur'
          }
        ]
      }
    })

    return { ...rules, ...(props.dialogConfig.rules || {}) }
  }

  // 渲染表单字段
  const renderFormItem = (item: DialogFormItem) => {
    const commonProps = {
      modelValue: dialogFormData.value[item.prop as string],
      'onUpdate:modelValue': (value: unknown) => {
        dialogFormData.value[item.prop as string] = value
      },
      placeholder: item.config?.placeholder || `请输入${item.label}`,
      disabled: item.config?.disabled || props.loading,
      clearable: item.config?.clearable !== false,
      ...item.config
    }

    switch (item.type) {
      case 'textarea':
        return h(ElInput as any, {
          ...commonProps,
          type: 'textarea',
          rows: item.config?.rows || 4
        })
      case 'number':
        return h(ElInputNumber, {
          ...commonProps,
          min: item.config?.min,
          max: item.config?.max,
          step: item.config?.step || 1,
          style: { width: '100%' }
        })
      case 'select': {
        const options = typeof item.options === 'function' ? item.options() : item.options || []
        return h(ElSelect as any, commonProps, {
          default: () =>
            options.map((option) =>
              h(ElOption, {
                key: option.value as string,
                label: option.label,
                value: option.value,
                disabled: option.disabled
              })
            )
        })
      }
      case 'upload':
        return h(ArtUpload, {
          modelValue: dialogFormData.value[item.prop as string],
          'onUpdate:modelValue': (value: unknown) => {
            dialogFormData.value[item.prop as string] = value
          },
          singleFile: true,
          accept: item.config?.accept || 'image/*',
          maxSize: item.config?.maxSize || 10,
          triggerText: item.config?.triggerText || '选择文件',
          tip: item.config?.tip || '',
          disabled: item.config?.disabled || props.loading,
          ...item.config
        })
      case 'richtext':
        return h(ArtWangEditor, {
          modelValue: (dialogFormData.value[item.prop as string] as string) || '',
          'onUpdate:modelValue': (value: string) => {
            dialogFormData.value[item.prop as string] = value
          },
          height: item.config?.height || '300px',
          mode: item.config?.mode || 'default',
          placeholder: item.config?.placeholder || `请输入${item.label}`,
          toolbarKeys: item.config?.toolbarKeys,
          excludeKeys: item.config?.excludeKeys || ['fontFamily'],
          uploadConfig: item.config?.uploadConfig,
          ...item.config
        })
      default:
        return h(ElInput as any, commonProps)
    }
  }

  // ==================== 事件处理函数 ====================

  // 对话框提交
  const handleDialogSubmit = async (): Promise<void> => {
    if (!dialogFormRef.value || !props.dialogConfig) return

    await dialogFormRef.value.validate(async (valid) => {
      if (valid) {
        submitLoading.value = true
        try {
          emits('submit', {
            formData: { ...dialogFormData.value },
            type: props.type
          })
        } finally {
          submitLoading.value = false
        }
      }
    })
  }

  // 对话框取消
  const handleDialogCancel = (): void => {
    dialogFormRef.value?.resetFields()
    emits('cancel')
  }

  // 关闭对话框
  const handleDialogClose = (): void => {
    handleDialogCancel()
  }

  // ==================== 监听器 ====================

  // 监听对话框显示状态，重新初始化表单数据
  watch(
    () => props.visible,
    (visible) => {
      if (visible) {
        initDialogFormData()
      }
    },
    { immediate: true }
  )

  // 监听当前行数据变化
  watch(
    () => props.currentRow,
    () => {
      if (props.visible) {
        initDialogFormData()
      }
    }
  )

  // 监听loading状态
  watch(
    () => props.loading,
    (loading) => {
      submitLoading.value = loading
    }
  )

  // ==================== 暴露方法 ====================

  defineExpose({
    resetFields: () => dialogFormRef.value?.resetFields(),
    validate: () => dialogFormRef.value?.validate(),
    clearValidate: () => dialogFormRef.value?.clearValidate()
  })
</script>

<template>
  <ElDialog
    v-model="dialogVisible"
    :title="dialogTitle"
    :width="responsiveDialogWidth"
    align-center
    :close-on-click-modal="false"
    :lock-scroll="false"
    :append-to-body="true"
    :modal-class="'art-dialog-modal'"
    @close="handleDialogClose"
  >
    <ElForm
      v-if="dialogConfig?.formItems && dialogConfig.formItems.length > 0"
      ref="dialogFormRef"
      :model="dialogFormData"
      :rules="getDialogFormRules()"
      :label-width="responsiveLabelWidth"
      style="padding: 0 20px"
    >
      <ElRow :gutter="responsiveGutter">
        <ElCol
          v-for="item in dialogConfig.formItems"
          :key="item.prop"
          :span="getResponsiveSpan(item)"
          v-show="
            typeof item.show === 'function' ? item.show(dialogFormData, type) : item.show !== false
          "
        >
          <ElFormItem :label="item.label" :prop="item.prop">
            <component :is="renderFormItem(item)" />
          </ElFormItem>
        </ElCol>
      </ElRow>
    </ElForm>

    <div v-else style="padding: 40px; color: #999; text-align: center">暂无表单配置</div>

    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="handleDialogCancel">取消</ElButton>
        <ElButton type="primary" @click="handleDialogSubmit" :loading="submitLoading">
          {{ type === 'add' ? '添加' : '更新' }}
        </ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<style lang="scss" scoped>
  .dialog-footer {
    display: flex;
    gap: 10px;
    justify-content: flex-end;

    // 移动端按钮布局优化
    @media (width <= 767px) {
      flex-direction: column-reverse;
      gap: 12px;

      .el-button {
        width: 100%;
        margin: 0;
      }
    }

    // 平板端按钮优化
    @media (width >= 768px) and (width <= 1023px) {
      gap: 12px;

      .el-button {
        min-width: 80px;
      }
    }
  }

  // 移动端对话框内容优化
  @media (width <= 767px) {
    :deep(.el-dialog) {
      margin: 8vh auto 2vh !important;

      .el-dialog__header {
        padding: 16px 20px 12px;

        .el-dialog__title {
          font-size: 16px;
          line-height: 1.4;
        }

        .el-dialog__headerbtn {
          top: 16px;
          right: 16px;

          .el-dialog__close {
            font-size: 18px;
          }
        }
      }

      .el-dialog__body {
        max-height: 65vh;
        padding: 16px 16px 20px;
        overflow-y: auto;

        .el-form {
          padding: 0 !important;

          .el-form-item {
            margin-bottom: 18px;

            .el-form-item__label {
              padding-right: 8px;
              font-size: 14px;
              line-height: 1.4;
            }

            .el-form-item__content {
              .el-input,
              .el-select,
              .el-input-number,
              .el-textarea {
                width: 100%;

                .el-input__wrapper,
                .el-textarea__inner {
                  font-size: 16px; // 防止iOS自动缩放
                }
              }

              .el-input-number {
                .el-input__wrapper {
                  width: 100%;
                }
              }
            }
          }
        }
      }

      .el-dialog__footer {
        padding: 12px 16px 16px;
      }
    }
  }

  // 平板端优化
  @media (width >= 768px) and (width <= 1023px) {
    :deep(.el-dialog) {
      margin: 6vh auto !important;

      .el-dialog__body {
        max-height: 70vh;
        padding: 20px 20px 24px;

        .el-form {
          padding: 0 !important;

          .el-form-item {
            margin-bottom: 20px;
          }
        }
      }
    }
  }

  // 表单字段类型特殊处理
  :deep(.el-form-item) {
    // 富文本编辑器在移动端的优化
    &:has(.wang-editor) {
      @media (width <= 767px) {
        .el-form-item__content {
          .wang-editor {
            .w-e-toolbar {
              flex-wrap: wrap;

              .w-e-toolbar-item {
                margin: 2px 1px;
              }
            }
          }
        }
      }
    }

    // 上传组件在移动端的优化
    &:has(.art-upload) {
      @media (width <= 767px) {
        .el-form-item__content {
          .art-upload {
            .el-upload {
              width: 100%;
            }
          }
        }
      }
    }
  }
</style>
