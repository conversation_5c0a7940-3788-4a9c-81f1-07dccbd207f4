import request from '@/utils/http'

/**
 * 用户转账管理服务类
 * 对应的Go结构体：
 * - UserTransfer: 用户转账主表结构
 * - 转账类型常量: 内部转账(1)
 * - 转账状态常量: 待处理(1)、已完成(2)、已取消(3)、已失败(4)
 */
export class UserTransferService {
  private static baseUrl = '/wallet/transfer'

  // 转账类型常量
  static readonly TRANSFER_TYPE_INTERNAL = 1 // 内部转账

  // 转账状态常量
  static readonly TRANSFER_STATUS_PENDING = 1 // 待处理
  static readonly TRANSFER_STATUS_COMPLETED = 2 // 已完成
  static readonly TRANSFER_STATUS_CANCELED = 3 // 已取消
  static readonly TRANSFER_STATUS_FAILED = 4 // 已失败

  // ==================== 基础 CRUD ====================

  // 获取用户转账列表
  static getUserTransferList(params?: any) {
    return request.get<any>({
      url: this.baseUrl,
      params
    })
  }

  // 获取用户转账详情
  static getUserTransferDetail(id: number) {
    return request.get<any>({
      url: `${this.baseUrl}/${id}`
    })
  }

  // 创建用户转账
  static createUserTransfer(data: any) {
    return request.post<any>({
      url: this.baseUrl,
      data
    })
  }

  // 更新用户转账
  static updateUserTransfer(id: number, data: any) {
    return request.put<any>({
      url: `${this.baseUrl}/${id}`,
      data
    })
  }

  // 删除用户转账
  static deleteUserTransfer(id: number) {
    return request.del<any>({
      url: `${this.baseUrl}/${id}`
    })
  }

  // 批量删除用户转账
  static batchDeleteUserTransfers(ids: number[]) {
    return request.del<any>({
      url: `${this.baseUrl}/batch`,
      data: { ids }
    })
  }

  // ==================== 状态操作 ====================

  // 完成转账
  static completeTransfer(id: number, reason?: string) {
    return request.put<any>({
      url: `${this.baseUrl}/${id}/complete`,
      data: { reason }
    })
  }

  // 取消转账
  static cancelTransfer(id: number, reason: string) {
    return request.put<any>({
      url: `${this.baseUrl}/${id}/cancel`,
      data: { reason }
    })
  }

  // 设置转账失败
  static failTransfer(id: number, reason: string) {
    return request.put<any>({
      url: `${this.baseUrl}/${id}/fail`,
      data: { reason }
    })
  }

  // 批量更新状态
  static batchUpdateStatus(ids: number[], status: number, reason?: string) {
    return request.put<any>({
      url: `${this.baseUrl}/batch/status`,
      data: { ids, status, reason }
    })
  }

  // ==================== 用户转账查询 ====================

  // 获取用户的发送转账
  static getUserSendTransfers(userId: number, params?: any) {
    return request.get<any>({
      url: `${this.baseUrl}/user/${userId}/send`,
      params
    })
  }

  // 获取用户的接收转账
  static getUserReceiveTransfers(userId: number, params?: any) {
    return request.get<any>({
      url: `${this.baseUrl}/user/${userId}/receive`,
      params
    })
  }

  // 获取用户的全部转账
  static getUserAllTransfers(userId: number, params?: any) {
    return request.get<any>({
      url: `${this.baseUrl}/user/${userId}/all`,
      params
    })
  }

  // ==================== 资产转账查询 ====================

  // 获取资产相关的转账
  static getAssetTransfers(assetId: number, params?: any) {
    return request.get<any>({
      url: `${this.baseUrl}/asset/${assetId}`,
      params
    })
  }

  // ==================== 转账验证 ====================

  // 验证转账信息
  static validateTransfer(data: any) {
    return request.post<any>({
      url: `${this.baseUrl}/validate`,
      data
    })
  }

  // 检查用户余额
  static checkUserBalance(userId: number, assetId: number, amount: number) {
    return request.get<any>({
      url: `${this.baseUrl}/check-balance`,
      params: { user_id: userId, asset_id: assetId, amount }
    })
  }

  // ==================== 统计数据 ====================

  // 获取转账统计
  static getTransferStats(params?: any) {
    return request.get<any>({
      url: `${this.baseUrl}/stats`,
      params
    })
  }

  // 获取用户转账统计
  static getUserTransferStats(userId: number, params?: any) {
    return request.get<any>({
      url: `${this.baseUrl}/user/${userId}/stats`,
      params
    })
  }

  // 获取资产转账统计
  static getAssetTransferStats(assetId: number, params?: any) {
    return request.get<any>({
      url: `${this.baseUrl}/asset/${assetId}/stats`,
      params
    })
  }

  // 获取转账趋势数据
  static getTransferTrendData(params?: any) {
    return request.get<any>({
      url: `${this.baseUrl}/stats/trend`,
      params
    })
  }

  // ==================== 导出功能 ====================

  // 导出转账列表
  static exportTransferList(params?: any) {
    return request.get<any>({
      url: `${this.baseUrl}/export`,
      params,
      responseType: 'blob'
    })
  }

  // 导出用户转账
  static exportUserTransfers(userId: number, params?: any) {
    return request.get<any>({
      url: `${this.baseUrl}/user/${userId}/export`,
      params,
      responseType: 'blob'
    })
  }

  // ==================== 搜索功能 ====================

  // 搜索转账
  static searchTransfers(params: any) {
    return request.get<any>({
      url: `${this.baseUrl}/search`,
      params
    })
  }

  // 高级搜索转账
  static advancedSearchTransfers(params: any) {
    return request.post<any>({
      url: `${this.baseUrl}/advanced-search`,
      data: params
    })
  }

  // ==================== 转账监控 ====================

  // 获取异常转账
  static getAbnormalTransfers(params?: any) {
    return request.get<any>({
      url: `${this.baseUrl}/monitor/abnormal`,
      params
    })
  }

  // 获取大额转账
  static getLargeAmountTransfers(params?: any) {
    return request.get<any>({
      url: `${this.baseUrl}/monitor/large-amount`,
      params
    })
  }

  // 获取频繁转账用户
  static getFrequentTransferUsers(params?: any) {
    return request.get<any>({
      url: `${this.baseUrl}/monitor/frequent-users`,
      params
    })
  }

  // ==================== 转账审核 ====================

  // 提交审核
  static submitForReview(id: number, data?: any) {
    return request.put<any>({
      url: `${this.baseUrl}/${id}/submit-review`,
      data
    })
  }

  // 审核转账
  static reviewTransfer(id: number, approved: boolean, reason?: string) {
    return request.put<any>({
      url: `${this.baseUrl}/${id}/review`,
      data: { approved, reason }
    })
  }

  // 批量审核转账
  static batchReviewTransfers(ids: number[], approved: boolean, reason?: string) {
    return request.put<any>({
      url: `${this.baseUrl}/batch/review`,
      data: { ids, approved, reason }
    })
  }
}
