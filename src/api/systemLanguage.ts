import request from '@/utils/http'

export class LanguageService {
  static baseUrl = '/system/language'

  /**
   * 获取语言列表
   */
  static async getLanguageList(params: any): Promise<any> {
    return request.get<any>({
      url: `${this.baseUrl}`,
      params
    })
  }

  /**
   * 创建语言
   */
  static async createLanguage(data: any): Promise<any> {
    return request.post<any>({
      url: `${this.baseUrl}`,
      data
    })
  }

  /**
   * 更新语言
   */
  static async updateLanguage(id: number, data: any): Promise<any> {
    return request.put<any>({
      url: `${this.baseUrl}/${id}`,
      data
    })
  }

  /**
   * 删除语言
   */
  static async deleteLanguage(id: number): Promise<any> {
    return request.del<any>({
      url: `${this.baseUrl}/${id}`
    })
  }

  /**
   * 批量删除语言
   */
  static async batchDeleteLanguages(ids: number[]): Promise<any> {
    return request.del<any>({
      url: `${this.baseUrl}/batch`,
      data: { ids }
    })
  }

  /**
   * 获取语言详情
   */
  static async getLanguageDetail(id: number): Promise<any> {
    return request.get<any>({
      url: `${this.baseUrl}/${id}`
    })
  }
}
