<template>
  <ArtCrudTable :config="crudConfig" @status-change="handleStatusChange" ref="crudTableRef">
    <!-- 自定义插槽 -->
    <template #userType="{ row }">
      <el-tag
        :type="row.userType === 'vip' ? 'warning' : row.userType === 'premium' ? 'success' : 'info'"
      >
        {{
          row.userType === 'vip' ? 'VIP用户' : row.userType === 'premium' ? '高级用户' : '普通用户'
        }}
      </el-tag>
    </template>

    <template #actions="{ row }">
      <el-button size="small" @click="editRow(row)">编辑</el-button>
      <el-button size="small" type="danger" @click="deleteRow(row)">删除</el-button>
      <el-button size="small" type="primary" @click="viewProfile(row)">查看</el-button>
    </template>
  </ArtCrudTable>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { ElTag, ElButton, ElMessage } from 'element-plus'
  import ArtCrudTable, {
    type CrudTableConfig,
    type DialogFormItem,
    type BatchAction,
    type ExportConfig,
    type PermissionConfig,
    type ResponsiveConfig,
    type SecurityConfig,
    type PerformanceConfig
  } from '@/components/core/tables/ArtCrudTable.vue'

  // 模拟 API 服务
  const mockApiService = {
    // 获取列表数据
    getList: async (params: any) => {
      console.log('获取列表参数:', params)
      // 模拟延迟
      await new Promise((resolve) => setTimeout(resolve, 800))

      // 模拟更丰富的数据
      const userTypes = ['normal', 'vip', 'premium']
      const departments = ['技术部', '产品部', '运营部', '设计部', '市场部']
      const cities = ['北京', '上海', '深圳', '杭州', '广州', '成都']

      const mockData = Array.from({ length: params.size || 20 }, (_, i) => {
        const id = i + 1 + (params.current - 1) * params.size
        const userType = userTypes[Math.floor(Math.random() * userTypes.length)]
        const department = departments[Math.floor(Math.random() * departments.length)]
        const city = cities[Math.floor(Math.random() * cities.length)]

        return {
          id,
          name: `用户${id}`,
          email: `user${id}@example.com`,
          phone: `138${String(id).padStart(8, '0')}`,
          avatar: 'https://youke1.picui.cn/s1/2025/08/20/68a595d420452.jpg', // 使用用户提供的头像
          status: Math.random() > 0.3 ? 1 : 0,
          userType,
          department,
          city,
          website: `https://www.user${id}.com`,
          rating: Math.floor(Math.random() * 5) + 1,
          isVip: Math.random() > 0.6,
          balance: Math.floor(Math.random() * 10000) + 1000,
          lastLoginTime: new Date(
            Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000
          ).toISOString(),
          createTime: new Date(
            Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000
          ).toISOString(),
          biography: `这是用户${id}的个人简介，包含了一些\n多行文本内容。\n展示了用户的基本信息。`,
          content: `<p>这是用户${id}的<strong>富文本内容</strong>示例</p><p>包含了<em>HTML标签</em>和格式化内容。</p>`,
          tags: ['前端', 'Vue', 'TypeScript'].slice(0, Math.floor(Math.random() * 3) + 1),
          socialMedia: {
            github: `https://github.com/user${id}`,
            linkedin: `https://linkedin.com/in/user${id}`,
            twitter: `https://twitter.com/user${id}`
          }
        }
      })

      return {
        code: 200,
        data: {
          records: mockData,
          total: 150,
          current: params.current,
          size: params.size
        }
      }
    },

    // 创建数据
    create: async (data: any) => {
      console.log('创建数据:', data)
      await new Promise((resolve) => setTimeout(resolve, 500))
      return { code: 200, message: '创建成功' }
    },

    // 更新数据
    update: async (id: any, data: any) => {
      console.log('更新数据:', id, data)
      await new Promise((resolve) => setTimeout(resolve, 500))
      return { code: 200, message: '更新成功' }
    },

    // 删除数据
    delete: async (id: any) => {
      console.log('删除数据:', id)
      await new Promise((resolve) => setTimeout(resolve, 500))
      return { code: 200, message: '删除成功' }
    },

    // 批量删除
    batchDelete: async (ids: any[]) => {
      console.log('批量删除:', ids)
      await new Promise((resolve) => setTimeout(resolve, 500))
      return { code: 200, message: '批量删除成功' }
    }
  }

  // 搜索表单配置
  const searchFormItems = [
    {
      key: 'name',
      label: '用户名',
      type: 'input',
      placeholder: '请输入用户名'
    },
    {
      key: 'email',
      label: '邮箱',
      type: 'input',
      placeholder: '请输入邮箱'
    },
    {
      key: 'status',
      label: '状态',
      type: 'select',
      options: [
        { label: '全部', value: '' },
        { label: '启用', value: 1 },
        { label: '禁用', value: 0 }
      ]
    },
    {
      key: 'userType',
      label: '用户类型',
      type: 'select',
      options: [
        { label: '全部', value: '' },
        { label: 'VIP用户', value: 'vip' },
        { label: '高级用户', value: 'premium' },
        { label: '普通用户', value: 'normal' }
      ]
    },
    {
      key: 'department',
      label: '部门',
      type: 'select',
      options: [
        { label: '全部', value: '' },
        { label: '技术部', value: '技术部' },
        { label: '产品部', value: '产品部' },
        { label: '运营部', value: '运营部' },
        { label: '设计部', value: '设计部' },
        { label: '市场部', value: '市场部' }
      ]
    },
    {
      key: 'createTime',
      label: '创建时间',
      type: 'daterange',
      placeholder: '请选择创建时间范围'
    }
  ]

  // 表格列配置 - 展示各种列类型
  const tableColumns = [
    { type: 'selection', width: 50 },
    { type: 'index', label: '序号', width: 60 },

    // 图片列 - 头像
    {
      prop: 'avatar',
      label: '头像',
      columnType: 'image',
      width: 80
    },

    // 普通文本列
    { prop: 'name', label: '用户名', minWidth: 100 },

    // 复制列 - 邮箱
    {
      prop: 'email',
      label: '邮箱',
      columnType: 'copy',
      minWidth: 180
    },

    // 复制列 - 手机号
    {
      prop: 'phone',
      label: '手机号',
      columnType: 'copy',
      width: 140
    },

    // 开关列 - 状态
    {
      prop: 'status',
      label: '状态',
      columnType: 'toggle',
      width: 100
    },

    // 标签列 - 用户类型（使用自定义插槽）
    {
      prop: 'userType',
      label: '用户类型',
      width: 120,
      useSlot: true,
      slotName: 'userType'
    },

    // 标签列 - 部门（使用配置着色）
    {
      prop: 'department',
      label: '部门',
      columnType: 'tag',
      width: 100,
      options: [
        { label: '技术部', value: '技术部', color: 'primary' },
        { label: '产品部', value: '产品部', color: 'success' },
        { label: '运营部', value: '运营部', color: 'warning' },
        { label: '设计部', value: '设计部', color: 'danger' },
        { label: '市场部', value: '市场部', color: 'info' }
      ]
    },

    // 标签列 - 城市（智能着色）
    {
      prop: 'city',
      label: '城市',
      columnType: 'tag',
      width: 80
    },

    // 链接列 - 个人网站
    {
      prop: 'website',
      label: '个人网站',
      columnType: 'link',
      width: 150
    },

    // 评分列
    {
      prop: 'rating',
      label: '用户评分',
      columnType: 'rating',
      width: 150
    },

    // 复选框列 - VIP状态
    {
      prop: 'isVip',
      label: 'VIP用户',
      columnType: 'checkbox',
      width: 90
    },

    // 货币列 - 余额（新增）
    {
      prop: 'balance',
      label: '账户余额',
      columnType: 'currency',
      width: 120
    },

    // 日期列 - 最后登录
    {
      prop: 'lastLoginTime',
      label: '最后登录',
      columnType: 'date',
      width: 160
    },

    // 日期列 - 创建时间
    {
      prop: 'createTime',
      label: '创建时间',
      columnType: 'date',
      width: 160
    },

    // 多行文本列 - 个人简介
    {
      prop: 'biography',
      label: '个人简介',
      columnType: 'rows',
      width: 200
    },

    // HTML列 - 富文本内容
    {
      prop: 'content',
      label: '富文本内容',
      columnType: 'html',
      width: 200
    },

    // 操作列
    {
      prop: 'actions',
      label: '操作',
      width: 180,
      fixed: 'right',
      useSlot: true,
      slotName: 'actions'
    }
  ]

  // 对话框表单配置
  const dialogFormItems: DialogFormItem[] = [
    {
      label: '用户名',
      prop: 'name',
      type: 'input',
      required: true,
      span: 12,
      config: {
        placeholder: '请输入用户名'
      }
    },
    {
      label: '邮箱',
      prop: 'email',
      type: 'input',
      required: true,
      span: 12,
      config: {
        placeholder: '请输入邮箱',
        maxlength: 50
      }
    },
    {
      label: '手机号',
      prop: 'phone',
      type: 'input',
      required: true,
      span: 12,
      config: {
        placeholder: '请输入手机号',
        maxlength: 11
      }
    },
    {
      label: '状态',
      prop: 'status',
      type: 'select',
      required: true,
      span: 12,
      options: [
        { label: '启用', value: 1 },
        { label: '禁用', value: 0 }
      ]
    },
    {
      label: '用户类型',
      prop: 'userType',
      type: 'select',
      required: true,
      span: 12,
      options: [
        { label: 'VIP用户', value: 'vip' },
        { label: '高级用户', value: 'premium' },
        { label: '普通用户', value: 'normal' }
      ]
    },
    {
      label: '部门',
      prop: 'department',
      type: 'select',
      required: true,
      span: 12,
      options: [
        { label: '技术部', value: '技术部' },
        { label: '产品部', value: '产品部' },
        { label: '运营部', value: '运营部' },
        { label: '设计部', value: '设计部' },
        { label: '市场部', value: '市场部' }
      ]
    },
    {
      label: '城市',
      prop: 'city',
      type: 'select',
      span: 12,
      options: [
        { label: '北京', value: '北京' },
        { label: '上海', value: '上海' },
        { label: '深圳', value: '深圳' },
        { label: '杭州', value: '杭州' },
        { label: '广州', value: '广州' },
        { label: '成都', value: '成都' }
      ]
    },
    {
      label: '个人网站',
      prop: 'website',
      type: 'input',
      span: 12,
      config: {
        placeholder: '请输入个人网站地址'
      }
    },
    {
      label: '用户评分',
      prop: 'rating',
      type: 'number',
      span: 12,
      config: {
        min: 1,
        max: 5,
        step: 1
      }
    },
    {
      label: '账户余额',
      prop: 'balance',
      type: 'number',
      span: 12,
      config: {
        min: 0,
        step: 100,
        placeholder: '请输入账户余额'
      }
    },
    {
      label: '头像',
      prop: 'avatar',
      type: 'upload',
      span: 24,
      config: {
        accept: 'image/*',
        maxSize: 5,
        triggerText: '选择头像',
        tip: '支持 jpg、png 格式，文件大小不超过 5MB'
      }
    },
    {
      label: '个人简介',
      prop: 'biography',
      type: 'textarea',
      span: 24,
      config: {
        placeholder: '请输入个人简介',
        rows: 4,
        maxlength: 500
      }
    },
    {
      label: '详细介绍',
      prop: 'content',
      type: 'richtext',
      span: 24,
      config: {
        placeholder: '请输入详细介绍',
        height: '300px',
        mode: 'default',
        excludeKeys: ['fontFamily'],
        uploadConfig: {
          maxFileSize: 5 * 1024 * 1024,
          maxNumberOfFiles: 10,
          server: '/api/upload'
        }
      }
    }
  ]

  // 批量操作配置 - 新增功能
  const batchActions: BatchAction[] = [
    {
      label: '批量启用',
      key: 'batchEnable',
      type: 'success',
      icon: 'Check',
      confirm: '确认启用选中的 {count} 个用户吗？',
      handler: async (selectedRows) => {
        console.log('批量启用用户:', selectedRows)
        // 模拟批量启用API调用
        await new Promise((resolve) => setTimeout(resolve, 1000))
        ElMessage.success(`成功启用 ${selectedRows.length} 个用户`)
      }
    },
    {
      label: '批量禁用',
      key: 'batchDisable',
      type: 'warning',
      icon: 'Close',
      confirm: '确认禁用选中的 {count} 个用户吗？',
      handler: async (selectedRows) => {
        console.log('批量禁用用户:', selectedRows)
        await new Promise((resolve) => setTimeout(resolve, 1000))
        ElMessage.success(`成功禁用 ${selectedRows.length} 个用户`)
      }
    },
    {
      label: '批量导出',
      key: 'batchExport',
      type: 'primary',
      icon: 'Download',
      handler: async (selectedRows) => {
        console.log('批量导出用户:', selectedRows)
        await new Promise((resolve) => setTimeout(resolve, 1000))
        ElMessage.success(`成功导出 ${selectedRows.length} 个用户的数据`)
      }
    }
  ]

  // 导出配置 - 新增功能
  const exportConfig: ExportConfig = {
    enabled: true,
    filename: '用户数据导出',
    columns: ['name', 'email', 'phone', 'department', 'city', 'createTime'],
    formats: ['excel', 'csv'],
    maxRows: 1000
  }

  // 权限配置 - 新增功能
  const permissionConfig: PermissionConfig = {
    create: () => {
      // 模拟权限检查
      return true
    },
    update: (row) => {
      // 例如：VIP用户不能被普通管理员编辑
      return row.userType !== 'vip'
    },
    delete: (row) => {
      // 例如：管理员用户不能被删除
      return row.userType !== 'admin'
    },
    export: () => true,
    batchDelete: () => true
  }

  // 响应式配置 - 新增功能
  const responsiveConfig: ResponsiveConfig = {
    mobile: {
      hideColumns: ['biography', 'content', 'website', 'lastLoginTime'],
      cardView: false,
      breakpoint: 768
    }
  }

  // 安全配置 - 新增功能
  const securityConfig: SecurityConfig = {
    enableXssProtection: true,
    allowedTags: ['p', 'br', 'strong', 'em', 'u', 'span', 'div'],
    allowedAttributes: {
      div: ['class', 'style'],
      span: ['class', 'style']
    }
  }

  // 性能配置 - 新增功能
  const performanceConfig: PerformanceConfig = {
    enableCache: true,
    cacheTime: 5 * 60 * 1000, // 5分钟
    debounceTime: 500,
    maxCacheSize: 50,
    lazyLoad: true,
    virtualScroll: {
      enabled: false, // 数据量不大时可以关闭
      itemHeight: 60,
      buffer: 10
    }
  }

  // CRUD 表格配置
  const crudConfig: CrudTableConfig = {
    title: '用户管理',
    addButtonText: '新增用户',
    deleteButtonText: '批量删除',
    deleteConfirmText: '确认删除这个用户吗？此操作不可恢复！',
    batchDeleteConfirmText: '确认删除选中的 {count} 个用户吗？此操作不可恢复！',

    // 搜索配置
    searchFormItems,
    initialSearchState: {
      name: '',
      email: '',
      status: '',
      userType: '',
      department: '',
      createTime: []
    },

    // 表格配置
    tableColumns: tableColumns as any,
    rowKey: 'id',
    pageSize: 15,

    // API 服务
    apiService: mockApiService,

    // 对话框配置
    dialog: {
      title: {
        add: '新增用户',
        edit: '编辑用户'
      },
      width: '900px',
      formItems: dialogFormItems,
      initialFormData: {
        name: '',
        email: '',
        phone: '',
        status: 1,
        userType: 'normal',
        department: '技术部',
        city: '北京',
        website: '',
        rating: 5,
        balance: 1000,
        avatar: '',
        biography: '',
        content: ''
      }
    },

    // 新增配置项
    batchActions, // 批量操作
    export: exportConfig, // 导出配置
    permissions: permissionConfig, // 权限配置
    responsive: responsiveConfig, // 响应式配置
    security: securityConfig, // 安全配置
    performance: performanceConfig, // 性能配置

    // 状态切换配置
    hasStatusSwitch: true,
    statusField: 'status',
    statusActiveValue: 1,
    statusInactiveValue: 0,

    // 允许的操作
    allowBatchDelete: true
  }

  const crudTableRef = ref()

  // 事件处理
  const handleStatusChange = (row: any, newStatus: boolean) => {
    console.log('状态变更:', row.name, '状态:', newStatus ? '启用' : '禁用')
    ElMessage.success(`用户 ${row.name} 状态已${newStatus ? '启用' : '禁用'}`)
  }

  const editRow = (row: any) => {
    console.log('编辑用户:', row)
    crudTableRef.value?.showDialog('edit', row)
  }

  const deleteRow = (row: any) => {
    console.log('删除用户:', row)
    crudTableRef.value?.deleteRow(row)
  }

  const viewProfile = (row: any) => {
    console.log('查看用户资料:', row)
    ElMessage.info(`查看用户 ${row.name} 的详细资料`)
    // 这里可以跳转到用户详情页面
  }
</script>

<style scoped lang="scss">
  // 表格页面样式优化
  :deep(.art-crud-table) {
    .art-table-card {
      .el-card__body {
        padding: 16px;
      }
    }

    // 优化表格单元格显示
    .el-table {
      .el-table__cell {
        .el-tag {
          margin: 2px;
        }

        .el-switch {
          --el-switch-on-color: var(--el-color-success);
          --el-switch-off-color: var(--el-color-info);
        }

        .el-button {
          margin: 0 2px;
        }
      }
    }

    // 图片列样式优化
    .el-image {
      overflow: hidden;
      border-radius: 50%;

      img {
        object-fit: cover;
      }
    }
  }

  // 操作按钮组样式
  .el-button-group {
    .el-button + .el-button {
      margin-left: 0;
    }
  }
</style>
