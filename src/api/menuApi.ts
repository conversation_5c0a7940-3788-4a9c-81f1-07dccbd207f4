import request from '@/utils/http'
import { AppRouteRecord } from '@/types/router'

interface MenuResponse {
  menuList: AppRouteRecord[]
}

// 菜单服务类
export class MenuService {
  private static baseURL = '/system/menu'

  /**
   * 获取菜单列表
   */
  static async getMenuList(params?: any) {
    return request.get<any>({
      url: this.baseURL,
      params
    })
  }

  /**
   * 创建菜单
   */
  static async createMenu(data: any) {
    return request.post<any>({
      url: this.baseURL,
      data
    })
  }

  /**
   * 更新菜单
   */
  static async updateMenu(id: number, data: any) {
    return request.put<any>({
      url: `${this.baseURL}/${id}`,
      data
    })
  }

  /**
   * 删除菜单
   */
  static async deleteMenu(id: number) {
    return request.del<any>({
      url: `${this.baseURL}/${id}`
    })
  }

  /**
   * 批量删除菜单
   */
  static async batchDeleteMenus(ids: number[]) {
    return request.del<any>({
      url: `${this.baseURL}/batch`,
      data: { ids }
    })
  }

  /**
   * 获取菜单详情
   */
  static async getMenuDetail(id: number) {
    return request.get<any>({
      url: `${this.baseURL}/${id}`
    })
  }

  /**
   * 更新菜单状态
   */
  static async updateMenuStatus(id: number, status: number) {
    return request.put<any>({
      url: `${this.baseURL}/${id}/status`,
      data: { status }
    })
  }

  /**
   * 获取菜单树形结构
   */
  static async getMenuTree(params?: any) {
    return request.get<any>({
      url: `${this.baseURL}/tree`,
      params
    })
  }

  /**
   * 获取用户菜单
   */
  static async getUserMenu() {
    return request.get<any>({
      url: '/menu'
    })
  }

  /**
   * 导入菜单数据
   */
  static async importMenus(data: any) {
    return request.post<any>({
      url: `${this.baseURL}/import`,
      data
    })
  }

  /**
   * 导出菜单数据
   */
  static async exportMenus(params: any) {
    return request.get<any>({
      url: `${this.baseURL}/export`,
      params,
      responseType: 'blob'
    })
  }
}

// 菜单接口
export const menuService = {
  async getMenuList(): Promise<MenuResponse> {
    try {
      const response = await MenuService.getUserMenu()
      // 处理不同的响应格式
      let menuList: AppRouteRecord[]

      if (Array.isArray(response)) {
        // 如果响应直接是数组
        menuList = response
      } else if (response && response.menuList && Array.isArray(response.menuList)) {
        // 如果响应是包含menuList属性的对象
        menuList = response.menuList
      } else if (response && response.data && Array.isArray(response.data)) {
        // 如果响应是包含data属性的对象
        menuList = response.data
      } else {
        throw new Error('无效的菜单数据格式')
      }

      return { menuList }
    } catch (error) {
      throw error instanceof Error ? error : new Error('获取菜单失败')
    }
  }
}
