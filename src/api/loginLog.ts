import request from '@/utils/http'

// 登录日志服务类
export class LoginLogService {
  private static baseURL = '/monitor/loginLog'

  /**
   * 获取登录日志列表
   */
  static async getLoginLogList(params: any) {
    return request.get<any>({
      url: this.baseURL,
      params
    })
  }

  /**
   * 删除登录日志
   */
  static async deleteLoginLog(id: number) {
    return request.del<any>({
      url: `${this.baseURL}/${id}`
    })
  }

  /**
   * 批量删除登录日志
   */
  static async batchDeleteLoginLogs(ids: number[]) {
    return request.del<any>({
      url: `${this.baseURL}/batch`,
      data: { ids }
    })
  }
}
