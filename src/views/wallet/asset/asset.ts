import { UserAssetService } from '@/api/walletAsset'
import type {
  CrudTableConfig,
  BatchAction,
  ExportConfig,
  PermissionConfig,
  SecurityConfig,
  PerformanceConfig,
  ResponsiveConfig
} from '@/components/core/tables/ArtCrudTable.vue'

// 过滤空值参数的工具函数
const filterEmptyParams = (params: Record<string, any>) => {
  const filtered: Record<string, any> = {}
  Object.keys(params).forEach((key) => {
    const value = params[key]
    // 只保留非空值：非空字符串、非null、非undefined、数字0、布尔值false
    if (value !== '' && value !== null && value !== undefined) {
      filtered[key] = value
    }
  })
  return filtered
}

// 包装API服务，添加参数过滤
const createAssetApiService = () => ({
  getList: (params: any) => {
    // 过滤空值参数，解决URL中出现空值的问题
    const filteredParams = filterEmptyParams(params)
    console.log('UserAsset API - 过滤后的请求参数:', filteredParams)
    return UserAssetService.getUserAssetList(filteredParams)
  },
  delete: (id: any) => UserAssetService.deleteUserAsset(id),
  batchDelete: (ids: any[]) => UserAssetService.batchDeleteUserAssets(ids),
  update: (id: any, data: any) => UserAssetService.updateUserAsset(id, data),
  create: (data: any) => UserAssetService.createUserAsset(data)
})

// 批量操作配置
const batchActions: BatchAction[] = [
  {
    label: '批量导出',
    key: 'batchExport',
    type: 'info',
    icon: 'Download',
    handler: async (selectedRows) => {
      console.log('批量导出资产:', selectedRows)
      const exportData = selectedRows.map((row) => ({
        id: row.id as number,
        user_id: row.user_id as number,
        asset_id: row.asset_id as number,
        frozen_amount: row.frozen_amount as number,
        available_amount: row.available_amount as number,
        profit_amount: row.profit_amount as number,
        sum_deposit_amount: row.sum_deposit_amount as number,
        sum_withdraw_amount: row.sum_withdraw_amount as number,
        is_primary: row.is_primary as boolean,
        created_at: row.created_at as string
      }))
      console.log('导出数据:', exportData)
    }
  }
]

// 金额格式化工具函数
export const formatAmount = (amount: number | string, precision: number = 4) => {
  if (!amount && amount !== 0) return '0.0000'
  const num = typeof amount === 'string' ? parseFloat(amount) : amount
  return num.toFixed(precision)
}

// 计算总资产
export const calculateTotalAmount = (row: any) => {
  const available = parseFloat(row.available_amount || 0)
  const frozen = parseFloat(row.frozen_amount || 0)
  const profit = parseFloat(row.profit_amount || 0)
  return formatAmount(available + frozen + profit)
}

// 计算资产利用率
export const calculateAssetUtilization = (row: any) => {
  const total = parseFloat(calculateTotalAmount(row))
  const available = parseFloat(row.available_amount || 0)
  if (total === 0) return '0%'
  return ((available / total) * 100).toFixed(2) + '%'
}

// 主资产标识
export const getPrimaryStatus = (isPrimary: boolean) => {
  return isPrimary ? '主资产' : '普通资产'
}

// 获取金额状态样式类
export const getAmountStatusClass = (amount: number, type: string) => {
  if (amount > 0) return `amount-positive ${type}-positive`
  if (amount < 0) return `amount-negative ${type}-negative`
  return `amount-zero ${type}-zero`
}

// 导出配置
const exportConfig: ExportConfig = {
  enabled: true,
  filename: '用户资产数据导出',
  columns: [
    'id',
    'user_id',
    'asset_id',
    'frozen_amount',
    'available_amount',
    'profit_amount',
    'sum_deposit_amount',
    'sum_withdraw_amount',
    'is_primary',
    'created_at'
  ],
  formats: ['excel', 'csv'],
  maxRows: 10000
}

// 权限配置
const permissionConfig: PermissionConfig = {
  create: () => {
    return true
  },
  update: () => {
    return true
  },
  delete: () => {
    // 主资产和有余额的资产需要谨慎删除
    return true
  },
  export: () => true,
  batchDelete: () => true
}

// 响应式配置
const responsiveConfig: ResponsiveConfig = {
  mobile: {
    hideColumns: ['profit_amount', 'sum_deposit_amount', 'sum_withdraw_amount', 'created_at'],
    cardView: false,
    breakpoint: 768
  }
}

// 安全配置
const securityConfig: SecurityConfig = {
  enableXssProtection: true,
  allowedTags: ['p', 'br', 'strong', 'em'],
  allowedAttributes: {
    div: ['class'],
    span: ['class']
  }
}

// 性能配置
const performanceConfig: PerformanceConfig = {
  enableCache: true,
  cacheTime: 5 * 60 * 1000, // 5分钟
  debounceTime: 500,
  maxCacheSize: 100,
  lazyLoad: true,
  virtualScroll: {
    enabled: true,
    itemHeight: 70,
    buffer: 20
  }
}

// 用户资产管理配置
export const assetTableConfig: CrudTableConfig = {
  title: '用户资产',
  addButtonText: '新增资产',
  deleteButtonText: '删除资产',
  deleteConfirmText: '确定要删除该用户资产吗？',
  batchDeleteConfirmText: '确定要删除选中的 {count} 条用户资产吗？',
  emptyHeight: '500px',

  initialSearchState: {
    manager_id: '',
    user_id: '',
    asset_id: '',
    min_amount: '',
    max_amount: '',
    is_primary: '',
    has_balance: '',
    startTime: '',
    endTime: ''
  },

  searchFormItems: [
    {
      key: 'manager_id',
      label: '管理员ID',
      type: 'input',
      placeholder: '请输入管理员ID'
    },
    {
      key: 'user_id',
      label: '用户ID',
      type: 'input',
      placeholder: '请输入用户ID'
    },
    {
      key: 'asset_id',
      label: '资产ID',
      type: 'input',
      placeholder: '请输入资产ID'
    },
    {
      key: 'min_amount',
      label: '最小余额',
      type: 'input',
      placeholder: '请输入最小余额'
    },
    {
      key: 'max_amount',
      label: '最大余额',
      type: 'input',
      placeholder: '请输入最大余额'
    },
    {
      key: 'is_primary',
      label: '是否主资产',
      type: 'select',
      placeholder: '请选择是否主资产',
      options: [
        { label: '主资产', value: 'true' },
        { label: '普通资产', value: 'false' }
      ]
    },
    {
      key: 'has_balance',
      label: '是否有余额',
      type: 'select',
      placeholder: '请选择是否有余额',
      options: [
        { label: '有余额', value: 'true' },
        { label: '零余额', value: 'false' }
      ]
    },
    {
      key: 'startTime',
      label: '开始时间',
      type: 'date',
      placeholder: '请选择开始时间'
    },
    {
      key: 'endTime',
      label: '结束时间',
      type: 'date',
      placeholder: '请选择结束时间'
    }
  ],

  tableColumns: [
    { type: 'selection', width: 55 },
    { label: 'ID', prop: 'id', width: 80, sortable: true },
    {
      label: '用户信息',
      prop: 'user',
      minWidth: 150,
      useSlot: true,
      slotName: 'user'
    },
    {
      label: '资产信息',
      prop: 'asset',
      minWidth: 150,
      useSlot: true,
      slotName: 'asset'
    },
    {
      label: '可用余额',
      prop: 'available_amount',
      minWidth: 120,
      useSlot: true,
      slotName: 'availableAmount',
      sortable: true
    },
    {
      label: '冻结余额',
      prop: 'frozen_amount',
      minWidth: 120,
      useSlot: true,
      slotName: 'frozenAmount',
      sortable: true
    },
    {
      label: '收益余额',
      prop: 'profit_amount',
      minWidth: 120,
      useSlot: true,
      slotName: 'profitAmount',
      sortable: true
    },
    {
      label: '充值总额',
      prop: 'sum_deposit_amount',
      minWidth: 120,
      useSlot: true,
      slotName: 'depositAmount',
      sortable: true
    },
    {
      label: '提现总额',
      prop: 'sum_withdraw_amount',
      minWidth: 120,
      useSlot: true,
      slotName: 'withdrawAmount',
      sortable: true
    },
    {
      label: '总资产',
      prop: 'total_amount',
      minWidth: 120,
      useSlot: true,
      slotName: 'totalAmount',
      sortable: false
    },
    {
      label: '主资产',
      prop: 'is_primary',
      width: 100,
      useSlot: true,
      slotName: 'primaryStatus'
    },
    {
      label: '创建时间',
      prop: 'created_at',
      minWidth: 160,
      sortable: true,
      columnType: 'date'
    },
    {
      label: '操作',
      width: 280,
      fixed: 'right',
      useSlot: true,
      slotName: 'actions'
    }
  ],

  apiService: createAssetApiService(),

  // 分页参数映射
  paginationKey: {
    current: 'page',
    size: 'pageSize'
  },

  dialog: {
    title: {
      add: '添加用户资产',
      edit: '编辑用户资产'
    },
    width: '800px',
    formItems: [
      {
        label: '用户ID',
        prop: 'user_id',
        type: 'input',
        required: true,
        config: {
          placeholder: '请输入用户ID'
        },
        rules: [{ required: true, message: '请输入用户ID', trigger: 'blur' }],
        span: 12
      },
      {
        label: '资产ID',
        prop: 'asset_id',
        type: 'input',
        required: true,
        config: {
          placeholder: '请输入资产ID'
        },
        rules: [{ required: true, message: '请输入资产ID', trigger: 'blur' }],
        span: 12
      },
      {
        label: '可用余额',
        prop: 'available_amount',
        type: 'input',
        config: {
          placeholder: '请输入可用余额',
          type: 'number',
          min: 0,
          step: 0.0001,
          precision: 4
        },
        rules: [{ pattern: /^\d+(\.\d{1,4})?$/, message: '请输入有效的金额', trigger: 'blur' }],
        span: 12
      },
      {
        label: '冻结余额',
        prop: 'frozen_amount',
        type: 'input',
        config: {
          placeholder: '请输入冻结余额',
          type: 'number',
          min: 0,
          step: 0.0001,
          precision: 4
        },
        rules: [{ pattern: /^\d+(\.\d{1,4})?$/, message: '请输入有效的金额', trigger: 'blur' }],
        span: 12
      },
      {
        label: '收益余额',
        prop: 'profit_amount',
        type: 'input',
        config: {
          placeholder: '请输入收益余额',
          type: 'number',
          step: 0.0001,
          precision: 4
        },
        rules: [{ pattern: /^-?\d+(\.\d{1,4})?$/, message: '请输入有效的金额', trigger: 'blur' }],
        span: 12
      },
      {
        label: '充值总额',
        prop: 'sum_deposit_amount',
        type: 'input',
        config: {
          placeholder: '请输入充值总额',
          type: 'number',
          min: 0,
          step: 0.0001,
          precision: 4
        },
        rules: [{ pattern: /^\d+(\.\d{1,4})?$/, message: '请输入有效的金额', trigger: 'blur' }],
        span: 12
      },
      {
        label: '提现总额',
        prop: 'sum_withdraw_amount',
        type: 'input',
        config: {
          placeholder: '请输入提现总额',
          type: 'number',
          min: 0,
          step: 0.0001,
          precision: 4
        },
        rules: [{ pattern: /^\d+(\.\d{1,4})?$/, message: '请输入有效的金额', trigger: 'blur' }],
        span: 12
      },
      {
        label: '是否主资产',
        prop: 'is_primary',
        type: 'select',
        options: [
          { label: '普通资产', value: false },
          { label: '主资产', value: true }
        ],
        span: 12
      }
    ],
    initialFormData: {
      user_id: '',
      asset_id: '',
      available_amount: '0.0000',
      frozen_amount: '0.0000',
      profit_amount: '0.0000',
      sum_deposit_amount: '0.0000',
      sum_withdraw_amount: '0.0000',
      is_primary: false
    }
  },

  // 功能配置
  batchActions,
  export: exportConfig,
  permissions: permissionConfig,
  responsive: responsiveConfig,
  security: securityConfig,
  performance: performanceConfig,

  // 操作权限
  allowBatchDelete: true
}
