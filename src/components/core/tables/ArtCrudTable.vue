<script setup lang="ts">
  import { ref, reactive, computed, onMounted } from 'vue'
  import {
    ElCard,
    ElButton,
    ElTableColumn,
    ElMessageBox,
    ElMessage,
    ElSwitch,
    ElTag,
    ElImage,
    type FormRules
  } from 'element-plus'
  import { useWindowSize } from '@vueuse/core'

  // 导入组件
  import ArtTable from './art-table/index.vue'
  import ArtTableHeader from './art-table-header/index.vue'
  import ArtSearchBar from '@/components/core/forms/art-search-bar/index.vue'
  import ArtCrudDialog from './ArtCrudDialog.vue'

  // 导入类型和组合函数
  import { useTable } from '@/composables/useTable'
  import type { ColumnOption } from '@/types'
  import type { SearchFormItem } from '@/components/core/forms/art-search-bar/index.vue'

  defineOptions({ name: 'ArtCrudTable' })

  // ==================== 类型定义 ====================

  // 严格的列类型定义
  export type ColumnType =
    | 'text'
    | 'image'
    | 'toggle'
    | 'tag'
    | 'select'
    | 'checkbox'
    | 'images'
    | 'html'
    | 'copy'
    | 'rows'
    | 'link'
    | 'rating'
    | 'date'
    | 'currency'
    | 'progress'
    | 'index'
    | 'selection'

  // 批量操作接口
  export interface BatchAction<T = Record<string, unknown>> {
    label: string
    key: string
    type?: 'primary' | 'success' | 'warning' | 'danger' | 'info'
    icon?: string
    confirm?: string
    disabled?: boolean | ((selectedRows: T[]) => boolean)
    handler: (selectedRows: T[]) => Promise<void>
  }

  // 导出配置接口
  export interface ExportConfig {
    enabled: boolean
    filename?: string
    columns?: string[]
    formats?: ('excel' | 'csv' | 'pdf')[]
    maxRows?: number
  }

  // 虚拟滚动配置
  export interface VirtualScrollConfig {
    enabled: boolean
    itemHeight: number
    buffer?: number
  }

  // 响应式配置
  export interface ResponsiveConfig {
    mobile?: {
      hideColumns?: string[]
      cardView?: boolean
      breakpoint?: number
    }
  }

  // 权限配置
  export interface PermissionConfig<T = Record<string, unknown>> {
    create?: boolean | (() => boolean)
    update?: boolean | ((row: T) => boolean)
    delete?: boolean | ((row: T) => boolean)
    export?: boolean | (() => boolean)
    batchDelete?: boolean | (() => boolean)
  }

  // XSS 防护配置
  export interface SecurityConfig {
    enableXssProtection: boolean
    allowedTags?: string[]
    allowedAttributes?: Record<string, string[]>
  }

  // 性能配置
  export interface PerformanceConfig {
    enableCache?: boolean
    cacheTime?: number
    debounceTime?: number
    maxCacheSize?: number
    lazyLoad?: boolean
    virtualScroll?: VirtualScrollConfig
  }

  // 对话框表单字段类型 - 泛型优化
  export interface DialogFormItem<T = Record<string, unknown>> {
    label: string
    prop: string
    type: 'input' | 'textarea' | 'number' | 'select' | 'upload' | 'richtext'
    required?: boolean
    rules?: any[]
    config?: {
      placeholder?: string
      maxlength?: number | string
      disabled?: boolean
      min?: number
      max?: number
      step?: number
      rows?: number
      clearable?: boolean
      height?: string
      mode?: 'default' | 'simple'
      toolbarKeys?: string[]
      excludeKeys?: string[]
      uploadConfig?: {
        maxFileSize?: number
        maxNumberOfFiles?: number
        server?: string
      }
      accept?: string
      maxSize?: number
      triggerText?: string
      tip?: string
      [key: string]: unknown
    }
    options?:
      | Array<{ label: string; value: any; disabled?: boolean }>
      | (() => Array<{ label: string; value: any; disabled?: boolean }>)
    span?: number
    show?: boolean | ((formData: T, type: 'add' | 'edit') => boolean)
  }

  // API 服务接口 - 泛型优化
  export interface ApiService<T = Record<string, unknown>, K = string | number> {
    getList: (params: Record<string, unknown>) => Promise<{
      code: number
      data: {
        records: T[]
        total: number
        current: number
        size: number
      }
    }>
    delete?: (id: K) => Promise<{ code: number; message?: string }>
    batchDelete?: (ids: K[]) => Promise<{ code: number; message?: string }>
    update?: (id: K, data: any) => Promise<{ code: number; message?: string }>
    create?: (data: any) => Promise<{ code: number; message?: string }>
  }

  // 主配置接口 - 泛型优化
  export interface CrudTableConfig<T = Record<string, unknown>, K = string | number> {
    title: string
    addButtonText?: string
    deleteButtonText?: string
    deleteConfirmText?: string
    batchDeleteConfirmText?: string

    // 搜索表单配置
    searchFormItems?: SearchFormItem[]
    initialSearchState?: Record<string, unknown>

    // 表格列配置 - 严格类型
    tableColumns: Array<
      ColumnOption<T> & {
        formatter?: (row: T) => string | number
        render?: (row: T, column: any, cellValue: unknown, index: number) => unknown
        columnType?: ColumnType
        options?: Array<{ label: string; value: unknown; color?: string }>
      }
    >

    // API 服务配置
    apiService: ApiService<T, K>

    // 对话框配置
    dialog?: {
      title?: {
        add: string
        edit: string
      }
      width?: string
      formItems: DialogFormItem<T>[]
      initialFormData?: Record<string, any>
      rules?: FormRules
    }

    // 批量操作配置
    batchActions?: BatchAction<T>[]

    // 导出配置
    export?: ExportConfig

    // 权限配置
    permissions?: PermissionConfig<T>

    // 安全配置
    security?: SecurityConfig

    // 控制配置
    hideAddButton?: boolean
    hideDeleteButton?: boolean
    disableEdit?: boolean
    allowDelete?: boolean
    allowBatchDelete?: boolean

    // 其他配置
    rowKey?: string
    pageSize?: number
    hasStatusSwitch?: boolean
    statusField?: string
    statusActiveValue?: unknown
    statusInactiveValue?: unknown
    emptyHeight?: string | number

    // 性能配置
    performance?: PerformanceConfig

    // 响应式配置
    responsive?: ResponsiveConfig

    // 分页配置
    paginationKey?: {
      current?: string
      size?: string
      orderBy?: string
      order?: string
    }
  }

  interface Props<T = Record<string, unknown>, K = string | number> {
    config: CrudTableConfig<T, K>
  }

  const props = defineProps<Props>()

  // ==================== 工具函数 ====================

  // XSS 防护 - HTML 清理
  const sanitizeHtml = (html: string): string => {
    if (!props.config.security?.enableXssProtection) return html

    const allowedTags = props.config.security.allowedTags || [
      'p',
      'br',
      'strong',
      'em',
      'u',
      'span'
    ]

    // 简单的 HTML 标签清理（生产环境建议使用 DOMPurify）
    return html.replace(/<[^>]*>/g, (tag) => {
      const tagName = tag.match(/<\/?(\w+)/)?.[1]?.toLowerCase()
      if (!tagName || !allowedTags.includes(tagName)) return ''
      return tag
    })
  }

  // 格式化日期
  const formatDate = (date: unknown, format = 'YYYY-MM-DD HH:mm:ss'): string => {
    if (!date) return ''
    const d = new Date(date as string)
    if (isNaN(d.getTime())) return ''

    const year = d.getFullYear().toString()
    const month = String(d.getMonth() + 1).padStart(2, '0')
    const day = String(d.getDate()).padStart(2, '0')
    const hours = String(d.getHours()).padStart(2, '0')
    const minutes = String(d.getMinutes()).padStart(2, '0')
    const seconds = String(d.getSeconds()).padStart(2, '0')

    return format
      .replace('YYYY', year)
      .replace('MM', month)
      .replace('DD', day)
      .replace('HH', hours)
      .replace('mm', minutes)
      .replace('ss', seconds)
  }

  // 智能标签着色
  const getTagType = (
    value: string,
    options?: Array<{ label: string; value: unknown; color?: string }>
  ): 'primary' | 'success' | 'warning' | 'danger' | 'info' => {
    if (!value) return 'info'

    if (options) {
      const option = options.find((opt) => opt.value === value)
      if (option?.color)
        return option.color as 'primary' | 'success' | 'warning' | 'danger' | 'info'
    }

    let hash = 0
    for (let i = 0; i < value.length; i++) {
      const char = value.charCodeAt(i)
      hash = (hash << 5) - hash + char
      hash = hash & hash
    }

    const types: ('primary' | 'success' | 'warning' | 'danger' | 'info')[] = [
      'primary',
      'success',
      'warning',
      'danger',
      'info'
    ]
    return types[Math.abs(hash) % types.length]
  }

  // 复制到剪贴板
  const handleCopy = async (text: string): Promise<void> => {
    try {
      await navigator.clipboard.writeText(text || '')
      ElMessage.success('复制成功')
    } catch {
      ElMessage.error('复制失败，请手动复制')
    }
  }

  // ==================== 响应式状态 ====================

  const { width } = useWindowSize()
  const isMobile = computed(
    () => width.value < (props.config.responsive?.mobile?.breakpoint || 768)
  )

  const dialogType = ref<'add' | 'edit'>('add')
  const dialogVisible = ref(false)
  const currentRow = ref<Record<string, unknown> | null>(null)
  const formFilters = reactive({ ...props.config.initialSearchState })
  const dialogFormData = ref<Record<string, unknown>>({})
  const submitLoading = ref(false)
  const artTableRef = ref<InstanceType<typeof ArtTable>>()

  // 计算属性
  const showAddButton = computed(() => {
    const hasPermission =
      props.config.permissions?.create !== false &&
      (typeof props.config.permissions?.create === 'function'
        ? props.config.permissions.create()
        : true)
    return (
      !props.config.hideAddButton &&
      props.config.dialog &&
      props.config.apiService.create &&
      hasPermission
    )
  })

  const showDeleteButton = computed(() => {
    const hasPermission =
      props.config.permissions?.batchDelete !== false &&
      (typeof props.config.permissions?.batchDelete === 'function'
        ? props.config.permissions.batchDelete()
        : true)
    return (
      !props.config.hideDeleteButton && props.config.allowBatchDelete !== false && hasPermission
    )
  })

  // 响应式列配置
  const responsiveColumns = computed(() => {
    if (!isMobile.value || !props.config.responsive?.mobile?.hideColumns) {
      return props.config.tableColumns
    }

    return props.config.tableColumns.filter(
      (col) => !props.config.responsive?.mobile?.hideColumns?.includes(col.prop || '')
    )
  })

  // ==================== useTable Hook 集成 ====================

  const {
    data: tableData,
    loading: isLoading,
    pagination,
    handleSizeChange: onPageSizeChange,
    handleCurrentChange: onCurrentPageChange,
    searchParams: searchState,
    resetSearchParams: resetSearch,
    getData: searchData,
    getDataDebounced: searchDataDebounced,
    clearData,
    refreshData: refreshAll,
    refreshSoft,
    refreshCreate: refreshAfterCreate,
    refreshUpdate: refreshAfterUpdate,
    refreshRemove: refreshAfterRemove,
    clearCache: invalidateCache,
    clearExpiredCache,
    columns,
    columnChecks
  } = useTable<Record<string, unknown>>({
    core: {
      apiFn: props.config.apiService.getList,
      apiParams: (() => {
        // 🔧 使用动态的分页键名，避免参数重复
        const paginationKey = props.config.paginationKey || {}
        const currentKey = paginationKey.current || 'current'
        const sizeKey = paginationKey.size || 'size'

        const params: Record<string, unknown> = {
          [currentKey]: 1,
          [sizeKey]: props.config.pageSize || 20,
          ...props.config.initialSearchState
        }

        return params
      })(),
      immediate: true,
      columnsFactory: () =>
        responsiveColumns.value.map((column) => ({
          ...column,
          useSlot: !!column.slotName || !!column.slot || column.useSlot,
          slotName: column.slotName || column.slot,
          checked: true
        })),
      paginationKey: props.config.paginationKey
    },

    performance: {
      enableCache: props.config.performance?.enableCache ?? false,
      cacheTime: props.config.performance?.cacheTime ?? 5 * 60 * 1000,
      debounceTime: props.config.performance?.debounceTime ?? 300,
      maxCacheSize: props.config.performance?.maxCacheSize ?? 100
    },

    hooks: {
      onSuccess: (data: Record<string, unknown>[]) => {
        console.log(`[ArtCrudTable] 数据加载成功: ${data.length} 条`)
      },
      onError: (error: { message?: string }) => {
        ElMessage.error(error.message || '数据加载失败')
      },
      onCacheHit: (data: Record<string, unknown>[]) => {
        console.log(`[ArtCrudTable] 缓存命中: ${data.length} 条`)
      }
    },

    debug: {
      enableLog: true,
      logLevel: 'info'
    }
  })

  // ==================== 事件处理函数 ====================

  // 表单重置
  const handleReset = (): void => {
    Object.assign(formFilters, { ...props.config.initialSearchState })
    resetSearch()
  }

  // 搜索处理
  const handleSearch = (): void => {
    Object.assign(searchState, formFilters)
    searchData()
  }

  // 刷新处理
  const handleRefresh = (): void => {
    refreshAll()
  }

  // 状态切换
  const handleStatusChange = async (
    row: Record<string, unknown>,
    newStatus: boolean
  ): Promise<void> => {
    if (!props.config.apiService.update) return

    const statusField = (props.config.statusField as string) || 'status'
    const statusActiveValue = props.config.statusActiveValue ?? 1
    const statusInactiveValue = props.config.statusInactiveValue ?? 0

    const statusValue = newStatus ? statusActiveValue : statusInactiveValue
    const statusText = newStatus ? '启用' : '禁用'

    try {
      await props.config.apiService.update(
        row.id as string | number,
        { [statusField]: statusValue } as Partial<Record<string, unknown>>
      )
      ElMessage.success(`${statusText}成功`)
      row[statusField] = statusValue
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : `${statusText}失败`
      ElMessage.error(errorMsg)
      row[statusField] = newStatus ? statusInactiveValue : statusActiveValue
    }
  }

  // 开关状态变化处理
  const handleToggleChange = async (
    row: Record<string, unknown>,
    prop: string,
    value: unknown
  ): Promise<void> => {
    if (!props.config.apiService.update) return

    try {
      await props.config.apiService.update(
        row.id as string | number,
        { [prop]: value } as Partial<Record<string, unknown>>
      )
      ElMessage.success('状态已更新')
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : '状态更新失败'
      ElMessage.error(errorMsg)
      row[prop] = value === 1 ? 0 : 1
    }
  }

  // 评分点击处理
  const handleRatingClick = async (
    row: Record<string, unknown>,
    prop: string,
    rating: number
  ): Promise<void> => {
    if (!props.config.apiService.update) return

    try {
      await props.config.apiService.update(
        row.id as string | number,
        { [prop]: rating } as Partial<Record<string, unknown>>
      )
      row[prop] = rating
      ElMessage.success(`评分已更新为: ${rating}星`)
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : '评分更新失败'
      ElMessage.error(errorMsg)
    }
  }

  // ==================== 批量操作 ====================

  // 批量删除
  const handleDelete = (): void => {
    // 🔧 直接从 ElTable 获取选中行数据，避免依赖事件传递
    const selection = artTableRef.value?.elTableRef?.getSelectionRows() || []
    if (selection.length === 0) {
      ElMessage.warning(`请选择要删除的${props.config.title}`)
      return
    }

    const confirmText = (
      props.config.batchDeleteConfirmText || '确认删除选中的 {count} 条记录吗？'
    ).replace('{count}', selection.length.toString())

    ElMessageBox.confirm(confirmText, `删除${props.config.title}`, {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async () => {
      try {
        if (!props.config.apiService.batchDelete) return
        const ids = selection.map((item) => item.id as string | number)
        await props.config.apiService.batchDelete(ids)
        ElMessage.success('删除成功')
        await refreshAfterRemove()
        // 清空选择
        artTableRef.value?.elTableRef?.clearSelection()
      } catch (error) {
        const errorMsg = error instanceof Error ? error.message : '删除失败'
        ElMessage.error(errorMsg)
      }
    })
  }

  // 执行批量操作
  const handleBatchAction = async (action: BatchAction): Promise<void> => {
    // 🔧 直接从 ElTable 获取选中行数据
    const selection = artTableRef.value?.elTableRef?.getSelectionRows() || []
    if (selection.length === 0) {
      ElMessage.warning(`请选择要${action.label}的项目`)
      return
    }

    if (action.confirm) {
      try {
        await ElMessageBox.confirm(
          action.confirm.replace('{count}', selection.length.toString()),
          action.label,
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
      } catch {
        return
      }
    }

    try {
      await action.handler(selection)
      ElMessage.success(`${action.label}成功`)
      await refreshAll()
      // 清空选择
      artTableRef.value?.elTableRef?.clearSelection()
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : `${action.label}失败`
      ElMessage.error(errorMsg)
    }
  }

  // ==================== 导出功能 ====================

  // 导出数据
  const handleExport = async (format: 'excel' | 'csv' | 'pdf' = 'excel'): Promise<void> => {
    if (!props.config.export?.enabled) return

    const hasPermission =
      props.config.permissions?.export !== false &&
      (typeof props.config.permissions?.export === 'function'
        ? props.config.permissions.export()
        : true)

    if (!hasPermission) {
      ElMessage.error('没有导出权限')
      return
    }

    try {
      const exportColumns =
        props.config.export.columns ||
        props.config.tableColumns
          .filter((col) => col.prop && !['selection', 'index'].includes(col.type || ''))
          .map((col) => col.prop as string)

      const exportData = tableData.value.map((row) => {
        const exportRow: Record<string, unknown> = {}
        exportColumns.forEach((key) => {
          exportRow[key] = row[key]
        })
        return exportRow
      })

      // 这里应该调用实际的导出服务
      console.log(`导出${format}格式数据:`, exportData)
      ElMessage.success(`${format.toUpperCase()}导出成功`)
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : '导出失败'
      ElMessage.error(errorMsg)
    }
  }

  // ==================== 对话框相关 ====================

  // 删除单个
  const deleteRow = (row: Record<string, unknown>): void => {
    const hasPermission =
      props.config.permissions?.delete !== false &&
      (typeof props.config.permissions?.delete === 'function'
        ? props.config.permissions.delete(row)
        : true)

    if (!hasPermission) {
      ElMessage.error('没有删除权限')
      return
    }

    const confirmText = props.config.deleteConfirmText || '确认删除这条记录吗？'

    ElMessageBox.confirm(confirmText, `删除${props.config.title}`, {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async () => {
      try {
        if (!props.config.apiService.delete) return
        await props.config.apiService.delete(row.id as string | number)
        ElMessage.success('删除成功')
        await refreshAfterRemove()
      } catch (error) {
        const errorMsg = error instanceof Error ? error.message : '删除失败'
        ElMessage.error(errorMsg)
      }
    })
  }

  // 显示对话框
  const showDialog = (type: string, row?: Record<string, unknown>): void => {
    if (!props.config.dialog) return

    const isEdit = type === 'edit'
    const hasPermission = isEdit
      ? props.config.permissions?.update !== false &&
        (typeof props.config.permissions?.update === 'function'
          ? props.config.permissions.update(row!)
          : true)
      : props.config.permissions?.create !== false &&
        (typeof props.config.permissions?.create === 'function'
          ? props.config.permissions.create()
          : true)

    if (!hasPermission) {
      ElMessage.error(`没有${isEdit ? '编辑' : '新增'}权限`)
      return
    }

    dialogVisible.value = true
    dialogType.value = type as 'add' | 'edit'
    currentRow.value = row || null

    // 初始化对话框表单数据
    if (type === 'edit' && row) {
      Object.keys(dialogFormData.value).forEach((key) => {
        if (row[key] !== undefined) {
          dialogFormData.value[key] = row[key]
        }
      })
    }
  }

  // 对话框提交
  const handleDialogSubmit = async (data: {
    formData: Record<string, unknown>
    type: 'add' | 'edit'
  }): Promise<void> => {
    submitLoading.value = true
    try {
      const { formData, type } = data

      if (type === 'add') {
        if (props.config.apiService.create) {
          await props.config.apiService.create(formData)
          ElMessage.success('添加成功')
          refreshAfterCreate()
        }
      } else {
        if (props.config.apiService.update && currentRow.value) {
          await props.config.apiService.update(
            currentRow.value.id as string | number,
            formData as Partial<Record<string, unknown>>
          )
          ElMessage.success('更新成功')
          refreshAfterUpdate()
        }
      }
      dialogVisible.value = false
    } catch (error) {
      const errorMsg =
        error instanceof Error ? error.message : `${data.type === 'add' ? '添加' : '更新'}失败`
      ElMessage.error(errorMsg)
    } finally {
      submitLoading.value = false
    }
  }

  // 对话框取消
  const handleDialogCancel = (): void => {
    dialogVisible.value = false
  }

  // 初始化同步搜索状态
  onMounted(() => {
    // 同步搜索状态
    Object.assign(searchState, formFilters)

    // 🔧 不需要手动调用 searchData()，useTable 的 immediate: true 已经会自动发起请求
    console.log('[ArtCrudTable] 组件挂载完成，useTable 将自动加载数据')
  })

  // 暴露方法给父组件
  defineExpose({
    handleStatusChange,
    deleteRow,
    showDialog,
    refreshAll,
    handleRefresh,
    searchData,
    searchDataDebounced,
    refreshSoft,
    refreshAfterCreate,
    refreshAfterUpdate,
    refreshAfterRemove,
    invalidateCache,
    clearExpiredCache,
    clearData,
    handleExport,
    handleBatchAction
  })
</script>

<template>
  <div class="art-crud-table">
    <!-- 搜索栏 -->
    <ArtSearchBar
      v-if="config.searchFormItems && config.searchFormItems.length > 0"
      v-model:filter="formFilters"
      :items="config.searchFormItems"
      @reset="handleReset"
      @search="handleSearch"
    />

    <!-- 表格卡片 -->
    <ElCard shadow="never" class="art-table-card">
      <!-- 表格头部 -->
      <ArtTableHeader v-model:columns="columnChecks" @refresh="handleRefresh">
        <template #left>
          <ElButton v-if="showAddButton" type="primary" @click="showDialog('add')">
            {{ config.addButtonText || `新增${config.title}` }}
          </ElButton>
          <ElButton
            v-if="showDeleteButton && config.apiService.batchDelete"
            type="danger"
            @click="handleDelete"
          >
            {{ config.deleteButtonText || `删除${config.title}` }}
          </ElButton>
        </template>
      </ArtTableHeader>

      <!-- 表格 -->
      <ArtTable
        ref="artTableRef"
        :row-key="config.rowKey || 'id'"
        :loading="isLoading"
        :data="tableData"
        :config="{
          emptyHeight: config.emptyHeight || '400px'
        }"
        :pagination="{
          current: pagination.current,
          size: pagination.size,
          total: pagination.total
        }"
        :layout="{ marginTop: 10 }"
        @pagination:size-change="onPageSizeChange"
        @pagination:current-change="onCurrentPageChange"
      >
        <template #default>
          <!-- 动态渲染表格列 -->
          <template v-for="column in columns" :key="column.prop || column.type">
            <ElTableColumn
              v-if="column.type"
              :type="column.type"
              :width="column.width"
              :label="column.label"
            />
            <!-- 图片列 -->
            <ElTableColumn
              v-else-if="(column as any).columnType === 'image'"
              :label="column.label"
              :prop="column.prop"
              :width="column.width"
              :min-width="column.minWidth"
              :fixed="column.fixed"
              :sortable="column.sortable"
              :show-overflow-tooltip="column.showOverflowTooltip"
            >
              <template #default="{ row }">
                <el-image
                  :src="row[column.prop || ''] || '/static/avatar/default_avatar.png'"
                  :alt="column.label"
                  style="width: 50px; height: 50px; object-fit: cover; border-radius: 4px"
                  fit="cover"
                  :preview-src-list="[row[column.prop || '']]"
                  loading="lazy"
                  preview-teleported
                >
                  <template #error>
                    <div
                      style="
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        width: 50px;
                        height: 50px;
                        font-size: 12px;
                        color: #999;
                        background: #f5f5f5;
                        border: 1px solid #e0e0e0;
                        border-radius: 4px;
                      "
                    >
                      加载失败
                    </div>
                  </template>
                </el-image>
              </template>
            </ElTableColumn>
            <!-- 开关列 -->
            <ElTableColumn
              v-else-if="(column as any).columnType === 'toggle'"
              :label="column.label"
              :prop="column.prop"
              :width="column.width"
              :min-width="column.minWidth"
              :fixed="column.fixed"
              :sortable="column.sortable"
              :show-overflow-tooltip="column.showOverflowTooltip"
            >
              <template #default="{ row }">
                <el-switch
                  v-model="row[column.prop || '']"
                  inline-prompt
                  :active-value="1"
                  :inactive-value="0"
                  active-text="启用"
                  inactive-text="禁用"
                  @change="handleToggleChange(row, column.prop || '', $event)"
                />
              </template>
            </ElTableColumn>
            <!-- 标签列 -->
            <ElTableColumn
              v-else-if="(column as any).columnType === 'tag'"
              :label="column.label"
              :prop="column.prop"
              :width="column.width"
              :min-width="column.minWidth"
              :fixed="column.fixed"
              :sortable="column.sortable"
              :show-overflow-tooltip="column.showOverflowTooltip"
            >
              <template #default="{ row }">
                <el-tag
                  :type="getTagType(row[column.prop || ''] || '', (column as any).options) as any"
                  size="small"
                  effect="light"
                >
                  {{ row[column.prop || ''] || '-' }}
                </el-tag>
              </template>
            </ElTableColumn>
            <!-- 复制列 -->
            <ElTableColumn
              v-else-if="(column as any).columnType === 'copy'"
              :label="column.label"
              :prop="column.prop"
              :width="column.width"
              :min-width="column.minWidth"
              :fixed="column.fixed"
              :sortable="column.sortable"
              :show-overflow-tooltip="column.showOverflowTooltip"
            >
              <template #default="{ row }">
                <div style="display: flex; gap: 8px; align-items: center">
                  <span style="flex: 1; word-break: break-all">{{
                    row[column.prop || ''] || '-'
                  }}</span>
                  <el-button
                    type="primary"
                    link
                    size="small"
                    @click="handleCopy(row[column.prop || ''])"
                  >
                    复制
                  </el-button>
                </div>
              </template>
            </ElTableColumn>
            <!-- 链接列 -->
            <ElTableColumn
              v-else-if="(column as any).columnType === 'link'"
              :label="column.label"
              :prop="column.prop"
              :width="column.width"
              :min-width="column.minWidth"
              :fixed="column.fixed"
              :sortable="column.sortable"
              :show-overflow-tooltip="column.showOverflowTooltip"
            >
              <template #default="{ row }">
                <div style="display: flex; gap: 8px; align-items: center">
                  <el-tag type="primary" size="small" effect="light">
                    <a
                      :href="row[column.prop || '']"
                      target="_blank"
                      rel="noopener noreferrer"
                      style="color: inherit; text-decoration: none"
                    >
                      {{ row[column.prop || ''] || '-' }}
                    </a>
                  </el-tag>
                  <el-button
                    type="primary"
                    link
                    size="small"
                    @click="handleCopy(row[column.prop || ''])"
                  >
                    复制
                  </el-button>
                </div>
              </template>
            </ElTableColumn>
            <!-- 评分列 -->
            <ElTableColumn
              v-else-if="(column as any).columnType === 'rating'"
              :label="column.label"
              :prop="column.prop"
              :width="column.width"
              :min-width="column.minWidth"
              :fixed="column.fixed"
              :sortable="column.sortable"
              :show-overflow-tooltip="column.showOverflowTooltip"
            >
              <template #default="{ row }">
                <div style="display: flex; gap: 8px; align-items: center">
                  <div style="display: flex; align-items: center">
                    <span
                      v-for="i in 5"
                      :key="i"
                      :style="{
                        color:
                          i <= ((row[column.prop || ''] as number) || 0) ? '#f7ba2a' : '#c6d1de',
                        fontSize: '16px',
                        cursor: 'pointer'
                      }"
                      @click="handleRatingClick(row, column.prop || '', i)"
                    >
                      ★
                    </span>
                  </div>
                  <span style="font-size: 12px; color: #666">
                    {{ (row[column.prop || ''] as number) || 0 }}/5
                  </span>
                </div>
              </template>
            </ElTableColumn>
            <!-- 日期列 -->
            <ElTableColumn
              v-else-if="(column as any).columnType === 'date'"
              :label="column.label"
              :prop="column.prop"
              :width="column.width"
              :min-width="column.minWidth"
              :fixed="column.fixed"
              :sortable="column.sortable"
              :show-overflow-tooltip="column.showOverflowTooltip"
            >
              <template #default="{ row }">
                {{ formatDate(row[column.prop || '']) }}
              </template>
            </ElTableColumn>
            <!-- HTML 列 -->
            <ElTableColumn
              v-else-if="(column as any).columnType === 'html'"
              :label="column.label"
              :prop="column.prop"
              :width="column.width"
              :min-width="column.minWidth"
              :fixed="column.fixed"
              :sortable="column.sortable"
              :show-overflow-tooltip="column.showOverflowTooltip"
            >
              <template #default="{ row }">
                <div
                  v-html="sanitizeHtml((row[column.prop || ''] as string) || '')"
                  style="max-width: 200px; word-break: break-all"
                ></div>
              </template>
            </ElTableColumn>
            <!-- 多行文本列 -->
            <ElTableColumn
              v-else-if="(column as any).columnType === 'rows'"
              :label="column.label"
              :prop="column.prop"
              :width="column.width"
              :min-width="column.minWidth"
              :fixed="column.fixed"
              :sortable="column.sortable"
              :show-overflow-tooltip="column.showOverflowTooltip"
            >
              <template #default="{ row }">
                <div
                  style="
                    max-width: 200px;
                    line-height: 1.4;
                    word-break: break-all;
                    white-space: pre-wrap;
                  "
                >
                  {{ row[column.prop || ''] || '-' }}
                </div>
              </template>
            </ElTableColumn>
            <!-- 复选框列 -->
            <ElTableColumn
              v-else-if="(column as any).columnType === 'checkbox'"
              :label="column.label"
              :prop="column.prop"
              :width="column.width"
              :min-width="column.minWidth"
              :fixed="column.fixed"
              :sortable="column.sortable"
              :show-overflow-tooltip="column.showOverflowTooltip"
            >
              <template #default="{ row }">
                <el-tag
                  :type="row[column.prop || ''] ? 'success' : 'info'"
                  size="small"
                  effect="light"
                >
                  {{ row[column.prop || ''] ? '是' : '否' }}
                </el-tag>
              </template>
            </ElTableColumn>
            <!-- 自定义插槽列 -->
            <ElTableColumn
              v-else-if="column.useSlot"
              :label="column.label"
              :prop="column.prop"
              :width="column.width"
              :min-width="column.minWidth"
              :fixed="column.fixed"
              :sortable="column.sortable"
              :show-overflow-tooltip="column.showOverflowTooltip"
            >
              <template #default="scope">
                <slot
                  :name="column.slotName"
                  :row="scope.row"
                  :column="column"
                  :index="scope.$index"
                />
              </template>
            </ElTableColumn>
            <!-- 格式化列 -->
            <ElTableColumn
              v-else-if="column.formatter"
              :label="column.label"
              :prop="column.prop"
              :width="column.width"
              :min-width="column.minWidth"
              :fixed="column.fixed"
              :sortable="column.sortable"
              :show-overflow-tooltip="column.showOverflowTooltip"
            >
              <template #default="scope">
                {{ column.formatter(scope.row) }}
              </template>
            </ElTableColumn>
            <!-- 默认文本列 -->
            <ElTableColumn
              v-else
              :label="column.label"
              :prop="column.prop"
              :width="column.width"
              :min-width="column.minWidth"
              :fixed="column.fixed"
              :sortable="column.sortable"
              :show-overflow-tooltip="column.showOverflowTooltip"
            />
          </template>
        </template>
      </ArtTable>

      <!-- 对话框 -->
      <ArtCrudDialog
        v-if="config.dialog"
        :visible="dialogVisible"
        :type="dialogType"
        :dialog-config="config.dialog"
        :current-row="currentRow"
        :loading="submitLoading"
        :entity-name="config.title"
        @update:visible="(val) => (dialogVisible = val)"
        @submit="handleDialogSubmit"
        @cancel="handleDialogCancel"
      />
    </ElCard>
  </div>
</template>

<style lang="scss" scoped>
  .art-crud-table {
    .art-table-card {
      .el-card__body {
        padding: 20px;
      }
    }

    .dialog-footer {
      display: flex;
      gap: 10px;
      justify-content: flex-end;
    }

    // 确保空数据状态优化
    :deep(.el-table__empty-block) {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 400px;
    }

    // 列设置样式优化
    :deep(.column-option) {
      display: flex;
      align-items: center;
      padding: 8px 12px;
      margin-bottom: 4px;
      cursor: move;
      background-color: var(--el-fill-color-light);
      border-radius: 4px;
      transition: all 0.2s;

      &:hover {
        background-color: var(--el-fill-color);
      }

      .drag-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 18px;
        height: 18px;
        margin-right: 8px;
        color: var(--el-text-color-secondary);

        i {
          font-size: 14px;
        }
      }

      .el-checkbox {
        flex: 1;
      }
    }
  }
</style>
