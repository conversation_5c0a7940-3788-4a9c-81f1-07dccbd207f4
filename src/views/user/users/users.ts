import { UserService } from '@/api/usersApi'
import type {
  CrudTableConfig,
  PermissionConfig,
  SecurityConfig,
  PerformanceConfig,
  ResponsiveConfig
} from '@/components/core/tables/ArtCrudTable.vue'
import { filterEmptyParams } from '@/utils'

// 包装API服务，添加参数过滤
const createUserApiService = () => ({
  getList: (params: any) => {
    // 过滤空值参数，解决URL中出现空值的问题
    const filteredParams = filterEmptyParams(params)
    return UserService.getUserList(filteredParams)
  },
  delete: (id: any) => UserService.deleteUser(id),
  batchDelete: (ids: any[]) => UserService.batchDeleteUsers(ids),
  update: (id: any, data: any) => UserService.updateUser(id, data),
  create: (data: any) => UserService.createUser(data)
})

// 性别文本转换
export const getGenderText = (gender: number) => {
  const genderMap: Record<number, string> = {
    0: '保密',
    1: '男',
    2: '女'
  }
  return genderMap[gender] || '保密'
}

// 用户类型文本转换
export const getTypeText = (type: number) => {
  const typeMap: Record<number, string> = {
    1: '虚拟用户',
    2: '普通用户'
  }
  return typeMap[type] || '普通用户'
}

// 用户状态文本转换
export const getStatusText = (status: number) => {
  const statusMap: Record<number, string> = {
    1: '激活',
    2: '禁用',
    3: '冻结',
    4: '锁定'
  }
  return statusMap[status] || '激活'
}

// 权限配置
const permissionConfig: PermissionConfig = {
  create: () => {
    return true
  },
  update: (row) => {
    // 管理员账户不允许编辑
    return (row.username as string) !== 'admin'
  },
  delete: (row) => {
    // 管理员账户不允许删除
    return (row.username as string) !== 'admin'
  },
  export: () => true,
  batchDelete: () => true
}

// 响应式配置
const responsiveConfig: ResponsiveConfig = {
  mobile: {
    hideColumns: ['telephone', 'score', 'integral', 'gender', 'created_at'],
    cardView: false,
    breakpoint: 768
  }
}

// 安全配置
const securityConfig: SecurityConfig = {
  enableXssProtection: true,
  allowedTags: ['p', 'br', 'strong', 'em'],
  allowedAttributes: {
    div: ['class'],
    span: ['class']
  }
}

// 性能配置
const performanceConfig: PerformanceConfig = {
  enableCache: true,
  cacheTime: 3 * 60 * 1000, // 3分钟
  debounceTime: 500,
  maxCacheSize: 50,
  lazyLoad: true,
  virtualScroll: {
    enabled: true, // 用户数据可能较多，启用虚拟滚动
    itemHeight: 60,
    buffer: 10
  }
}

// 用户管理配置
export const userTableConfig: CrudTableConfig = {
  title: '用户',
  addButtonText: '新增用户',
  deleteButtonText: '删除用户',
  deleteConfirmText: '确定要删除该用户吗？',
  batchDeleteConfirmText: '确定要删除选中的 {count} 条用户吗？',

  initialSearchState: {
    username: '',
    nickname: '',
    email: '',
    telephone: '',
    gender: '',
    type: '',
    status: '',
    startTime: '',
    endTime: ''
  },

  searchFormItems: [
    {
      key: 'username',
      label: '用户名',
      type: 'input',
      placeholder: '请输入用户名'
    },
    {
      key: 'nickname',
      label: '昵称',
      type: 'input',
      placeholder: '请输入昵称'
    },
    {
      key: 'email',
      label: '邮箱',
      type: 'input',
      placeholder: '请输入邮箱'
    },
    {
      key: 'telephone',
      label: '手机号',
      type: 'input',
      placeholder: '请输入手机号'
    },
    {
      key: 'gender',
      label: '性别',
      type: 'select',
      placeholder: '请选择性别',
      options: [
        { label: '保密', value: '0' },
        { label: '男', value: '1' },
        { label: '女', value: '2' }
      ]
    },
    {
      key: 'type',
      label: '用户类型',
      type: 'select',
      placeholder: '请选择用户类型',
      options: [
        { label: '虚拟用户', value: '1' },
        { label: '普通用户', value: '2' }
      ]
    },
    {
      key: 'status',
      label: '状态',
      type: 'select',
      placeholder: '请选择状态',
      options: [
        { label: '激活', value: '1' },
        { label: '禁用', value: '2' },
        { label: '冻结', value: '3' },
        { label: '锁定', value: '4' }
      ]
    }
  ],

  tableColumns: [
    { type: 'selection', width: 55 },
    { label: 'ID', prop: 'id', width: 80, sortable: true },
    {
      label: '头像',
      prop: 'avatar',
      width: 80,
      columnType: 'image',
      useSlot: true,
      slotName: 'avatar'
    },
    {
      label: '用户名',
      prop: 'username',
      minWidth: 120
    },
    { label: '昵称', prop: 'nickname', minWidth: 120 },
    {
      label: '邮箱',
      prop: 'email',
      minWidth: 180,
      showOverflowTooltip: true
    },
    {
      label: '手机号',
      prop: 'telephone',
      minWidth: 120
    },
    {
      label: '性别',
      prop: 'gender',
      width: 80,
      useSlot: true,
      slotName: 'gender'
    },
    {
      label: '用户类型',
      prop: 'type',
      width: 100,
      useSlot: true,
      slotName: 'type'
    },
    {
      label: '可用金额',
      prop: 'available_amount',
      width: 120,
      columnType: 'currency'
    },
    {
      label: '冻结金额',
      prop: 'frozen_amount',
      width: 120,
      columnType: 'currency'
    },
    {
      label: '状态',
      prop: 'status',
      width: 100,
      columnType: 'toggle',
      useSlot: true,
      slotName: 'status'
    },
    {
      label: '最后登录时间',
      prop: 'last_login_at',
      minWidth: 160,
      sortable: true,
      columnType: 'date'
    },
    {
      label: '创建时间',
      prop: 'created_at',
      minWidth: 160,
      sortable: true,
      columnType: 'date'
    },
    {
      label: '操作',
      width: 200,
      fixed: 'right',
      useSlot: true,
      slotName: 'actions'
    }
  ],

  apiService: createUserApiService(),

  // 分页参数映射
  paginationKey: {
    current: 'page',
    size: 'pageSize'
  },

  dialog: {
    title: {
      add: '添加用户',
      edit: '编辑用户'
    },
    width: '800px',
    formItems: [
      {
        label: '用户名',
        prop: 'username',
        type: 'input',
        required: true,
        config: {
          placeholder: '请输入用户名',
          maxlength: 60
        },
        rules: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { max: 60, message: '用户名长度不能超过 60 个字符', trigger: 'blur' }
        ],
        span: 12,
        show: (formData, type) => type === 'add'
      },
      {
        label: '密码',
        prop: 'password',
        type: 'input',
        required: true,
        config: {
          placeholder: '请输入密码',
          minlength: 6
        },
        rules: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 6, message: '密码长度不能少于 6 个字符', trigger: 'blur' }
        ],
        span: 12,
        show: (formData, type) => type === 'add'
      },
      {
        label: '昵称',
        prop: 'nickname',
        type: 'input',
        config: {
          placeholder: '请输入昵称',
          maxlength: 50
        },
        rules: [{ max: 50, message: '昵称长度不能超过 50 个字符', trigger: 'blur' }],
        span: 12,
        show: (formData, type) => type === 'edit'
      },
      {
        label: '邮箱',
        prop: 'email',
        type: 'input',
        config: {
          placeholder: '请输入邮箱',
          maxlength: 100
        },
        rules: [
          { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' },
          { max: 100, message: '邮箱长度不能超过 100 个字符', trigger: 'blur' }
        ],
        span: 12,
        show: (formData, type) => type === 'edit'
      },
      {
        label: '手机号',
        prop: 'telephone',
        type: 'input',
        config: {
          placeholder: '请输入手机号',
          maxlength: 20
        },
        rules: [{ pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' }],
        span: 12,
        show: (formData, type) => type === 'edit'
      },
      {
        label: '性别',
        prop: 'gender',
        type: 'select',
        options: [
          { label: '保密', value: 0 },
          { label: '男', value: 1 },
          { label: '女', value: 2 }
        ],
        span: 12,
        show: (formData, type) => type === 'edit'
      },
      {
        label: '用户类型',
        prop: 'type',
        type: 'select',
        required: true,
        options: [
          { label: '虚拟用户', value: 1 },
          { label: '普通用户', value: 2 }
        ],
        span: 12
      },
      {
        label: '状态',
        prop: 'status',
        type: 'select',
        required: true,
        options: [
          { label: '激活', value: 1 },
          { label: '禁用', value: 2 },
          { label: '冻结', value: 3 },
          { label: '锁定', value: 4 }
        ],
        span: 12,
        show: (formData, type) => type === 'edit'
      },
      {
        label: '头像',
        prop: 'avatar',
        type: 'upload',
        config: {
          accept: 'image/*',
          maxSize: 5,
          triggerText: '选择头像',
          tip: '支持 JPG、PNG 格式，文件大小不超过 5MB'
        },
        span: 24,
        show: (formData, type) => type === 'edit'
      },
      {
        label: '详情描述',
        prop: 'description',
        type: 'textarea',
        config: {
          placeholder: '请输入用户详情描述',
          maxlength: 255,
          rows: 4
        },
        rules: [{ max: 255, message: '详情描述长度不能超过 255 个字符', trigger: 'blur' }],
        span: 24,
        show: (formData, type) => type === 'edit'
      }
    ],
    initialFormData: {
      username: '',
      password: '',
      nickname: '',
      email: '',
      telephone: '',
      avatar: '',
      gender: 0,
      type: 2,
      status: 1,
      description: ''
    }
  },

  // 功能配置
  permissions: permissionConfig,
  responsive: responsiveConfig,
  security: securityConfig,
  performance: performanceConfig,

  // 状态切换配置
  hasStatusSwitch: true,
  statusField: 'status',
  statusActiveValue: 1,
  statusInactiveValue: 2,

  // 操作权限
  allowBatchDelete: true
}
